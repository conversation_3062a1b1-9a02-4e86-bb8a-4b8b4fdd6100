import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/features/systemConfig/model/answer_mode.dart';
import 'package:flutterquiz/ui/widgets/customAppbar.dart';
import 'package:flutterquiz/utils/awesome_notification_manager.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/reminder_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingScreen extends StatefulWidget {
  const SettingScreen({super.key});

  static Route<SettingScreen> route(RouteSettings settings) {
    return CupertinoPageRoute(builder: (_) => const SettingScreen());
  }

  @override
  State<SettingScreen> createState() => _SettingScreenState();
}

class _SettingScreenState extends State<SettingScreen> {
  SystemConfigCubit get _systemConfigCubit => context.read<SystemConfigCubit>();

  AnswerMode get _answerMode => _systemConfigCubit.answerMode;

  bool dailyReminder = false;
  DateTime? _selectedDate;

  ReminderManager reminderManager = ReminderManager();

  String reminderTime = '';

  @override
  void initState() {
    super.initState();
    reminderManager.getReminderStatus().then((value) {
      setState(() {
        dailyReminder = value ?? false;
      });
    });

    reminderManager.getReminderTime().then((value) {
      if (value != null) {
        setState(() {
          _selectedDate = value;
          reminderTime = '${value.hour}:${value.minute}';
        });
      }
    });

    if (_answerMode == AnswerMode.noAnswerCorrectness) {
      answerMode = 'بدون عرض الاجابات';
    } else if (_answerMode == AnswerMode.showAnswerCorrectness) {
      answerMode = 'بدون تصحيح الاجابة';
    } else if (_answerMode ==
        AnswerMode.showAnswerCorrectnessAndCorrectAnswer) {
      answerMode = 'تصحيح الاجابة';
    }
  }

  String answerMode = '';
  List<String> list = [
    'تصحيح الاجابة',
    'بدون تصحيح الاجابة',
    'بدون عرض الاجابات'
  ];

  List<String> messages = [
    'افتح تطبيق مجتهد وارتقِ بمهاراتك للأعلى! 🦅✨',
    'تبغى التميز؟ مجتهد هو طريقك للنجاح المستمر! 🚀🔥',
    'اللي يبغى القمة يبدأ مع مجتهد! افتح التطبيق وابدأ الآن.',
    'مراجعتك اليوم هي نجاحك غدًا! افتح مجتهد وابدأ رحلتك.',
    'ابدأ يومك مع مجتهد، وارفع من مستواك بكل اختبار.',
    'إذا هدفك التميز، مجتهد هو طريقك للأفضل في كل اختبار.',
    'ما تحتاج تنتظر! افتح مجتهد الآن واستثمر في قدراتك. 👏😉',
    'ما عندك وقت تضيعه؟ افتح مجتهد وخليك مستعد لأي تحدٍ جديد!',
    'تحب النجاح؟ افتح مجتهد وابدأ طريقك بخطوات ثابتة.',
    'مع مجتهد، كل يوم فرصة جديدة للتطور! افتح التطبيق الآن وابدأ!'
  ];

  String getRandomMessage() {
    var random = Random();
    int index = random.nextInt(messages.length);
    return messages[index];
  }

  String formatTimeToArabic(DateTime dateTime) {
    var hour = dateTime.hour;
    final minute = dateTime.minute;

    final period = hour >= 12 ? 'م' : 'ص';
    hour = hour % 12 == 0 ? 12 : hour % 12;
    final minuteString = minute.toString().padLeft(2, '0');

    return '$hour:$minuteString $period';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: QAppBar(
        title: Text(context.tr('settingLbl')!),
      ),
      body: Container(
        // إضافة حاوية رئيسية لتطبيق الخلفية المتدرجة على كامل الصفحة
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.1),
              Theme.of(context).colorScheme.background.withOpacity(0.95),
            ],
            stops: const [0.0, 0.7],
          ),
        ),
        child: BlocBuilder(
          bloc: context.read<SettingsCubit>(),
          builder: (BuildContext context, state) {
            if (state is SettingsState) {
              final settingsCubit = context.read<SettingsCubit>();
              final settings = settingsCubit.getSettings();
              final size = MediaQuery.of(context).size;
              final colorScheme = Theme.of(context).colorScheme;
              final primaryColor = Theme.of(context).primaryColor;

              return LayoutBuilder(
                builder: (context, constraints) {
                  // حساب الحجم المناسب للشاشة
                  final maxWidth = constraints.maxWidth;
                  final isTablet = maxWidth > 600;
                  final cardWidth = isTablet ? maxWidth * 0.7 : maxWidth;

                  return SingleChildScrollView(
                    padding: EdgeInsets.symmetric(
                      vertical: size.height * 0.02,
                      horizontal: isTablet
                          ? (maxWidth - cardWidth) / 2
                          : size.width * 0.04,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // عنوان القسم
                        Container(
                          margin: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: size.height * 0.02,
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(isTablet ? 16 : 12),
                                decoration: BoxDecoration(
                                  color: primaryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(15),
                                  boxShadow: [
                                    BoxShadow(
                                      color: primaryColor.withOpacity(0.1),
                                      blurRadius: 10,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.settings_suggest_rounded,
                                  color: primaryColor,
                                  size: isTablet ? 32 : 28,
                                ),
                              ),
                              SizedBox(width: isTablet ? 24 : 16),
                              Text(
                                "إعدادات التطبيق",
                                style: TextStyle(
                                  fontSize: isTablet ? 28 : 24,
                                  fontWeight: FontWeights.bold,
                                  color: colorScheme.onBackground,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // بطاقة الإعدادات
                        Container(
                          margin: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: size.height * 0.01,
                          ),
                          width: cardWidth,
                          decoration: BoxDecoration(
                            color: colorScheme.surface,
                            borderRadius: BorderRadius.circular(24),
                            boxShadow: [
                              BoxShadow(
                                color: colorScheme.shadow.withOpacity(0.08),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              // الصوت
                              _buildSettingTile(
                                icon: Icons.music_note_rounded,
                                title: "الأصوات",
                                subtitle: "تفعيل أصوات التطبيق",
                                trailing: Transform.scale(
                                  scale: 0.9,
                                  child: Switch.adaptive(
                                    value: settings.sound,
                                    onChanged: (v) => setState(() {
                                      settingsCubit.sound = v;
                                    }),
                                    activeColor: primaryColor,
                                  ),
                                ),
                                colorScheme: colorScheme,
                                primaryColor: primaryColor,
                                isFirst: true,
                              ),

                              _buildDivider(),

                              // الاهتزاز
                              _buildSettingTile(
                                icon: Icons.vibration_rounded,
                                title: "الاهتزاز",
                                subtitle: "تفعيل اهتزاز الجهاز عند الإجابة",
                                trailing: Transform.scale(
                                  scale: 0.9,
                                  child: Switch.adaptive(
                                    value: settings.vibration,
                                    onChanged: (v) => setState(() {
                                      settingsCubit.vibration = v;
                                    }),
                                    activeColor: primaryColor,
                                  ),
                                ),
                                colorScheme: colorScheme,
                                primaryColor: primaryColor,
                              ),

                              _buildDivider(),

                              // التذكير اليومي
                              _buildSettingTile(
                                icon: Icons.notifications_active_rounded,
                                title: "التذكير اليومي",
                                subtitle: _selectedDate != null
                                    ? 'موعد التذكير: ${formatTimeToArabic(_selectedDate!)}'
                                    : "اضغط هنا لتحديد موعد التذكير اليومي",
                                onTap: () async {
                                  if (!dailyReminder) {
                                    setState(() {
                                      dailyReminder = true;
                                    });
                                  }
                                  DateTime? date =
                                      await reminderManager.selectReminderTime(
                                    context,
                                    popAfter: false,
                                  );
                                  if (date != null) {
                                    setState(() {
                                      _selectedDate = DateTime.now().copyWith(
                                        hour: date.hour,
                                        minute: date.minute,
                                        second: 0,
                                      );
                                    });
                                  }
                                },
                                trailing: Transform.scale(
                                  scale: 0.9,
                                  child: Switch.adaptive(
                                    value: dailyReminder,
                                    onChanged: (v) async {
                                      setState(() {
                                        dailyReminder = v;
                                      });
                                      if (v) {
                                        // طلب إذن الإشعارات أولاً
                                        await awesomeNotificationManager
                                            .appOpenNotification();

                                        DateTime? date = await reminderManager
                                            .selectReminderTime(
                                          context,
                                          popAfter: false,
                                        );
                                        if (date != null) {
                                          setState(() {
                                            _selectedDate =
                                                DateTime.now().copyWith(
                                              hour: date.hour,
                                              minute: date.minute,
                                              second: 0,
                                            );
                                          });
                                        }
                                      } else {
                                        await awesomeNotificationManager
                                            .cancelNotificationById(
                                          id: 393,
                                        );
                                        setState(() {
                                          _selectedDate = null;
                                          dailyReminder = false;
                                        });
                                      }
                                    },
                                    activeColor: primaryColor,
                                  ),
                                ),
                                colorScheme: colorScheme,
                                primaryColor: primaryColor,
                              ),

                              _buildDivider(),

                              // نمط الإجابات
                              _buildSettingTile(
                                icon: Icons.quiz_rounded,
                                title: "نمط عرض الإجابات",
                                subtitle: "اختر طريقة عرض نتائج الإجابات",
                                trailing: Container(
                                  constraints: BoxConstraints(
                                    maxWidth:
                                        MediaQuery.of(context).size.width *
                                            0.45,
                                  ),
                                  child: Theme(
                                    data: Theme.of(context).copyWith(
                                      textButtonTheme: TextButtonThemeData(
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.zero,
                                          minimumSize: Size.zero,
                                        ),
                                      ),
                                    ),
                                    child: DropdownButton<String>(
                                      value: answerMode,
                                      icon: Icon(
                                        Icons.keyboard_arrow_down_rounded,
                                        color: primaryColor,
                                        size: 18,
                                      ),
                                      isDense: true,
                                      itemHeight: 48,
                                      underline: const SizedBox(),
                                      alignment: AlignmentDirectional.centerEnd,
                                      style: TextStyle(
                                        color: colorScheme.onBackground,
                                        fontSize: 13,
                                        fontWeight: FontWeights.medium,
                                      ),
                                      dropdownColor: colorScheme.surface,
                                      borderRadius: BorderRadius.circular(16),
                                      items: list.map<DropdownMenuItem<String>>(
                                        (String value) {
                                          return DropdownMenuItem<String>(
                                            value: value,
                                            child: SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.4,
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      value,
                                                      style: TextStyle(
                                                        color: value ==
                                                                answerMode
                                                            ? primaryColor
                                                            : colorScheme
                                                                .onBackground,
                                                        fontSize: 13,
                                                        fontWeight:
                                                            value == answerMode
                                                                ? FontWeights
                                                                    .semiBold
                                                                : FontWeights
                                                                    .regular,
                                                      ),
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                  if (value == answerMode) ...[
                                                    const SizedBox(width: 4),
                                                    Icon(
                                                      Icons.check_rounded,
                                                      color: primaryColor,
                                                      size: 16,
                                                    ),
                                                  ],
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ).toList(),
                                      onChanged: (String? newValue) async {
                                        if (newValue != null) {
                                          final shared = await SharedPreferences
                                              .getInstance();
                                          setState(() {
                                            answerMode = newValue;
                                          });

                                          if (newValue == list[0]) {
                                            await shared.setString('answerMode',
                                                'showAnswerCorrectnessAndCorrectAnswer');
                                          } else if (newValue == list[1]) {
                                            await shared.setString('answerMode',
                                                'showAnswerCorrectness');
                                          } else if (newValue == list[2]) {
                                            await shared.setString('answerMode',
                                                'noAnswerCorrectness');
                                          }

                                          await _systemConfigCubit
                                              .getSystemConfig();
                                        }
                                      },
                                    ),
                                  ),
                                ),
                                colorScheme: colorScheme,
                                primaryColor: primaryColor,
                                isLast: true,
                              ),
                              
                              // إضافة SizedBox بارتفاع صغير لضمان مظهر مناسب
                              const SizedBox(height: 8),
                            ],
                          ),
                        ),
                        
                        // إضافة مساحة في نهاية الصفحة
                        SizedBox(height: size.height * 0.05),
                      ],
                    ),
                  );
                },
              );
            }
            return const SizedBox();
          },
        ),
      ),
    );
  }

  Widget _buildSettingTile({
    required IconData icon,
    required String title,
    required Widget trailing,
    required ColorScheme colorScheme,
    required Color primaryColor,
    String? subtitle,
    bool isFirst = false,
    bool isLast = false,
    VoidCallback? onTap,
  }) {
    final isTablet = MediaQuery.of(context).size.width > 600;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.vertical(
          top: isFirst ? const Radius.circular(24) : Radius.zero,
          bottom: isLast ? const Radius.circular(24) : Radius.zero,
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 24 : 16,
            vertical: isTablet ? 20 : 14,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(
              top: isFirst ? const Radius.circular(24) : Radius.zero,
              bottom: isLast ? const Radius.circular(24) : Radius.zero,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 8),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: primaryColor,
                  size: isTablet ? 28 : 22,
                ),
              ),
              SizedBox(width: isTablet ? 20 : 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: isTablet ? 18 : 15,
                        fontWeight: FontWeights.medium,
                        color: colorScheme.onBackground,
                      ),
                    ),
                    if (subtitle != null) ...[
                      SizedBox(height: isTablet ? 6 : 3),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: isTablet ? 14 : 12,
                          color: colorScheme.onBackground.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(width: 8),
              trailing,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.only(right: 68),
      child: Divider(
        height: 1,
        thickness: 0.5,
        color: Colors.grey.withOpacity(0.2),
      ),
    );
  }
}
