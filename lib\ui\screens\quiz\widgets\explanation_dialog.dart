import 'package:flutter/material.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/video_question_widget.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/explanation_video_screen.dart';
import 'package:google_fonts/google_fonts.dart';

class ExplanationDialog extends StatefulWidget {
  final Question question;
  final VoidCallback? onDialogOpened;
  final VoidCallback? onDialogClosed;

  const ExplanationDialog({
    super.key,
    required this.question,
    this.onDialogOpened,
    this.onDialogClosed,
  });

  @override
  State<ExplanationDialog> createState() => _ExplanationDialogState();
}

class _ExplanationDialogState extends State<ExplanationDialog>
    with SingleTickerProviderStateMixin {
  late TabController? _tabController;

  bool get hasNote =>
      widget.question.note != null && widget.question.note!.isNotEmpty;
  bool get hasVideo => widget.question.hasVideo;
  bool get hasMultipleContent => hasNote && hasVideo;

  @override
  void initState() {
    super.initState();

    // إضافة logs مفصلة

    // إشعار الوالد بفتح النافذة (لإيقاف المؤقت)
    widget.onDialogOpened?.call();

    // تحديد عدد التبويبات بناءً على المحتوى المتوفر
    if (hasMultipleContent) {
      int tabCount = 0;
      if (hasNote) tabCount++;
      if (hasVideo) tabCount++;
      _tabController = TabController(length: tabCount, vsync: this);
    } else {
      _tabController = null;
    }
  }

  @override
  void dispose() {
    // إشعار الوالد بإغلاق النافذة (لاستكمال المؤقت)
    widget.onDialogClosed?.call();
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isPortrait = size.height > size.width;
    final dialogWidth = isPortrait ? size.width * 0.95 : size.width * 0.7;
    final dialogHeight = isPortrait ? size.height * 0.8 : size.height * 0.9;

    // إذا لم يكن هناك محتوى للعرض، لا نعرض أي شيء
    if (!hasNote && !hasVideo) {
      return _buildNoContentDialog(context);
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(context, isPortrait),
            // عرض التبويبات فقط إذا كان هناك محتوى متعدد
            if (hasMultipleContent) _buildTabBar(context),
            Expanded(
              child: hasMultipleContent
                  ? _buildTabBarView(context, isPortrait)
                  : _buildSingleContent(context, isPortrait),
            ),
            _buildCloseButton(context, isPortrait),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isPortrait) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: MediaQuery.of(context).size.height * 0.02,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.lightbulb,
              color: Colors.white,
              size: isPortrait ? 24 : 28,
            ),
          ),
          SizedBox(width: MediaQuery.of(context).size.width * 0.03),
          Text(
            "شرح السؤال",
            style: GoogleFonts.ibmPlexSansArabic(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: isPortrait ? 20 : 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    List<Tab> tabs = [];

    if (hasNote) {
      tabs.add(Tab(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.text_snippet_outlined, size: 18),
              const SizedBox(width: 6),
              Text("شرح نصي",
                  style: GoogleFonts.ibmPlexSansArabic(fontSize: 14)),
            ],
          ),
        ),
      ));
    }

    if (hasVideo) {
      tabs.add(Tab(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.play_circle_outline, size: 18),
              const SizedBox(width: 6),
              Text("شرح بالفيديو",
                  style: GoogleFonts.ibmPlexSansArabic(fontSize: 14)),
            ],
          ),
        ),
      ));
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: TabBar(
        controller: _tabController!,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey.shade600,
        indicator: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: tabs,
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context, bool isPortrait) {
    List<Widget> tabViews = [];

    if (hasNote) {
      tabViews.add(_buildTextExplanation(context, isPortrait));
    }

    if (hasVideo) {
      tabViews.add(_buildVideoExplanation(context, isPortrait));
    }

    return TabBarView(
      controller: _tabController!,
      children: tabViews,
    );
  }

  Widget _buildSingleContent(BuildContext context, bool isPortrait) {
    if (hasVideo) {
      return _buildVideoExplanation(context, isPortrait);
    } else if (hasNote) {
      return _buildTextExplanation(context, isPortrait);
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildTextExplanation(BuildContext context, bool isPortrait) {
    final size = MediaQuery.of(context).size;

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Container(
        margin: EdgeInsets.all(size.width * 0.05),
        padding: EdgeInsets.all(size.width * 0.04),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  "الشرح النصي:",
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              widget.question.note!,
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: isPortrait ? size.width * 0.04 : size.height * 0.025,
                height: 1.6,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.right,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoExplanation(BuildContext context, bool isPortrait) {
    final size = MediaQuery.of(context).size;

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Container(
        margin: EdgeInsets.all(size.width * 0.05),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.play_circle_outline,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  "الشرح بالفيديو:",
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            VideoQuestionWidget(
              question: widget.question,
              height: isPortrait ? size.height * 0.3 : size.height * 0.4,
              autoPlay: false,
              showControls: true,
              onFullScreenTap: () {
                print('🎬 [VIDEO EXPLANATION] الضغط على الشاشة الكاملة');
                // إغلاق نافذة الشرح وفتح صفحة الفيديو
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => ExplanationVideoScreen(
                      question: widget.question,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.fullscreen,
                    color: Colors.blue.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      "اضغط على زر الشاشة الكاملة لمشاهدة الفيديو بحجم أكبر",
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context, bool isPortrait) {
    final size = MediaQuery.of(context).size;

    return Padding(
      padding: EdgeInsets.all(size.width * 0.05),
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            vertical: size.height * 0.015,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.close_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  "إغلاق",
                  style: GoogleFonts.ibmPlexSansArabic(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: isPortrait ? 16 : 18,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoContentDialog(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.orange,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              "لا يوجد شرح متاح",
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              "لا يوجد شرح نصي أو فيديو لهذا السؤال",
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text("حسناً"),
            ),
          ],
        ),
      ),
    );
  }
}
