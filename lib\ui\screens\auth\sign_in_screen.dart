import 'dart:io';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/authRemoteDataSource.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/auth/cubits/signInCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/auth/widgets/email_textfield.dart';
import 'package:flutterquiz/ui/screens/auth/widgets/pswd_textfield.dart';
import 'package:flutterquiz/ui/screens/auth/widgets/terms_and_condition.dart';
import 'package:flutterquiz/ui/widgets/all.dart';
import 'package:flutterquiz/utils/constants/assets_constants.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _formKeyDialog = GlobalKey<FormState>();

  bool isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final emailController = TextEditingController();
  final forgotPswdController = TextEditingController();
  final pswdController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    emailController.dispose();
    forgotPswdController.dispose();
    pswdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SignInCubit>(
      create: (_) => SignInCubit(AuthRepository()),
      child: Builder(
        builder: (context) => Scaffold(
          body: showForm(context),
        ),
      ),
    );
  }

  Widget showForm(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final c = context.read<SystemConfigCubit>();
    final colorScheme = Theme.of(context).colorScheme;
    final primaryColor = Theme.of(context).primaryColor;

    return BlocListener<SignInCubit, SignInState>(
      listener: (context, state) async {
        if (state is SignInSuccess &&
            state.authProvider != AuthProviders.email) {
          context.read<AuthCubit>().updateAuthDetails(
                authProvider: state.authProvider,
                firebaseId: state.user.uid,
                authStatus: true,
                isNewUser: state.isNewUser,
              );
          if (state.isNewUser) {
            context.read<UserDetailsCubit>().fetchUserDetails();
            Navigator.of(context)
                .pushReplacementNamed(Routes.selectProfile, arguments: true);
          } else {
            context.read<UserDetailsCubit>().fetchUserDetails();
            Navigator.of(context).pushNamedAndRemoveUntil(
              Routes.home,
              (_) => false,
              arguments: false,
            );
          }
        } else if (state is SignInFailure &&
            state.authProvider != AuthProviders.email) {
          UiUtils.showSnackBar(
            context.tr(
              convertErrorCodeToLanguageKey(state.errorMessage),
            )!,
            context,
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              primaryColor.withOpacity(0.25),
              primaryColor.withOpacity(0.15),
              primaryColor.withOpacity(0.05),
              Colors.transparent,
            ],
            stops: const [0.0, 0.3, 0.6, 1.0],
          ),
        ),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    SizedBox(height: size.height * 0.08),
                    // Welcome Text with Animation - تحسين الحجم
                    SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.2),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: ModalRoute.of(context)!.animation!,
                        curve: Curves.easeOut,
                      )),
                      child: FadeTransition(
                        opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                          CurvedAnimation(
                            parent: ModalRoute.of(context)!.animation!,
                            curve: Curves.easeOut,
                          ),
                        ),
                        child: Column(
                          children: [
                            ShaderMask(
                              blendMode: BlendMode.srcIn,
                              shaderCallback: (bounds) => LinearGradient(
                                colors: [
                                  primaryColor,
                                  primaryColor.withOpacity(0.8),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ).createShader(bounds),
                              child: Text(
                                'مرحباً بك',
                                style: GoogleFonts.ibmPlexSansArabic(
                                  fontSize: 38,
                                  fontWeight: FontWeights.bold,
                                  height: 1.2,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'سجل دخول للمتابعة',
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 16,
                                color: colorScheme.onBackground.withOpacity(0.7),
                                height: 1.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 25),
                    
                    // Sign-in options info card - تحسين المسافات الداخلية
                    Container(
                      margin: const EdgeInsets.only(bottom: 20),
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).primaryColor.withOpacity(0.15),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.info_outline,
                              color: Theme.of(context).primaryColor,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'يمكنك تسجيل الدخول باستخدام بريدك الإلكتروني أو حساب قوقل أو رقم الهاتف',
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 13,
                                color: Theme.of(context).colorScheme.onBackground,
                                height: 1.4,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Login Methods
                    if (c.areAllLoginMethodsDisabled)
                      ..._buildNoLoginMethods()
                    else ...[
                      if (c.isEmailLoginMethodEnabled) ...[
                        ..._buildEmailLoginMethod(context, size.height),
                      ],

                      if (c.isPhoneLoginMethodEnabled ||
                          c.isAppleLoginMethodEnabled ||
                          c.isGmailLoginMethodEnabled)
                        ..._buildSocialMediaLoginMethods(context, size.height),
                    ],
                    
                    const SizedBox(height: 16),
                    _buildAnimatedTerms(),
                    SizedBox(height: size.height * 0.01),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedTerms() {
    return const AnimatedOpacity(
      opacity: 1.0,
      duration: Duration(milliseconds: 800),
      child: TermsAndCondition(),
    );
  }

  List<Widget> _buildSocialMediaLoginMethods(
    BuildContext context,
    double height,
  ) {
    final c = context.read<SystemConfigCubit>();

    return [
      if (Platform.isIOS && !c.isAppleLoginMethodEnabled) ...[
        const SizedBox(height: 10),
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            context.tr('forIOSMustEnableAppleLogin')!,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 10),
      ],
      if (c.isEmailLoginMethodEnabled) ...[
        orLabel(),
        SizedBox(height: height * 0.03),
        // loginWith(),
        showSocialMedia(context),
      ] else ...[
        BlocBuilder<SignInCubit, SignInState>(
          builder: (context, state) {
            return Column(
              children: state is SignInProgress
                  ? [
                      const Center(child: CircularProgressContainer()),
                    ]
                  : [
                      /// Apple Login
                      if (Platform.isIOS && c.isAppleLoginMethodEnabled) ...[
                        _buildLoginButton(
                          title: context.tr('signInApple')!,
                          icon: Assets.appleIcon,
                          onTap: () => context
                              .read<SignInCubit>()
                              .signInUser(AuthProviders.apple),
                        ),
                      ],

                      /// Gmail Login
                      if (c.isGmailLoginMethodEnabled) ...[
                        if (Platform.isIOS && c.isAppleLoginMethodEnabled) ...[
                          const SizedBox(height: 10),
                        ],
                        _buildLoginButton(
                          title: context.tr('signInGoogle')!,
                          icon: Assets.googleIcon,
                          onTap: () => context
                              .read<SignInCubit>()
                              .signInUser(AuthProviders.gmail),
                        ),
                      ],

                      /// Phone Login
                      if (c.isPhoneLoginMethodEnabled) ...[
                        if (c.isAppleLoginMethodEnabled ||
                            c.isGmailLoginMethodEnabled) ...[
                          const SizedBox(height: 10),
                        ],
                        _buildLoginButton(
                          title: context.tr('signInPhone')!,
                          icon: Assets.phoneIcon,
                          onTap: () =>
                              Navigator.of(context).pushNamed(Routes.otpScreen),
                        ),
                      ],
                    ],
            );
          },
        ),
      ],
    ];
  }

  Widget _buildLoginButton({
    required String title,
    required String icon,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 16,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor.withOpacity(0.15),
                  Theme.of(context).cardColor.withOpacity(0.9),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SvgPicture.asset(
                    icon,
                    width: 24,
                    height: 24,
                    colorFilter: ColorFilter.mode(
                      Theme.of(context).primaryColor,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 16,
                    fontWeight: FontWeights.semiBold,
                    color: Theme.of(context).primaryColor,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildNoLoginMethods() {
    return [
      const SizedBox(height: 20),
      Container(
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          color: Colors.amber.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Colors.amber.withOpacity(0.5)),
        ),
        child: Text(
          context.tr('noLoginMethodsWarning')!,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 18,
            color: Theme.of(context).colorScheme.onTertiary,
            fontWeight: FontWeights.regular,
          ),
        ),
      ),
      const SizedBox(height: 20),
    ];
  }

  List<Widget> _buildEmailLoginMethod(BuildContext context, double height) {
    return [
      _buildAnimatedContainer(
        child: EmailTextField(controller: emailController),
      ),
      SizedBox(height: height * .025),
      _buildAnimatedContainer(
        delay: 200,
        child: PswdTextField(controller: pswdController),
      ),
      SizedBox(height: height * .01),
      forgetPwd(),
      SizedBox(height: height * 0.03),
      _buildSignInButton(context),
      SizedBox(height: height * 0.03),
      showGoSignup(),
    ];
  }

  Widget _buildAnimatedContainer({required Widget child, int delay = 0}) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOut,
      builder: (context, value, _) {
        return Transform.scale(
          scale: value,
          child: Opacity(opacity: value, child: child),
        );
      },
    );
  }

  Widget _buildSignInButton(BuildContext context) {
    return _buildAnimatedContainer(
      delay: 400,
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height * 0.06,
        child: BlocConsumer<SignInCubit, SignInState>(
          bloc: context.read<SignInCubit>(),
          listener: (context, state) async {
            if (state is SignInSuccess &&
                state.authProvider == AuthProviders.email) {
              context.read<AuthCubit>().updateAuthDetails(
                    authProvider: state.authProvider,
                    firebaseId: state.user.uid,
                    authStatus: true,
                    isNewUser: state.isNewUser,
                  );
              if (state.isNewUser) {
                await context.read<UserDetailsCubit>().fetchUserDetails();
                await Navigator.of(context).pushReplacementNamed(
                  Routes.selectProfile,
                  arguments: true,
                );
              } else {
                await context.read<UserDetailsCubit>().fetchUserDetails();
                await Navigator.of(context).pushNamedAndRemoveUntil(
                  Routes.home,
                  (_) => false,
                  arguments: false,
                );
              }
            } else if (state is SignInFailure &&
                state.authProvider == AuthProviders.email) {
              UiUtils.showSnackBar(
                context.tr(
                  convertErrorCodeToLanguageKey(state.errorMessage),
                )!,
                context,
              );
            }
          },
          builder: (context, state) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withOpacity(0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).primaryColor.withOpacity(0.25),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: state is SignInProgress
                      ? null
                      : () async {
                          if (_formKey.currentState!.validate()) {
                            {
                              context.read<SignInCubit>().signInUser(
                                    AuthProviders.email,
                                    email: emailController.text.trim(),
                                    password: pswdController.text.trim(),
                                  );
                            }
                          }
                        },
                  borderRadius: BorderRadius.circular(15),
                  child: Center(
                    child: state is SignInProgress &&
                            state.authProvider == AuthProviders.email
                        ? const CircularProgressContainer(whiteLoader: true)
                        : Text(
                            context.tr('loginLbl')!,
                            style: GoogleFonts.ibmPlexSansArabic(
                              textStyle: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeights.medium,
                              ),
                            ),
                          ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget forgetPwd() {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        onPressed: () async {
          await showModalBottomSheet<void>(
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
              borderRadius: UiUtils.bottomSheetTopRadius,
            ),
            context: context,
            builder: (context) => Padding(
              padding: MediaQuery.of(context).viewInsets,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.1),
                      Theme.of(context).scaffoldBackgroundColor,
                    ],
                  ),
                  borderRadius: UiUtils.bottomSheetTopRadius,
                ),
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * (0.45),
                ),
                child: Form(
                  key: _formKeyDialog,
                  child: Column(
                    children: [
                      SizedBox(
                        height: MediaQuery.of(context).size.height * 0.03,
                      ),
                      Text(
                        context.tr('resetPwdLbl')!,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 22,
                          color: Theme.of(context).colorScheme.onTertiary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsetsDirectional.only(
                          start: 20,
                          end: 20,
                          top: 20,
                        ),
                        child: Text(
                          context.tr('resetEnterEmailLbl')!,
                          textAlign: TextAlign.center,
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 16,
                            color: Theme.of(context).colorScheme.onTertiary,
                            fontWeight: FontWeights.semiBold,
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsetsDirectional.only(
                          start: MediaQuery.of(context).size.width * .08,
                          end: MediaQuery.of(context).size.width * .08,
                          top: 20,
                        ),
                        child:
                            EmailTextField(controller: forgotPswdController),
                      ),
                      const SizedBox(height: 30),
                      Container(
                        width: MediaQuery.of(context).size.width * 0.55,
                        height: 50,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).primaryColor,
                              Theme.of(context).primaryColor.withOpacity(0.8),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).primaryColor.withOpacity(0.25),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              final form = _formKeyDialog.currentState;
                              if (form!.validate()) {
                                form.save();
                                UiUtils.showSnackBar(
                                  context.tr('pwdResetLinkLbl')!,
                                  context,
                                );
                                AuthRemoteDataSource().resetPassword(
                                  forgotPswdController.text.trim(),
                                );
                                Future.delayed(const Duration(seconds: 1), () {
                                  Navigator.pop(context, 'Cancel');
                                });

                                forgotPswdController.text = '';
                                form.reset();
                              }
                            },
                            borderRadius: BorderRadius.circular(10),
                            child: Center(
                              child: Text(
                                context.tr('submitBtn')!,
                                style: GoogleFonts.ibmPlexSansArabic(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeights.medium,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
        child: Text(
          context.tr('forgotPwdLbl')!,
          style: TextStyle(
            fontWeight: FontWeights.medium,
            fontSize: 14,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }

  Widget orLabel() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        children: [
          Expanded(
            child: Divider(
              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.2),
              thickness: 1,
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              context.tr('orLbl')!,
              style: GoogleFonts.ibmPlexSansArabic(
                color: Theme.of(context).primaryColor,
                fontSize: 14,
                fontWeight: FontWeights.medium,
              ),
            ),
          ),
          Expanded(
            child: Divider(
              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.2),
              thickness: 1,
            ),
          ),
        ],
      ),
    );
  }

  // Widget loginWith() {
  //   return Container(
  //     margin: const EdgeInsets.only(bottom: 15),
  //     child: Text(
  //       context.tr('loginSocialMediaLbl')!,
  //       textAlign: TextAlign.center,
  //       style: GoogleFonts.ibmPlexSansArabic(
  //         fontWeight: FontWeights.regular,
  //         color: Theme.of(context).colorScheme.onTertiary.withOpacity(0.7),
  //         fontSize: 15,
  //       ),
  //     ),
  //   );
  // }

  Widget showSocialMedia(BuildContext context) {
    return BlocBuilder<SignInCubit, SignInState>(
      builder: (context, state) {
        final c = context.read<SystemConfigCubit>();

        return Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: (state is SignInProgress &&
                    state.authProvider != AuthProviders.email)
                ? [
                    const Center(child: CircularProgressContainer()),
                  ]
                : [
                    ///
                    if (Platform.isIOS && c.isAppleLoginMethodEnabled) ...[
                      _buildSocialLoginButton(
                        context: context,
                        icon: Assets.appleIcon,
                        onTap: () => context.read<SignInCubit>().signInUser(AuthProviders.apple),
                      ),
                    ],

                    ///
                    if (c.isGmailLoginMethodEnabled) ...[
                      if (Platform.isIOS && c.isAppleLoginMethodEnabled)
                        const SizedBox(width: 25),
                      _buildSocialLoginButton(
                        context: context,
                        icon: Assets.googleIcon,
                        onTap: () => context.read<SignInCubit>().signInUser(AuthProviders.gmail),
                      ),
                    ],

                    ///
                    if (c.isPhoneLoginMethodEnabled) ...[
                      if (c.isAppleLoginMethodEnabled ||
                          c.isGmailLoginMethodEnabled)
                        const SizedBox(width: 25),
                      _buildSocialLoginButton(
                        context: context,
                        icon: Assets.phoneIcon,
                        onTap: () => Navigator.of(context).pushNamed(Routes.otpScreen),
                      ),
                    ],
                  ],
          ),
        );
      },
    );
  }

  Widget _buildSocialLoginButton({
    required BuildContext context,
    required String icon,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(30),
          child: Container(
            height: 60,
            width: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).cardColor,
                  Theme.of(context).cardColor,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
            ),
            alignment: Alignment.center,
            padding: const EdgeInsets.all(15),
            child: SvgPicture.asset(
              icon,
              height: 30,
              width: 30,
              colorFilter: ColorFilter.mode(
                Theme.of(context).primaryColor,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget showGoSignup() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            context.tr('noAccountLbl')!,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 15,
              fontWeight: FontWeights.regular,
              color: Theme.of(context).colorScheme.onTertiary.withOpacity(0.7),
            ),
          ),
          const SizedBox(width: 4),
          TextButton(
            onPressed: () {
              _formKey.currentState!.reset();
              Navigator.of(context).pushNamed(Routes.signUp);
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              context.tr('signUpLbl')!,
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 15,
                fontWeight: FontWeights.semiBold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
