import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/features/systemConfig/model/answer_mode.dart';
import 'package:flutterquiz/ui/screens/quiz/quiz_screen.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/latex_answer_options_list.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/lifeLine_options.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutterquiz/utils/assets_utils.dart'; // إضافة هذا الاستيرادلاستيراد

class QuestionsContainer extends StatefulWidget {
  const QuestionsContainer({
    required this.submitAnswer,
    required this.quizType,
    required this.hasSubmittedAnswerForCurrentQuestion,
    required this.currentQuestionIndex,
    required this.questionAnimationController,
    required this.questionContentAnimationController,
    required this.questionContentAnimation,
    required this.questionScaleDownAnimation,
    required this.questionScaleUpAnimation,
    required this.questionSlideAnimation,
    required this.questions,
    required this.lifeLines,
    required this.timerAnimationController,
    required this.answerMode,
    super.key,
    this.level,
    this.topPadding,
  });

  final QuizTypes quizType;
  final bool Function() hasSubmittedAnswerForCurrentQuestion;
  final int currentQuestionIndex;
  final void Function(String) submitAnswer;
  final AnimationController questionContentAnimationController;
  final AnimationController questionAnimationController;
  final Animation<double> questionSlideAnimation;
  final Animation<double> questionScaleUpAnimation;
  final Animation<double> questionScaleDownAnimation;
  final Animation<double> questionContentAnimation;
  final List<Question> questions;

  final double? topPadding;
  final String? level;
  final Map<String, LifelineStatus> lifeLines;
  final AnswerMode answerMode;
  final AnimationController timerAnimationController;

  @override
  State<QuestionsContainer> createState() => _QuestionsContainerState();
}

class _QuestionsContainerState extends State<QuestionsContainer> {
  List<AnswerOption> filteredOptions = [];
  List<int> audiencePollPercentages = [];

  late double textSize;

  late final bool _isLatex = widget.quizType == QuizTypes.mathMania ||
      (context.read<SystemConfigCubit>().isLatexModeEnabled &&
          (widget.quizType == QuizTypes.quizZone ||
              widget.quizType == QuizTypes.oneVsOneBattle ||
              widget.quizType == QuizTypes.groupPlay ||
              widget.quizType == QuizTypes.dailyQuiz ||
              widget.quizType == QuizTypes.trueAndFalse ||
              widget.quizType == QuizTypes.selfChallenge));

  // إضافة متغير للتحكم في الصوت
  late bool isSoundEnabled;

  @override
  void initState() {
    textSize = widget.quizType == QuizTypes.groupPlay
        ? 20
        : context.read<SettingsCubit>().getSettings().playAreaFontSize;
    super.initState();
    // تهيئة حالة الصوت من الإعدادات
    isSoundEnabled = context.read<SettingsCubit>().getSettings().sound;
  }

  //to get question length
  int getQuestionsLength() {
    if (widget.questions.isNotEmpty) {
      return widget.questions.length;
    }
    return 0;
  }

  var _usingAudiencePoll = false;
  var _usingFiftyFifty = false;

  // تعديل دالة تشغيل الصوت
  void _playSound(bool isCorrect) {
    if (!isSoundEnabled) return;

    if (!widget.hasSubmittedAnswerForCurrentQuestion()) {
      if (widget.answerMode == AnswerMode.noAnswerCorrectness) {
        // استخدام صوت النقر في حالة عدم إظهار الإجابات
        AssetsUtils.playClickSound();
      } else {
        // استخدام أصوات الإجابة الصحيحة/الخاطئة في الحالات الأخرى
        if (isCorrect) {
          AssetsUtils.playCorrectAnswerSound();
        } else {
          AssetsUtils.playWrongAnswerSound();
        }
      }
    }
  }

  Widget _buildOptions(Question question, BoxConstraints constraints) {
    final correctAnswerId = AnswerEncryption.decryptCorrectAnswer(
      rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
      correctAnswer: question.correctAnswer!,
    );

    if (!question.attempted && widget.lifeLines.isNotEmpty) {
      _usingAudiencePoll =
          widget.lifeLines[audiencePoll] == LifelineStatus.using;
      _usingFiftyFifty = widget.lifeLines[fiftyFifty] == LifelineStatus.using;

      if (_usingAudiencePoll) {
        audiencePollPercentages = LifeLineOptions.getAudiencePollPercentage(
          question.answerOptions!,
          correctAnswerId,
        );
      }

      if (_usingFiftyFifty) {
        filteredOptions = LifeLineOptions.getFiftyFiftyOptions(
          question.answerOptions!,
          correctAnswerId,
        );
      }
    }

    ///
    if (_isLatex) {
      return LatexAnswerOptions(
        hasSubmittedAnswerForCurrentQuestion:
            widget.hasSubmittedAnswerForCurrentQuestion,
        submitAnswer: widget.submitAnswer,
        constraints: constraints,
        submittedAnswerId: question.submittedAnswerId,
        correctAnswerId: correctAnswerId,
        showAudiencePoll: _usingAudiencePoll,
        answerMode: widget.answerMode,
        audiencePollPercentages: audiencePollPercentages,
        answerOptions:
            _usingFiftyFifty ? filteredOptions : question.answerOptions!,
      );
    } else {
      return Column(
        children: (_usingFiftyFifty ? filteredOptions : question.answerOptions!)
            .map((option) {
          final idx = question.answerOptions!.indexOf(option);
          final isCorrectAnswer = option.id == correctAnswerId;
          final isAttempted = question.attempted;
          final hasSubmitted = widget.hasSubmittedAnswerForCurrentQuestion();
          final isSelected = question.submittedAnswerId == option.id;

          // تحديد لون الخلفية حسب إعدادات عرض الإجابة
          Color getBackgroundColor() {
            if (!hasSubmitted) return Colors.white;

            switch (widget.answerMode) {
              case AnswerMode.noAnswerCorrectness:
                if (isSelected) {
                  return Theme.of(context)
                      .primaryColor
                      .withOpacity(0.1); // تأثير خفيف عند اختيار إجابة
                }
                return Colors.white;

              case AnswerMode.showAnswerCorrectness:
                if (isSelected) {
                  return isCorrectAnswer
                      ? Colors.green.withOpacity(0.15)
                      : Colors.red.withOpacity(0.15);
                }
                return Colors.white;

              case AnswerMode.showAnswerCorrectnessAndCorrectAnswer:
                if (isSelected) {
                  return isCorrectAnswer
                      ? Colors.green.withOpacity(0.15)
                      : Colors.red.withOpacity(0.15);
                } else if (isCorrectAnswer && isAttempted) {
                  return Colors.green.withOpacity(0.15);
                }
                return Colors.white;
            }
          }

          // تحديد لون الحدود حسب إعدادات عرض الإجابة
          Color getBorderColor() {
            if (!hasSubmitted)
              return Theme.of(context).primaryColor.withOpacity(0.3);

            switch (widget.answerMode) {
              case AnswerMode.noAnswerCorrectness:
                if (isSelected) {
                  return Theme.of(context)
                      .primaryColor; // لون الحدود للإجابة المختارة
                }
                return Theme.of(context).primaryColor.withOpacity(0.3);

              case AnswerMode.showAnswerCorrectness:
                if (isSelected) {
                  return isCorrectAnswer ? Colors.green : Colors.red;
                }
                return Theme.of(context).primaryColor.withOpacity(0.3);

              case AnswerMode.showAnswerCorrectnessAndCorrectAnswer:
                if (isSelected) {
                  return isCorrectAnswer ? Colors.green : Colors.red;
                } else if (isCorrectAnswer && isAttempted) {
                  return Colors.green;
                }
                return Theme.of(context).primaryColor.withOpacity(0.3);
            }
          }

          // تحديد ما إذا كان سيتم عرض أيقونة الإجابة
          bool shouldShowIcon() {
            if (!hasSubmitted) return false;

            switch (widget.answerMode) {
              case AnswerMode.noAnswerCorrectness:
                return false;
              case AnswerMode.showAnswerCorrectness:
                return isSelected;
              case AnswerMode.showAnswerCorrectnessAndCorrectAnswer:
                return isSelected || (isCorrectAnswer && isAttempted);
            }
          }

          return Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            decoration: BoxDecoration(
              color: getBackgroundColor(),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: getBorderColor(),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: getBorderColor().withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(15),
                onTap: () {
                  if (!widget.hasSubmittedAnswerForCurrentQuestion()) {
                    // تشغيل الصوت عند اختيار إجابة
                    final isCorrect = option.id == correctAnswerId;
                    _playSound(isCorrect);

                    widget.submitAnswer(option.id!);
                  }
                },
                child: Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(
                    vertical: 15,
                    horizontal: 20,
                  ),
                  child: Row(
                    children: [
                      // رقم الإجابة
                      Container(
                        width: 28,
                        height: 28,
                        margin: const EdgeInsets.only(left: 10),
                        decoration: BoxDecoration(
                          color: getBorderColor().withOpacity(0.1),
                          borderRadius: BorderRadius.circular(14),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          String.fromCharCode(65 + idx), // A, B, C, D
                          style: TextStyle(
                            color: getBorderColor(),
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      const SizedBox(width: 10),

                      Expanded(
                        child: Text(
                          option.title!,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),

                      if (shouldShowIcon())
                        Icon(
                          isCorrectAnswer ? Icons.check_circle : Icons.cancel,
                          color: isCorrectAnswer ? Colors.green : Colors.red,
                          size: 24,
                        ),

                      if (_usingAudiencePoll)
                        Container(
                          margin: const EdgeInsets.only(left: 10),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${audiencePollPercentages[idx]}%',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      );
    }
  }

  // Widget _buildCurrentQuestionIndex() {
  //   final theme = Theme.of(context);
  //   final colorScheme = theme.colorScheme;
  //   final size = MediaQuery.of(context).size;
  //   final isSmallScreen = size.width < 360;

  //   return Container(
  //     padding: EdgeInsets.symmetric(
  //       horizontal: isSmallScreen ? 6 : 8,
  //       vertical: isSmallScreen ? 6 : 8,
  //     ),
  //     decoration: BoxDecoration(
  //       gradient: LinearGradient(
  //         colors: [
  //           theme.primaryColor.withOpacity(0.8),
  //           theme.primaryColor.withOpacity(0.6),
  //         ],
  //         begin: Alignment.topLeft,
  //         end: Alignment.bottomRight,
  //       ),
  //       borderRadius: BorderRadius.circular(16),
  //       boxShadow: [
  //         BoxShadow(
  //           color: theme.primaryColor.withOpacity(0.2),
  //           blurRadius: 8,
  //           offset: const Offset(0, 2),
  //         ),
  //       ],
  //     ),
  //     child: Row(
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         Container(
  //           padding: EdgeInsets.all(isSmallScreen ? 4 : 6),
  //           decoration: BoxDecoration(
  //             color: colorScheme.onPrimary.withOpacity(0.2),
  //             shape: BoxShape.circle,
  //           ),
  //           child: Text(
  //             '${widget.currentQuestionIndex + 1}',
  //             style: TextStyle(
  //               color: colorScheme.onPrimary,
  //               fontSize: isSmallScreen ? 14 : 16,
  //               fontWeight: FontWeight.bold,
  //             ),
  //           ),
  //         ),
  //         SizedBox(width: isSmallScreen ? 4 : 6),
  //         Text(
  //           'من',
  //           style: TextStyle(
  //             color: colorScheme.onPrimary.withOpacity(0.9),
  //             fontSize: isSmallScreen ? 12 : 14,
  //           ),
  //         ),
  //         SizedBox(width: isSmallScreen ? 4 : 6),
  //         Container(
  //           padding: EdgeInsets.symmetric(
  //             horizontal: isSmallScreen ? 8 : 10,
  //             vertical: 2,
  //           ),
  //           decoration: BoxDecoration(
  //             color: colorScheme.onPrimary.withOpacity(0.15),
  //             borderRadius: BorderRadius.circular(8),
  //           ),
  //           child: Text(
  //             '${widget.questions.length}',
  //             style: TextStyle(
  //               color: colorScheme.onPrimary,
  //               fontSize: isSmallScreen ? 12 : 14,
  //               fontWeight: FontWeight.w600,
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // TeXViewStyle _teXViewStyle() {
  //   return TeXViewStyle(
  //     borderRadius: const TeXViewBorderRadius.all(8),
  //     contentColor: Colors.white,
  //     backgroundColor: const Color.fromARGB(134, 66, 49, 165),
  //     padding: const TeXViewPadding.all(10),
  //     textAlign: TeXViewTextAlign.center,
  //     fontStyle: TeXViewFontStyle(
  //       fontFamily: 'IBMPlexSansArabic-Regular',
  //     ),
  //   );
  // }

  Widget _buildQuestionText({
    required String questionText,
    required String questionType,
  }) {
    return _isLatex
        ? Container(
            margin: const EdgeInsets.all(12),
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(
              horizontal:
                  MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
              vertical:
                  MediaQuery.of(context).size.height * UiUtils.vtMarginPct,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Text(
              questionText,
              textAlign: TextAlign.center,
              style: GoogleFonts.ibmPlexSansArabic(
                textStyle: TextStyle(
                  height: 1.5,
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: textSize,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.2,
                ),
              ),
            ),
          )
        // TeXView(
        //     onRenderFinished: (_) {
        //       if (widget.quizType != QuizTypes.selfChallenge) {
        //         widget.timerAnimationController.forward();
        //       }
        //     },
        //     child: TeXViewDocument(questionText),
        //     style: _teXViewStyle(),
        //     fonts: const [
        //       TeXViewFont(
        //         fontFamily: 'IBMPlexSansArabic-Regular',
        //         src: 'assets/fonts/IBMPlexSansArabic-Regular.ttf',
        //       ),
        //       TeXViewFont(
        //         fontFamily: 'IBMPlexSansArabic-Bold',
        //         src: 'assets/fonts/IBMPlexSansArabic-Bold.ttf',
        //       ),
        //     ],
        //   )
        : Container(
            margin: const EdgeInsets.all(12),
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(
              horizontal:
                  MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
              vertical:
                  MediaQuery.of(context).size.height * UiUtils.vtMarginPct,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Text(
              questionText,
              textAlign: TextAlign.center,
              style: GoogleFonts.ibmPlexSansArabic(
                textStyle: TextStyle(
                  height: 1.5,
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: textSize,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.2,
                ),
              ),
            ),
          );
  }

/******  ac34d1eb-9232-40fa-be9c-08988584e0dd  *******/ void
      _showFullScreenImage(BuildContext context, String imageUrl) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    showDialog(
      context: context,
      builder: (context) {
        return GestureDetector(
          onTap: () {
            Navigator.of(context).pop(); // اغلاق النافذة عند النقر
          },
          child: Dialog(
            backgroundColor: Colors.transparent,
            insetPadding: const EdgeInsets.all(10),
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              placeholder: (context, url) => const Center(
                child: CircularProgressIndicator(),
              ),
              errorWidget: (context, url, error) => const Icon(Icons.error),
              imageBuilder: (context, imageProvider) {
                return Container(
                  width: screenWidth,
                  height: screenHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.contain, // عرض الصورة بشكل ملائم
                    ),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 10,
                        offset: Offset(0, 5),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuestionContainer(
    double scale,
    int index,
    bool showContent,
    BuildContext context,
  ) {
    final child = LayoutBuilder(
      builder: (context, constraints) {
        final question = widget.questions[index];

        final hasImage =
            question.imageUrl != null && question.imageUrl!.isNotEmpty;

        return SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.quizType == QuizTypes.oneVsOneBattle ||
                  widget.quizType == QuizTypes.groupPlay)
                const SizedBox()
              else
                const SizedBox(height: 15),
              // current question index
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (widget.quizType == QuizTypes.groupPlay) ...[
                    const SizedBox(),
                  ],
                  // _buildCurrentQuestionIndex(),
                  if (widget.quizType == QuizTypes.groupPlay) ...[
                    const SizedBox(),
                  ],
                ],
              ),
              if (hasImage)
                Container(
                  width: MediaQuery.of(context).size.width,
                  height: (constraints.maxHeight *
                          (widget.quizType == QuizTypes.groupPlay
                              ? 0.25
                              : 0.9)) /
                      3,
                  alignment: Alignment.center,
                  margin: const EdgeInsets.only(top: 21),
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    // color: Theme.of(context).colorScheme.onPrimary,
                    // color: const Color.fromARGB(192, 66, 49, 165),
                    //   color:Theme.of(context).primaryColor.withOpacity(0.5),

                    border: Border.all(
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.4),
                    ),
                  ),
                  child: GestureDetector(
                    onTap: () {
                      _showFullScreenImage(context, question.imageUrl!);
                    },
                    child: Stack(
                      children: [
                        CachedNetworkImage(
                          placeholder: (_, __) => const Center(
                            child: CircularProgressContainer(),
                          ),
                          imageUrl: question.imageUrl!,
                          imageBuilder: (context, imageProvider) {
                            return Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: imageProvider,
                                  fit: BoxFit.contain,
                                ),
                                borderRadius: BorderRadius.circular(10),
                              ),
                            );
                          },
                          errorWidget: (_, i, e) {
                            return Center(
                              child: Icon(
                                Icons.error,
                                color: Theme.of(context).primaryColor,
                              ),
                            );
                          },
                        ),
                        Positioned(
                          top: -8,
                          right: -8,
                          child: CircleAvatar(
                            backgroundColor:
                                Theme.of(context).colorScheme.onPrimary,
                            child: const Icon(
                              Icons.zoom_out_map,
                              color: Colors.black45,
                              size: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              // عرض نص السؤال فقط إذا كان أكثر من حرف واحد
              if (question.shouldShowQuestionText)
                Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.symmetric(vertical: 20),
                  padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    //color: Theme.of(context).colorScheme.onPrimary,
                    //color: const Color.fromARGB(134, 66, 49, 165),
                    // border: Border.all(
                    //   color: Theme.of(context).primaryColor.withOpacity(0.4),
                    // ),
                  ),
                  child: _buildQuestionText(
                    questionText: question.question!,
                    questionType: question.questionType!,
                  ),
                ),
              _buildOptions(question, constraints),
              const SizedBox(height: 5),
            ],
          ),
        );
      },
    );

    return Container(
      transform: Matrix4.identity()..scale(scale),
      transformAlignment: Alignment.center,
      width: MediaQuery.of(context).size.width *
          UiUtils.questionContainerWidthPercentage,
      height: MediaQuery.of(context).size.height *
          (UiUtils.questionContainerHeightPercentage -
              0.045 * (widget.quizType == QuizTypes.groupPlay ? 1.0 : 0.0)),
      child: showContent
          ? SlideTransition(
              position: widget.questionContentAnimation.drive(
                Tween<Offset>(
                  begin: const Offset(0.5, 0),
                  end: Offset.zero,
                ),
              ),
              child: FadeTransition(
                opacity: widget.questionContentAnimation,
                child: child,
              ),
            )
          : const SizedBox(),
    );
  }

  Widget _buildQuestion(int questionIndex, BuildContext context) {
    //
    //if current question index is same as question index means
    //it is current question and will be on top
    //so we need to add animation that slide and fade this question
    if (widget.currentQuestionIndex == questionIndex) {
      return FadeTransition(
        opacity: widget.questionSlideAnimation.drive(
          Tween<double>(begin: 1, end: 0),
        ),
        child: SlideTransition(
          position: widget.questionSlideAnimation.drive(
            Tween<Offset>(begin: Offset.zero, end: const Offset(1.5, 0)),
          ),
          child: _buildQuestionContainer(1, questionIndex, true, context),
        ),
      );
    }
    //if the question is second or after current question
    //so we need to animation that scale this question
    //initial scale of this question is 0.95

    else if (questionIndex > widget.currentQuestionIndex &&
        (questionIndex == widget.currentQuestionIndex + 1)) {
      return AnimatedBuilder(
        animation: widget.questionAnimationController,
        builder: (context, child) {
          final scale = 0.95 +
              widget.questionScaleUpAnimation.value -
              widget.questionScaleDownAnimation.value;
          return _buildQuestionContainer(scale, questionIndex, false, context);
        },
      );
    }
    //to build question except top 2

    else if (questionIndex > widget.currentQuestionIndex) {
      return _buildQuestionContainer(1, questionIndex, false, context);
    }
    //if the question is already animated that show empty container
    return const SizedBox();
  }

  //to build questions
  List<Widget> _buildQuestions(BuildContext context) {
    final children = <Widget>[];

    //loop terminate condition will be questions.length instead of 4
    for (var i = 0; i < getQuestionsLength(); i++) {
      //add question
      children.add(_buildQuestion(i, context));
    }
    //need to reverse the list in order to display 1st question in top

    return children.reversed.toList();
  }

  @override
  Widget build(BuildContext context) {
    //Font Size change Lister to change questions font size
    return BlocListener<SettingsCubit, SettingsState>(
      bloc: context.read<SettingsCubit>(),
      listener: (context, state) {
        // تحديث حالة الصوت عند تغيير الإعدادات
        if (state.settingsModel!.sound != isSoundEnabled) {
          setState(() {
            isSoundEnabled = state.settingsModel!.sound;
          });
        }
        if (state.settingsModel!.playAreaFontSize != textSize) {
          setState(() {
            textSize =
                context.read<SettingsCubit>().getSettings().playAreaFontSize;
          });
        }
      },
      child: Stack(
        alignment: Alignment.topCenter,
        children: _buildQuestions(context),
      ),
    );
  }
}
