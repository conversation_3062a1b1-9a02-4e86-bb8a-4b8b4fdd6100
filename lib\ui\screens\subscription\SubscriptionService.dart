import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

class SubscriptionState {
  final bool isSubscribed;
  final String? error;
  final bool isLoading;
  final CustomerInfo? customerInfo;

  SubscriptionState({
    this.isSubscribed = false,
    this.error,
    this.isLoading = false,
    this.customerInfo,
  });

  SubscriptionState copyWith({
    bool? isSubscribed,
    String? error,
    bool? isLoading,
    CustomerInfo? customerInfo,
  }) {
    return SubscriptionState(
      isSubscribed: isSubscribed ?? this.isSubscribed,
      error: error,
      isLoading: isLoading ?? this.isLoading,
      customerInfo: customerInfo ?? this.customerInfo,
    );
  }
}

class SubscriptionCubit extends Cubit<SubscriptionState> {
  static const String entitlementID = 'premium_access';

  SubscriptionCubit() : super(SubscriptionState()) {
    _initializeListener();
    checkSubscription();
  }

  void _initializeListener() {
    Purchases.addCustomerInfoUpdateListener((customerInfo) async {
      final entitlement = customerInfo.entitlements.all[entitlementID];
      emit(state.copyWith(
        isSubscribed: entitlement?.isActive ?? false,
        customerInfo: customerInfo,
      ));
    });
  }

  Future<void> checkSubscription() async {
    emit(state.copyWith(isLoading: true));
    try {
      final customerInfo = await Purchases.getCustomerInfo();
      final entitlement = customerInfo.entitlements.all[entitlementID];
      emit(state.copyWith(
        isSubscribed: entitlement?.isActive ?? false,
        customerInfo: customerInfo,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }

  Future<void> restorePurchases() async {
    emit(state.copyWith(isLoading: true));
    try {
      final customerInfo = await Purchases.restorePurchases();
      final entitlement = customerInfo.entitlements.all[entitlementID];
      emit(state.copyWith(
        isSubscribed: entitlement?.isActive ?? false,
        customerInfo: customerInfo,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }

  @override
  Future<void> close() {
    Purchases.removeCustomerInfoUpdateListener((_) {});
    return super.close();
  }
}