import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/ads/rewarded_ad_cubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/cubits/questionsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/unlockedLevelCubit.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/quiz/models/comprehension.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/audioQuestionContainer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/explanation_dialog.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/customAppbar.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/exitGameDialog.dart';
import 'package:flutterquiz/ui/widgets/questionsContainer.dart';
import 'package:flutterquiz/ui/widgets/quick_settings_dialog.dart';
import 'package:flutterquiz/ui/widgets/text_circular_timer.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:flutter/services.dart';
// import 'package:video_player/video_player.dart';
// import 'package:chewie/chewie.dart';

enum LifelineStatus { unused, using, used }

class QuizScreen extends StatefulWidget {
  const QuizScreen({
    required this.isPlayed,
    required this.numberOfPlayer,
    required this.subcategoryMaxLevel,
    required this.quizType,
    required this.categoryId,
    required this.level,
    required this.subcategoryId,
    required this.unlockedLevel,
    required this.contestId,
    required this.comprehension,
    required this.isPremiumCategory,
    super.key,
    this.showRetryButton = true,
  });

  final int numberOfPlayer;
  final QuizTypes quizType;
  final String level; //will be in use for quizZone quizType
  final String categoryId; //will be in use for quizZone quizType
  final String subcategoryId; //will be in use for quizZone quizType
  final String
      subcategoryMaxLevel; //will be in use for quizZone quizType (to pass in result screen)
  final int unlockedLevel;
  final bool isPlayed; //Only in use when quiz type is audio questions
  final String contestId;
  final Comprehension
      comprehension; // will be in use for fun n learn quizType (to pass in result screen)

  // only used for when there is no questions for that category,
  // and showing retry button doesn't make any sense i guess.
  final bool showRetryButton;
  final bool isPremiumCategory;

  @override
  State<QuizScreen> createState() => _QuizScreenState();

  //to provider route
  static Route<dynamic> route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments! as Map;
    //keys of arguments are numberOfPlayer and quizType (required)
    //if quizType is quizZone then need to pass following keys
    //categoryId, subcategoryId, level, subcategoryMaxLevel and unlockedLevel

    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          //for questions and points
          BlocProvider<QuestionsCubit>(
            create: (_) => QuestionsCubit(QuizRepository()),
          ),
          //to update user coins after using lifeline
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
        ],
        child: QuizScreen(
          isPlayed: arguments['isPlayed'] as bool? ?? true,
          numberOfPlayer: arguments['numberOfPlayer'] as int,
          quizType: arguments['quizType'] as QuizTypes,
          categoryId: arguments['categoryId'] as String? ?? '',
          level: arguments['level'] as String? ?? '',
          subcategoryId: arguments['subcategoryId'] as String? ?? '',
          subcategoryMaxLevel:
              arguments['subcategoryMaxLevel'] as String? ?? '',
          unlockedLevel: arguments['unlockedLevel'] as int? ?? 0,
          contestId: arguments['contestId'] as String? ?? '',
          comprehension: arguments['comprehension'] as Comprehension? ??
              Comprehension.empty(),
          showRetryButton: arguments['showRetryButton'] as bool? ?? true,
          isPremiumCategory: arguments['isPremiumCategory'] as bool? ?? false,
        ),
      ),
    );
  }
}

class _QuizScreenState extends State<QuizScreen> with TickerProviderStateMixin {
  // إضافة متغير جديد لتتبع استخدام المساعدة في السؤال الحالي
  Map<String, bool> questionsUsingHelp = {};

  late AnimationController questionAnimationController;
  late AnimationController questionContentAnimationController;
  late AnimationController audioTimerController = AnimationController(
    vsync: this,
    duration: Duration(
      seconds: widget.quizType == QuizTypes.audioQuestions
          ? context
              .read<SystemConfigCubit>()
              .quizTimer(QuizTypes.audioQuestions)
          : 0,
    ),
  );
  late final timerAnimationController = AnimationController(
    vsync: this,
    reverseDuration: const Duration(seconds: inBetweenQuestionTimeInSeconds),
    duration: Duration(
      seconds: context.read<SystemConfigCubit>().quizTimer(widget.quizType),
    ),
  )..addStatusListener(currentUserTimerAnimationStatusListener);

  late Animation<double> questionSlideAnimation;
  late Animation<double> questionScaleUpAnimation;
  late Animation<double> questionScaleDownAnimation;
  late Animation<double> questionContentAnimation;
  late AnimationController animationController;
  late AnimationController topContainerAnimationController;
  late AnimationController showOptionAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 500),
  );
  late Animation<double> showOptionAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: showOptionAnimationController,
      curve: Curves.easeInOut,
    ),
  );
  late List<GlobalKey<AudioQuestionContainerState>> audioQuestionContainerKeys =
      [];
  int currentQuestionIndex = 0;
  final double optionWidth = 0.7;
  final double optionHeight = 0.09;

  late double totalSecondsToCompleteQuiz = 0;

  late Map<String, LifelineStatus> lifelines = {
    fiftyFifty: LifelineStatus.unused,
    audiencePoll: LifelineStatus.unused,
    skip: LifelineStatus.unused,
    resetTime: LifelineStatus.unused,
  };

  //to track if setting dialog is open
  bool isSettingDialogOpen = false;
  bool isExitDialogOpen = false;

  // VideoPlayerController? _videoController;
  // ChewieController? _chewieController;
  void _getQuestions() {
    Future.delayed(
      Duration.zero,
      () {
        context.read<QuestionsCubit>().getQuestions(
              widget.quizType,
              categoryId: widget.categoryId,
              level: widget.level,
              languageId: UiUtils.getCurrentQuizLanguageId(context),
              subcategoryId: widget.subcategoryId,
              contestId: widget.contestId,
              funAndLearnId: widget.comprehension.id,
            );
      },
    );
  }

  @override
  void initState() {
    super.initState();

    //init reward ad
    Future.delayed(Duration.zero, () {
      context.read<RewardedAdCubit>().createRewardedAd(context);
    });
    //init animations
    initializeAnimation();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    topContainerAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    //
    _getQuestions();
  }

  void initializeAnimation() {
    questionContentAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );
    questionAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 525),
    );
    questionSlideAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    questionScaleUpAnimation = Tween<double>(begin: 0, end: 0.1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0, 0.5, curve: Curves.easeInQuad),
      ),
    );
    questionContentAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionContentAnimationController,
        curve: Curves.easeInQuad,
      ),
    );
    questionScaleDownAnimation = Tween<double>(begin: 0, end: 0.05).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0.5, 1, curve: Curves.easeOutQuad),
      ),
    );
  }

  @override
  void dispose() {
    timerAnimationController
      ..removeStatusListener(currentUserTimerAnimationStatusListener)
      ..dispose();
    questionAnimationController.dispose();
    questionContentAnimationController.dispose();
    // _disposeVideoControllers();
    super.dispose();
  }

  // void _disposeVideoControllers() {
  //   _videoController?.dispose();
  //   _chewieController?.dispose();
  //   _videoController = null;
  //   _chewieController = null;
  // }

  void toggleSettingDialog() {
    isSettingDialogOpen = !isSettingDialogOpen;
  }

  void navigateToResultScreen() {
    if (isSettingDialogOpen) {
      Navigator.of(context).pop();
    }
    if (isExitDialogOpen) {
      Navigator.of(context).pop();
    }

    //move to result page
    //to see the what are the keys to pass in arguments for result screen
    //visit static route function in resultScreen.dart
    Navigator.of(context).pushReplacementNamed(
      Routes.result,
      arguments: {
        'numberOfPlayer': widget.numberOfPlayer,
        'myPoints': context.read<QuestionsCubit>().currentPoints(),
        'quizType': widget.quizType,
        'questions': context.read<QuestionsCubit>().questions(),
        'subcategoryMaxLevel': widget.subcategoryMaxLevel,
        'unlockedLevel': widget.unlockedLevel,
        'categoryId': widget.categoryId,
        'subcategoryId': widget.subcategoryId,
        'contestId': widget.contestId,
        'isPlayed': widget.isPlayed,
        'comprehension': widget.comprehension,
        'timeTakenToCompleteQuiz': totalSecondsToCompleteQuiz,
        'hasUsedAnyLifeline': checkHasUsedAnyLifeline(),
        'entryFee': 0,
        'isPremiumCategory': widget.isPremiumCategory,
        'questionsUsingHelp': questionsUsingHelp,
        'numberOfQuestionsWithHelp':
            questionsUsingHelp.values.where((used) => used).length,
      },
    );
  }

  void markLifeLineUsed() {
    if (lifelines[fiftyFifty] == LifelineStatus.using) {
      lifelines[fiftyFifty] = LifelineStatus.used;
    }
    if (lifelines[audiencePoll] == LifelineStatus.using) {
      lifelines[audiencePoll] = LifelineStatus.used;
    }
    if (lifelines[resetTime] == LifelineStatus.using) {
      lifelines[resetTime] = LifelineStatus.used;
    }
    if (lifelines[skip] == LifelineStatus.using) {
      lifelines[skip] = LifelineStatus.used;
    }
  }

  bool checkHasUsedAnyLifeline() {
    var hasUsedAnyLifeline = false;

    for (final lifelineStatus in lifelines.values) {
      if (lifelineStatus == LifelineStatus.used) {
        hasUsedAnyLifeline = true;
        break;
      }
    }
    //
    return hasUsedAnyLifeline;
  }

  //change to next Question

  void changeQuestion() {
    questionAnimationController.forward(from: 0).then((value) {
      //need to dispose the animation controllers
      questionAnimationController.dispose();
      questionContentAnimationController.dispose();
      //initializeAnimation again
      setState(() {
        initializeAnimation();
        currentQuestionIndex++;
        markLifeLineUsed();
      });
      //load content(options, image etc) of question
      questionContentAnimationController.forward();
    });
  }

  //if user has submitted the answer for current question
  bool hasSubmittedAnswerForCurrentQuestion() {
    return context
        .read<QuestionsCubit>()
        .questions()[currentQuestionIndex]
        .attempted;
  }

  Map<String, LifelineStatus> getLifeLines() {
    if (widget.quizType == QuizTypes.quizZone ||
        widget.quizType == QuizTypes.dailyQuiz) {
      return lifelines;
    }
    return {};
  }

  void updateTotalSecondsToCompleteQuiz() {
    final configCubit = context.read<SystemConfigCubit>();
    totalSecondsToCompleteQuiz = totalSecondsToCompleteQuiz +
        UiUtils.timeTakenToSubmitAnswer(
          animationControllerValue: timerAnimationController.value,
          quizTimer: configCubit.quizTimer(widget.quizType),
        );
  }

  //update answer locally and on cloud
  Future<void> submitAnswer(String submittedAnswer) async {
    timerAnimationController.stop(canceled: false);
    if (!context
        .read<QuestionsCubit>()
        .questions()[currentQuestionIndex]
        .attempted) {
      final questionId =
          context.read<QuestionsCubit>().questions()[currentQuestionIndex].id;
      final hasUsedHelp = questionsUsingHelp[questionId] ?? false;

      // تعديل النقاط بناءً على استخدام المساعدة
      final correctScore = hasUsedHelp
          ? 0
          : context
              .read<SystemConfigCubit>()
              .quizCorrectAnswerCreditScore(widget.quizType);
      final wrongScore = hasUsedHelp
          ? 0
          : context
              .read<SystemConfigCubit>()
              .quizWrongAnswerDeductScore(widget.quizType);

      context.read<QuestionsCubit>().updateQuestionWithAnswerAndLifeline(
            questionId,
            submittedAnswer,
            context.read<UserDetailsCubit>().getUserFirebaseId(),
            correctScore,
            wrongScore,
          );
      updateTotalSecondsToCompleteQuiz();
      await timerAnimationController.reverse();
      //change question
      await Future<void>.delayed(
        const Duration(seconds: inBetweenQuestionTimeInSeconds),
      );

      if (currentQuestionIndex !=
          (context.read<QuestionsCubit>().questions().length - 1)) {
        changeQuestion();
        //if quizType is not audio or latex(math or chemistry) then start timer again
        if (widget.quizType == QuizTypes.audioQuestions ||
            widget.quizType == QuizTypes.mathMania) {
          timerAnimationController.value = 0.0;
          await showOptionAnimationController.forward();
        } else {
          await timerAnimationController.forward(from: 0);
        }
      } else {
        navigateToResultScreen();
      }
    }
  }

  //listener for current user timer
  void currentUserTimerAnimationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      submitAnswer('-1');
    } else if (status == AnimationStatus.forward) {
      if (widget.quizType == QuizTypes.audioQuestions) {
        showOptionAnimationController.reverse();
      }
    }
  }

  Widget _buildShowOptionButton() {
    if (widget.quizType == QuizTypes.audioQuestions) {
      return Align(
        alignment: Alignment.bottomCenter,
        child: SlideTransition(
          position: showOptionAnimation.drive<Offset>(
            Tween<Offset>(begin: const Offset(0, 1.5), end: Offset.zero),
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height * (0.025),
              left: MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
              right: MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
            ),
            child: CustomRoundedButton(
              widthPercentage: MediaQuery.of(context).size.width,
              backgroundColor: Theme.of(context).primaryColor,
              buttonTitle: context.tr(showOptionsKey),
              titleColor: Theme.of(context).colorScheme.surface,
              onTap: () {
                if (!showOptionAnimationController.isAnimating) {
                  showOptionAnimationController.reverse();
                  audioQuestionContainerKeys[currentQuestionIndex]
                      .currentState!
                      .changeShowOption();
                  timerAnimationController.forward(from: 0);
                }
              },
              showBorder: false,
              radius: 8,
              height: 40,
              elevation: 5,
              fontWeight: FontWeight.w600,
              textSize: 18,
            ),
          ),
        ),
      );
    }
    return const SizedBox();
  }

  void onTapBackButton() {
    isExitDialogOpen = true;
    showDialog<void>(
      context: context,
      builder: (_) => ExitGameDialog(
        onTapYes: (widget.quizType == QuizTypes.quizZone)
            ? () {
                Navigator.of(context).pop(true);
                Navigator.of(context).pop(true);
              }
            : null,
      ),
    ).then(
      (_) {
        if (!mounted) return; // التحقق من mounted

        if (widget.quizType == QuizTypes.quizZone) {
          try {
            if (widget.subcategoryId == '0' || widget.subcategoryId == '') {
              // التحقق من وجود context
              if (context.mounted) {
                context.read<UnlockedLevelCubit>().fetchUnlockLevel(
                      widget.categoryId,
                      '0',
                    );
              }
            } else {
              // التحقق من وجود context
              if (context.mounted) {
                context.read<SubCategoryCubit>().fetchSubCategory(
                      widget.categoryId,
                    );
              }
            }
          } catch (e) {
            print("Error updating level: $e");
          }
        }
      },
    );
  }

  bool get isSmallDevice => MediaQuery.sizeOf(context).width <= 360;

  Duration get timer =>
      timerAnimationController.duration! -
      timerAnimationController.lastElapsedDuration!;

  String get remaining => (timerAnimationController.isAnimating)
      ? "${timer.inMinutes.remainder(60).toString().padLeft(2, '0')}:${timer.inSeconds.remainder(60).toString().padLeft(2, '0')}"
      : '';

  @override
  Widget build(BuildContext context) {
    final quesCubit = context.read<QuestionsCubit>();

    return BlocListener<UpdateScoreAndCoinsCubit, UpdateScoreAndCoinsState>(
      listener: (context, state) {
        if (state is UpdateScoreAndCoinsFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            timerAnimationController.stop();
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      child: BlocConsumer<QuestionsCubit, QuestionsState>(
        bloc: quesCubit,
        listener: (_, state) {
          if (state is QuestionsFetchSuccess) {
            if (state.questions.isNotEmpty) {
              if (currentQuestionIndex == 0 &&
                  !state.questions[currentQuestionIndex].attempted) {
                if (widget.quizType == QuizTypes.audioQuestions) {
                  for (final _ in state.questions) {
                    audioQuestionContainerKeys.add(
                      GlobalKey<AudioQuestionContainerState>(),
                    );
                  }

                  //
                  showOptionAnimationController.forward();
                  questionContentAnimationController.forward();
                  //add audio question container keys
                }

                //
                else if (widget.quizType == QuizTypes.mathMania) {
                  questionContentAnimationController.forward();
                } else {
                  timerAnimationController.forward();
                  questionContentAnimationController.forward();
                }
              }
            }
          } else if (state is QuestionsFetchFailure) {
            if (state.errorMessage == errorCodeUnauthorizedAccess) {
              showAlreadyLoggedInDialog(context);
            }
          }
        },
        builder: (context, state) {
          if (state is QuestionsFetchInProgress || state is QuestionsIntial) {
            return const Scaffold(
              body: Center(child: CircularProgressContainer()),
            );
          }
          if (state is QuestionsFetchFailure) {
            return Scaffold(
              appBar: const QAppBar(title: SizedBox(), roundedAppBar: false),
              body: Center(
                child: ErrorContainer(
                  showBackButton: true,
                  errorMessage:
                      convertErrorCodeToLanguageKey(state.errorMessage),
                  showRTryButton: widget.showRetryButton &&
                      convertErrorCodeToLanguageKey(state.errorMessage) !=
                          dailyQuizAlreadyPlayedKey,
                  onTapRetry: _getQuestions,
                  showErrorImage: true,
                ),
              ),
            );
          }

          return PopScope(
            canPop: false,
            onPopInvokedWithResult: (didPop, _) {
              if (didPop) return;
              onTapBackButton();
            },
            child: Scaffold(
              body: Stack(
                children: [
                  // خلفية الصفحة بتصميج محسن
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).primaryColor.withOpacity(0.8),
                          Colors.white,
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: const [0.2, 0.9],
                      ),
                    ),
                  ),

                  // زخارف الخلفية
                  Positioned(
                    top: -50,
                    right: -50,
                    child: Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).primaryColor.withOpacity(0.2),
                      ),
                    ),
                  ),

                  Positioned(
                    bottom: -80,
                    left: -80,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).primaryColor.withOpacity(0.15),
                      ),
                    ),
                  ),

                  // شريط العنوان المخصص
                  Positioned(
                    top: MediaQuery.of(context).padding.top + 10,
                    left: 16,
                    right: 16,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // زر الرجوع بتصميج محسن
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons.arrow_back_ios_new_rounded,
                              color: Theme.of(context).primaryColor,
                              size: 20,
                            ),
                            onPressed: onTapBackButton,
                          ),
                        ),

                        // Settings Button
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.settings,
                              color: Colors.white,
                              size: 24,
                            ),
                            onPressed: () {
                              showQuickSettingsDialog(context);
                            },
                          ),
                        ),

                        // Timer Container - تصميم محسن للتايمر
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.2),
                                blurRadius: 10,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(8),
                          child: widget.quizType == QuizTypes.funAndLearn
                              ? Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 15,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Theme.of(context).primaryColor,
                                        Theme.of(context)
                                            .primaryColor
                                            .withOpacity(0.8),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: AnimatedBuilder(
                                    animation: timerAnimationController,
                                    builder: (context, _) => Text(
                                      remaining,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.3),
                                      width: 2,
                                    ),
                                  ),
                                  child: TextCircularTimer(
                                    animationController:
                                        timerAnimationController,
                                    arcColor: Theme.of(context).primaryColor,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onTertiary
                                        .withOpacity(0.2),
                                    size: 50.0,
                                    strokeWidth: 6.0,
                                  ),
                                ),
                        ),

                        // Question Counter
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15.0, vertical: 8.0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: BlocBuilder<QuestionsCubit, QuestionsState>(
                            builder: (context, state) {
                              if (state is QuestionsFetchSuccess) {
                                return Text(
                                  "${currentQuestionIndex + 1}/${state.questions.length}",
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                );
                              }
                              return const SizedBox();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  // محتوى الأسئلة
                  Padding(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top + 80,
                      left: 16,
                      right: 16,
                      bottom: 80,
                    ),
                    child: Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Column(
                          children: [
                            Expanded(
                              child: QuestionsContainer(
                                quizType: widget.quizType,
                                answerMode: context
                                    .read<SystemConfigCubit>()
                                    .answerMode,
                                lifeLines: getLifeLines(),
                                timerAnimationController:
                                    timerAnimationController,
                                topPadding: 20,
                                hasSubmittedAnswerForCurrentQuestion:
                                    hasSubmittedAnswerForCurrentQuestion,
                                questions:
                                    context.read<QuestionsCubit>().questions(),
                                submitAnswer: submitAnswer,
                                questionContentAnimation:
                                    questionContentAnimation,
                                questionScaleDownAnimation:
                                    questionScaleDownAnimation,
                                questionScaleUpAnimation:
                                    questionScaleUpAnimation,
                                questionSlideAnimation: questionSlideAnimation,
                                currentQuestionIndex: currentQuestionIndex,
                                questionAnimationController:
                                    questionAnimationController,
                                questionContentAnimationController:
                                    questionContentAnimationController,
                                level: widget.level,
                              ),
                            ),

                            // زر "اشرح لي" لعرض ملاحظات السؤال
                            BlocBuilder<QuestionsCubit, QuestionsState>(
                              builder: (context, state) {
                                if (state is QuestionsFetchSuccess) {
                                  final currentQuestion =
                                      state.questions[currentQuestionIndex];

                                  // إضافة logs لتتبع الفيديوهات
                                  print(
                                      '📋 [QUIZ LOG] Current Question Index: $currentQuestionIndex');
                                  print(
                                      '📋 [QUIZ LOG] Question ID: ${currentQuestion.id}');
                                  print(
                                      '📋 [QUIZ LOG] Question Text: ${currentQuestion.question}');
                                  print(
                                      '📋 [QUIZ LOG] Video URL: ${currentQuestion.videoUrl}');
                                  print(
                                      '📋 [QUIZ LOG] Video ID: ${currentQuestion.videoId}');
                                  print(
                                      '📋 [QUIZ LOG] Has Video: ${currentQuestion.hasVideo}');
                                  print(
                                      '📋 [QUIZ LOG] Video Start Time: ${currentQuestion.videoStartTime}');

                                  final hasNote =
                                      currentQuestion.note != null &&
                                          currentQuestion.note!.isNotEmpty;
                                  final hasVideo = currentQuestion.hasVideo;
                                  final hasExplanation = hasNote || hasVideo;

                                  print('📋 [QUIZ LOG] Has Note: $hasNote');
                                  print(
                                      '📋 [QUIZ LOG] Has Explanation: $hasExplanation');

                                  // تحقق من حجم الشاشة
                                  final screenWidth =
                                      MediaQuery.of(context).size.width;
                                  final isSmallScreen = screenWidth < 400;

                                  return Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12.0,
                                      horizontal: 8.0,
                                    ),
                                    child: Column(
                                      children: [
                                        // العنوان
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 10),
                                          child: Text(
                                            "أدوات المساعدة",
                                            style: TextStyle(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ),

                                        // استخدام Wrap بدلاً من Row لتجنب تجاوز الحدود
                                        Wrap(
                                          alignment: WrapAlignment.center,
                                          spacing:
                                              10, // المسافة الأفقية بين العناصر
                                          runSpacing:
                                              10, // المسافة الرأسية بين الصفوف
                                          children: [
                                            if (hasExplanation)
                                              _buildHelpButton(
                                                icon: Icons.lightbulb_outline,
                                                title: "اشرح لي",
                                                color: Theme.of(context)
                                                    .primaryColor,
                                                onTap: () =>
                                                    _showExplanationDialog(
                                                        context,
                                                        currentQuestion),
                                                isSmallScreen: isSmallScreen,
                                              ),
                                            _buildHelpButton(
                                              icon: Icons.remove_circle_outline,
                                              title: "احذف إجابتين",
                                              color: Colors.orange,
                                              onTap: _eliminateWrongAnswers,
                                              isSmallScreen: isSmallScreen,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // أزرار التنقل بين الأسئلة
                  Positioned(
                    bottom: 20,
                    left: 20,
                    right: 20,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // زر السؤال السابق
                        if (currentQuestionIndex > 0)
                          Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.arrow_back_ios_new_rounded,
                                color: Colors.white,
                              ),
                              onPressed: () {
                                // التنقل للسؤال السابق
                                if (currentQuestionIndex > 0) {
                                  setState(() {
                                    currentQuestionIndex--;
                                  });
                                }
                              },
                            ),
                          )
                        else
                          const SizedBox(width: 48),

                        // زر السؤال التالي
                        if (true)
                          Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: IconButton(
                              icon: Icon(
                                currentQuestionIndex ==
                                        (context
                                                .read<QuestionsCubit>()
                                                .questions()
                                                .length -
                                            1)
                                    ? Icons.check_circle_outline_rounded
                                    : Icons.arrow_forward_ios_rounded,
                                color: Colors.white,
                              ),
                              onPressed: () {
                                // التنقل للسؤال التالي أو إنهاء الاختبار
                                if (currentQuestionIndex ==
                                    (context
                                            .read<QuestionsCubit>()
                                            .questions()
                                            .length -
                                        1)) {
                                  navigateToResultScreen();
                                } else {
                                  changeQuestion();
                                }
                              },
                            ),
                          )
                        else
                          // ignore: dead_code
                          const SizedBox(width: 48),
                      ],
                    ),
                  ),

                  // زر إظهار الخيارات
                  _buildShowOptionButton(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // دالة جديدة لإنشاء أزرار المساعدة بتنسيق موحد
  Widget _buildHelpButton({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    bool isLoading = false,
    bool isWide = false,
    bool isSmallScreen = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.zero,
        padding: EdgeInsets.symmetric(
            vertical: 12,
            horizontal:
                isSmallScreen ? (isWide ? 16 : 10) : (isWide ? 24 : 16)),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color,
              color.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: isLoading
            ? SizedBox(
                width: isSmallScreen ? 16 : 18,
                height: isSmallScreen ? 16 : 18,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: isSmallScreen ? 1.5 : 2,
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: isSmallScreen ? 16 : 18,
                    ),
                  ),
                  SizedBox(width: isSmallScreen ? 4 : 8),
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  void _showExplanationDialog(BuildContext context, Question question) {
    print('💡 [EXPLANATION LOG] فتح نافذة الشرح');
    print('💡 [EXPLANATION LOG] Question ID: ${question.id}');
    print('💡 [EXPLANATION LOG] Has Video: ${question.hasVideo}');
    print('💡 [EXPLANATION LOG] Video URL: ${question.videoUrl}');
    print('💡 [EXPLANATION LOG] Video ID: ${question.videoId}');
    print(
        '💡 [EXPLANATION LOG] Has Note: ${question.note != null && question.note!.isNotEmpty}');

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ExplanationDialog(
          question: question,
          onDialogOpened: () {
            print('💡 [EXPLANATION LOG] تم فتح نافذة الشرح - إيقاف المؤقت');
            // إيقاف المؤقت عند فتح نافذة الشرح
            timerAnimationController.stop();
          },
          onDialogClosed: () {
            print('💡 [EXPLANATION LOG] تم إغلاق نافذة الشرح - استكمال المؤقت');
            // استكمال المؤقت عند إغلاق نافذة الشرح
            if (mounted && !hasSubmittedAnswerForCurrentQuestion()) {
              timerAnimationController.forward();
            }
          },
        );
      },
    );

    // تحديث حالة استخدام المساعدة للسؤال الحالي
    final questionId = question.id ?? '';
    if (questionId.isNotEmpty) {
      questionsUsingHelp[questionId] = true;
    }
  }

  void _eliminateWrongAnswers() {
    final currentQuestion =
        context.read<QuestionsCubit>().questions()[currentQuestionIndex];

    // التحقق من الشروط الأساسية
    if (currentQuestion.answerOptions == null ||
        currentQuestion.answerOptions!.length <= 2 ||
        currentQuestion.attempted ||
        currentQuestion.correctAnswer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('لا يمكن استخدام هذه الميزة الآن'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
      return;
    }

    try {
      // نسخ قائمة الخيارات الحالية
      final allOptions =
          List<AnswerOption>.from(currentQuestion.answerOptions!);

      // الحصول على الإجابة الصحيحة
      final correctAnswerId = AnswerEncryption.decryptCorrectAnswer(
        correctAnswer: currentQuestion.correctAnswer!,
        rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
      );

      // التأكد من وجود الإجابة الصحيحة في القائمة
      final correctOption = allOptions.firstWhere(
        (option) => option.id == correctAnswerId,
      );

      // تجميع الإجابات الخاطئة فقط
      final wrongAnswers = allOptions
          .where((option) => option.id != correctAnswerId)
          .toList()
        ..shuffle();

      // التحقق من وجود إجابات خاطئة كافية
      if (wrongAnswers.isEmpty) {
        throw Exception('لا توجد إجابات خاطئة كافية');
      }

      // إنشاء قائمة الإجابات النهائية وترتيبها عشوائياً
      final remainingAnswers = [
        correctOption,
        wrongAnswers.first,
      ]..shuffle(); // هنا نقوم بترتيب القائمة عشوائياً

      // تسجيل استخدام المساعدة
      final questionId = currentQuestion.id ?? '';
      if (questionId.isNotEmpty) {
        questionsUsingHelp[questionId] = true;
      }

      // تحديث السؤال
      setState(() {
        currentQuestion.answerOptions?.clear();
        currentQuestion.answerOptions?.addAll(remainingAnswers);
        lifelines[fiftyFifty] = LifelineStatus.used;
      });

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم حذف إجابتين خاطئتين'),
          backgroundColor: Theme.of(context).primaryColor,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('حدث خطأ أثناء محاولة حذف الإجابات'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }
}
