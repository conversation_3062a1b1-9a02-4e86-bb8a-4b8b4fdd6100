# ميزة الشرح بالفيديو للأسئلة

## 📋 نظرة عامة

تم إضافة ميزة جديدة تتيح عرض شرح بالفيديو للأسئلة في التطبيق. هذه الميزة تدعم عرض فيديوهات يوتيوب مع إمكانية البدء من وقت محدد.

## 🎯 الميزات المضافة

### 1. **دعم الفيديو في نموذج Question**
- إضافة حقل `videoUrl` لحفظ رابط الفيديو
- دوال مساعدة لاستخراج معرف الفيديو والوقت المحدد
- دعم روابط يوتيوب بأشكال مختلفة

### 2. **مكون عرض الفيديو**
- `VideoQuestionWidget`: مكون متكامل لعرض فيديوهات يوتيوب
- دعم التحكم في التشغيل والإيقاف
- إمكانية البدء من وقت محدد
- واجهة مستخدم جميلة مع معالجة الأخطاء

### 3. **نافذة الشرح بالتبويبات**
- `ExplanationDialog`: نافذة شرح متطورة
- تبويبين: شرح نصي وشرح بالفيديو
- عرض ذكي حسب المحتوى المتوفر

### 4. **تحديث واجهات المستخدم**
- زر "اشرح لي" في صفحة الاختبار
- زر الشرح في صفحة مراجعة الإجابات
- عرض الزر فقط عند توفر شرح نصي أو فيديو

## 🔧 التفاصيل التقنية

### نموذج البيانات

```dart
class Question {
  final String? videoUrl;  // رابط الفيديو الجديد
  
  // دوال مساعدة
  String? get videoId;           // استخراج معرف الفيديو
  int? get videoStartTime;       // استخراج الوقت المحدد
  bool get hasVideo;             // التحقق من وجود فيديو
}
```

### أشكال الروابط المدعومة

```
// رابط عادي
https://www.youtube.com/watch?v=D9vDlUWcP9Y

// رابط بوقت محدد
https://www.youtube.com/watch?v=D9vDlUWcP9Y&t=1800s

// رابط مختصر
https://youtu.be/D9vDlUWcP9Y

// رابط مختصر بوقت محدد
https://youtu.be/D9vDlUWcP9Y?t=1800
```

### مكون عرض الفيديو

```dart
VideoQuestionWidget(
  question: question,
  height: 200,
  autoPlay: false,
  showControls: true,
)
```

### نافذة الشرح

```dart
ExplanationDialog(question: question)
```

## 📱 تجربة المستخدم

### في صفحة الاختبار:
1. يظهر زر "اشرح لي" إذا كان هناك شرح نصي أو فيديو
2. النقر على الزر يفتح نافذة الشرح بالتبويبات
3. يمكن التنقل بين الشرح النصي والفيديو

### في صفحة مراجعة الإجابات:
1. زر شرح إضافي بجانب أزرار الحفظ والإبلاغ
2. نفس نافذة الشرح بالتبويبات
3. عرض سلس ومتجاوب

## 🔄 تحديثات الباك إند المطلوبة

يجب على الباك إند إرسال حقل `video_url` في استجابة الأسئلة:

```json
{
  "question_id": "123",
  "question": "ما هو...",
  "note": "شرح نصي للسؤال",
  "video_url": "https://www.youtube.com/watch?v=D9vDlUWcP9Y&t=1800s",
  "image": "...",
  "audio": "...",
  // باقي الحقول
}
```

## 🎨 التصميم

### الألوان والأيقونات:
- أيقونة الشرح: `Icons.lightbulb_outline`
- أيقونة الفيديو: `Icons.play_circle_outline`
- أيقونة النص: `Icons.text_snippet_outlined`

### التبويبات:
- تبويب الشرح النصي: يظهر فقط إذا كان هناك `note`
- تبويب الفيديو: يظهر فقط إذا كان هناك `videoUrl` صحيح

## 🚀 الاستخدام

### إضافة فيديو لسؤال:
```dart
Question(
  question: "ما هو الحل؟",
  note: "شرح نصي مفصل",
  videoUrl: "https://www.youtube.com/watch?v=D9vDlUWcP9Y&t=1800s",
  // باقي الخصائص
)
```

### عرض نافذة الشرح:
```dart
showDialog(
  context: context,
  builder: (context) => ExplanationDialog(question: question),
);
```

## 📦 المكتبات المستخدمة

- `youtube_player_flutter: ^9.1.1` (موجودة مسبقاً)
- `google_fonts` (موجودة مسبقاً)

## ✅ الاختبار

تم اختبار الميزة على:
- عرض الفيديو بشكل صحيح
- استخراج معرف الفيديو من روابط مختلفة
- البدء من الوقت المحدد
- التبديل بين التبويبات
- معالجة الأخطاء

## 🔮 تحسينات مستقبلية

1. **دعم منصات فيديو أخرى** (Vimeo, etc.)
2. **تحميل الفيديو للمشاهدة بدون إنترنت**
3. **إحصائيات مشاهدة الفيديوهات**
4. **تقييم جودة الشرح**
5. **ترجمات تلقائية للفيديوهات**

## 📞 الدعم

في حالة وجود مشاكل أو أسئلة، يرجى التواصل مع فريق التطوير.
