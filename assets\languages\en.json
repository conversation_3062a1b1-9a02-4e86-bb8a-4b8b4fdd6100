{"aboutQuizApp": "About", "aboutUs": "About Us", "account": "Account", "account-exists-with-different-credential": "Account already exist", "accountDeletedSuccessfully": "Account deleted successfully", "accountHasBeenDeactive": "your account has been deactivated! please contact admin", "achievedMastery": "You've achieved mastery in the quiz.\nKeep up the outstanding work!", "addPlayer": "PLAYER", "allExamsCompleteLbl": "All Exams Are Complete", "allTimeLbl": "All time", "alreadyAccountLbl": "Already have an account?", "alreadyInExam": "You are already in exam room.Can't give exam", "alreadyLoggedIn": "Already logged in other device", "andLbl": "and", "anotherOpponentBtn": "ANOTHER OPPONENT", "appLink": "App Link", "appUnderMaintenance": "We apologize for the inconvenience, but we are performing some maintenance. We will back soon", "attemptedLbl": "Attempted", "audioQuestions": "Audio Questions", "audioQuizResult": "Audio Quiz Result", "back": "Back", "badges": "Badges", "battleOfTheDay": "Battle’s of the day", "battlePreparingLbl": "Battle Begins...", "battleQuiz": "1 v/s 1 Battle", "battleStatistics": "Battle Statistics", "bestOfLuckLbl": "Best Of Luck", "betterNextLbl": "Better luck next time", "bookmarkLbl": "Bookmarks", "bookmarkQuizResult": "Bookmark Quiz Result", "botNameLbl": "Robot", "boughtCoins": "Bought Coins", "boughtSuccess": "bought successfully", "buyCoins": "BUY COINS", "byUnlocking": "By Unlocking", "cameraLbl": "Camera", "canNotMakeRequest": "You have already made a payment request. Please wait for 48 hours after you made the previous request", "canNotStartGame": "To begin the game, a minimum of one additional player is required.", "cancel": "Cancel", "cantUseFiftyFiftyAfterPoll": "50/50 unavailable after Audience poll.", "cantUsePollAfterFiftyFifty": "Audience poll unavailable after 50/50.", "challengeYourselfLbl": "Challenge yourself", "changePayoutMethod": "Change payout method", "chat": "Cha<PERSON>", "choosePhoto": "Choose from photos", "close": "Close", "closerToMastery": "Getting closer to mastery!\nKeep going!", "cnPwdLbl": "Confirm Password", "cnPwdNotMatchMsg": "Confirm Password not match", "coinHistory": "Coin History", "coinStore": "Coin Store", "coinsBoughtSuccess": "Coins bought successfully", "coinsLbl": "Coins", "coinsUnlockingByBadge": "coin(s) by unlocking this badge", "coinsWillBeDeducted": "Coins will be deducted", "collectedBadges": "Collected Badges", "completeAllQueLbl": "Completed all questions", "completeSubTitle": "Complete 10 game Complete 10 game ", "completedIn": "Completed In", "completedLbl": "Completed", "comprehensiveLbl": "Comprehensive", "confirmPasswordRequired": "Confirm Password is Required", "congratulationsLbl": "Congratulations", "contactUs": "Contact Us", "contest": "Contest", "contestAlreadyPlayed": "Already played", "contestLbl": "Contest", "contestLeaderBoardLbl": "Contest LeaderBoard", "continueLbl": "Continue", "copyCodeLbl": "Copy\nCode", "correct": "Correct", "correctAndLbl": "Correct Answer ", "correctAnswerToUnlock": "correct answer to unlock", "correctAnswersLbl": "Correct Answers", "countryLbl": "Country", "createRoom": "Create Room", "creatingLbl": "Create", "creatingLoadingLbl": "Creating..", "creator": "CREATOR", "cropperLbl": "C<PERSON>per", "currentCoins": "Current Coins", "currentlyNotAvailable": "Currently not available", "dailyAdsDesc": "instant Coins", "dailyAdsLimitExceeded": "Daily Ads Limit Exceeded", "dailyAdsTitle": "Watch Video Ads & Earn Coins", "dailyLbl": "Today's", "dailyQuiz": "Daily Quiz", "dailyQuizAlreadyPlayed": "Already played", "dailyQuizResult": "Daily Quiz Result", "darkTheme": "Dark Theme", "dataNotFound": "Unable to find data", "defaultErrorMessage": "Something went wrong. Please try again later", "defeatLbl": "Defeat", "deleteAccConfirmation": "Are you sure you want to Delete your account?", "deleteAccountLbl": "Delete Account", "deletingAccount": "Deleting Account...", "desAudioQuestions": "Quiz with audio", "desBattleQuiz": "Battle with one on one", "desContest": "Play quiz contest", "desDailyQuiz": "Daily basic new quiz game", "desExam": "Give exam", "desFunAndLearn": "it's like a Comprehension game", "desGroupPlay": "It's a group quiz battle", "desGuessTheWord": "Fun vocabulary game", "desMathMania": "It's math quiz", "desQuizZone": "Select your favorite zone to play", "desTrueAndFalse": "Quiz with True / False format", "desTrueFalse": "True|False Question", "description1": "Challenge yourself with a variety of quizzes", "description2": "Think you're a genius? Prove it with our challenging quizzes!", "description3": "Discover Exciting Quiz Categories and Improve Your Skills", "draw": "Draw", "earnedLbl": "Earned", "editProfile": "Edit Profile", "email-already-in-use": "Email already in use", "emailAddress": "Email Address", "emailLbl": "Email", "emailRequiredMsg": "Email is Required", "emailVerify": "Verification email sent to", "emojis": "Emojis", "endsOnLbl": "Ends On", "enterCodeLbl": "Enter Code", "enterEmailLbl": "Enter email address", "enterExamKey": "Enter exam key", "enterExamKeyLbl": "Please enter the exam key", "enterExamLbl": "Enter in Exam", "enterNameLbl": "Enter your name", "enterNumberLbl": "Enter Your \nMobile Number", "enterOtpMsg": "Please enter Otp", "enterReason": "Enter your reason", "enterReferralCodeLbl": "Enter referral code", "enterRoomCodeHere": "Enter Room Code to Join <PERSON>", "enterValidEmailMsg": "Please enter valid email", "enterValidExamKey": "Please enter valid Exam Key", "enterValidNameMsg": "Please enter valid name", "enterroomerrorMSG": "Please Enter Code to join battle", "entryAmountLbl": "Entry Coins", "entryCoinsForBattle": "Entry Coins for Battle", "entryFeesLbl": "Entry Fees", "entryLbl": "Entry", "everyOneLeftLbl": "Everyone left the game", "exam": "Exam", "examDuration": "<PERSON><PERSON>", "examResult": "<PERSON><PERSON>", "examRules": "Exam Rules", "excellentWork": "Excellent work!", "exitLbl": "Exit", "failedToGetAppUrl": "Failed to get app url, Please update manually", "fantasticJob": "Wow, fantastic job!", "fileUploadFail": "Unable to upload image", "fillAllData": "Please fill all data", "findingOpponentLbl": "Finding opponent...", "fontSizeDescText": "you can modify the font size of the questions displayed in the quiz play area from here", "fontSizeLbl": "Play Area Font Size", "forgotPwdLbl": "Forgot Password?", "foundOpponentLbl": "Found opponent", "funAndLearn": "Fun 'N' Learn", "funAndLearnResult": "Fun And Learn Result", "gameStarted": "Game already started", "get": "Get", "getFreeCoins": "Get Free Coins", "getReadyLbl": "Get Ready For Battle", "getStarted": "Get Started", "goBAckLbl": "Go Back", "goodEffort": "Good effort!", "greatJob": "Great job!", "groupBattleResult": "Group Battle Result", "groupPlay": "Group Battle", "guessTheWord": "Guess The Word", "guessTheWordResult": "Guess The Word Result", "guest": "Guest", "guestMode": "To access this feature you need to Login!!", "haveNotCompletedExam": "Have not completed any exam yet", "hello": "Hello", "helloGuest": "Hello Guest", "hint": "Hint", "homeBtn": "Home", "howToPlayLbl": "How to Play", "howWorksLbl": "How it works?", "iAgreeWithExamRules": "I agree with Exam Rules", "iHaveInviteCode": "I have an invite code", "inAppPurchaseUnavailable": "In-app purchase is not available", "incorrect": "Incorrect", "incorrectAnswersLbl": "Incorrect Answers", "invalid-credential": "Credential is invalid", "invalid-email": "<PERSON><PERSON> is invalid", "invalid-verification-code": "Verification code is invalid", "invalid-verification-id": "Verification id is invalid", "invalid-phone-number": "Please Enter Valid Phone Number", "invalidHash": "Invalid hash", "inviteFriendsLbl": "Invite Friends", "joinLbl": "Join", "joinRoom": "Join Room", "joiningLoadingLbl": "Joining..", "keepAccount": "Keep, Account", "keepGoing": "Just a little more and you'll reach the top! Keep going!", "keepLearning": "Keep learning and trying again!", "language": "Language", "leaderboardLbl": "Leaderboard", "letsPlay": "Let's Play", "letsStart": "Let's Start", "letsplay": "Let's play the quiz", "levelLbl": "Level", "levelLocked": "Level is locked", "levels": "Levels", "lifeLineUsed": "Already used this lifeline", "lightTheme": "Light Theme", "liveChatLbl": "Live Chat", "liveLbl": "Ongoing", "loginLbl": "Log In", "loginSocialMediaLbl": "Connect with one of the following Option", "logoutDialogLbl": "Are you sure you want to logout your account?", "logoutLbl": "Logout!", "looserLbl": "<PERSON><PERSON><PERSON>", "lost": "Lost", "makeRequest": "Make Request", "makingProgress": "You're making progress, keep it up!", "mark": "<PERSON>", "markQuestionsLbl": "Marks Questions", "matchDrawLbl": "MATCH DRAW", "mathMania": "Math Mania", "mathQuizResult": "<PERSON> Man<PERSON>t", "messages": "Messages", "minLbl": "min", "minimumRedeemableAmount": "Minimum redeemable amount is", "mobileNumberLbl": "Mobile Number", "monthLbl": "Monthly", "moreThanZeroCoins": "Please enter more than zero coins", "myRank": "My Rank", "myScoreLbl": "My Quiz Score", "nameLbl": "Name", "needMore": "Need more", "nextLevelBtn": "Next Level", "noAccountLbl": "Don't have an account? ", "noBookmarkQueLbl": "No bookmarked questions", "noBtn": "No", "noCoinsMsg": "No enough coins to play contest", "noContest": "No contest", "noExamForToday": "No exam for today", "noInterNetSnackBar": "Please check your connection again, or connect to Wi-Fi", "noInternet": " No internet connection found. check your connection or try again.", "noLeaderboardLbl": "No Leaderboard", "noLevelsLbl": "No Levels", "noMatchesPlayed": "No matches played, yet", "noPastGameLbl": "no past game", "noProducts": "No products available", "noRewards": "No Rewards", "noStatisticsDescLbl": "Let your brilliance shine!\nParticipate in the quiz.", "noStatisticsLbl": "No Statistics Available", "noTransactions": "No Transactions", "noUpcomingContest": "No upcoming contest", "notAvailable": "Not Available", "notEditMailLbl": "Can't edit email", "notEditNumberMsg": "Can't edit mobile number", "notEnoughCoins": "Not enough coins", "notEnoughCoinsToRedeemAmount": "You don't have enough coins to redeem this amount", "notPlayedContest": "Not played any contest", "notesLbl": "Notes", "notesNotAvailable": "Notes not available", "notificationLbl": "Notifications", "obtainedMarks": "Obtained Marks", "offerLbl": "Offer", "okayLbl": "Okay", "oneToOneLbl": "ONE TO ONE", "operation-not-allowed": "Operation not allowed for this credential", "opponentLeftLbl": "Opponent left the game", "opponentNotFoundLbl": "No opponent detected.\nRetry or play against the bot.", "orLbl": "OR", "otpNotMatch": "Otp does Not Match", "otpNotMatchMsg": "enter valid Otp", "otpSendLbl": "Enter the 6 digit code sent to", "otpVerificationLbl": "OTP\n Verification", "passwordRequired": "Password is Required", "pastLbl": "Finished", "payment": "Payment", "payoutInputHintText": "00", "payoutMethod": "Payout method", "pending": "Pending", "phoneInputHintText": " ************", "phoneNumber": "Phone Number", "photoLibraryLbl": "Gallery", "playAgainBtn": "Play Again", "playBookmarkBtn": "Play Bookmark", "playDifferentZone": "Play Different Zone", "playLbl": "Play", "playWithBotLbl": "Play with <PERSON><PERSON>", "playWithFrdLbl": "Play With Friend", "played": "Played", "playedBattle": "Played Battle", "playedContest": "Played Contest", "playedGroupBattle": "Played Group Battle", "player2": "Player 2", "player3": "Player 3", "player4": "Player 4", "playersLbl": "Players", "playingLbl": "Playing", "playnowLbl": "Play Now", "pleaseAcceptExamRules": "Please accept exam rules", "pleaseFillAllData": "Please fill all data", "pleaseSelectCategory": "Please select category", "plsEnterTheCoins": "Please Enter the Coins", "privacyPolicy": "Privacy Policy", "privateRoomLbl": "PRIVATE ROOM", "productsFetchedFailure": "Failed to get products", "profileLbl": "Profile", "profileName": "Profile Name", "profilePhotoLbl": "Profile Photo", "provideGuestDetails": "Provide Email or Mobile Details", "publicRoomLbl": "PUBLIC ROOM", "purchaseError": "Unable to make a purchase", "pwdLbl": "Password", "pwdLengthMsg": "password should be more then 6 char long", "pwdResetLinkLbl": "Password reset link has been sent to your mail", "quesLbl": "<PERSON><PERSON>", "questionDetails": "Question Details", "questionLbl": "Question", "questions": "Questions", "questionsAttemptedLbl": "Questions Attempted", "quizDetails": "Quiz Details", "quizExitLbl": "Are you sure you want to quit the quiz?", "quizFanLbl": "Quiz Fan", "quizLbl": "The Flutter Quiz App", "quizResultLbl": "Quiz Result", "quizZone": "Quiz Zone", "randomBattleResult": "Random Battle Result", "randomLbl": "Random Battle", "rankLbl": "Rank", "rateUsLbl": "Rate Us", "receiveOtpLbl": "you'll Receive a 6 digit code to verify next.", "redeemNow": "Redeem Now", "redeemRequest": "Redeem Request", "redeemableAmount": "Redeemable Amount", "referAndEarn": "Refer your Friends \nand Earn", "referCodeCopyMsg": "Referral code copied", "referFrdLbl": "Invite your friends to join and earn coins as reward.", "referralCodeLbl": "Referral Code", "referredCodeToFriend": "Referred Code To Friend", "regSubtitle": "Enter your phone number to verify your account", "registration": "Registration", "removeAds": "Remove Ads", "removeAdsBoughtSuccess": "Remove Ads Bought Successfully", "reportQuestion": "Report Question", "request": "Request", "requestOtpLbl": "Request OTP", "requesting": "Requesting...", "requires-recent-login": "To delete account, sign-in again and delete account immediately.", "resendBtn": "Resend OTP", "resendSnackBar": "Request new OTP after 60 seconds", "resetEnterEmailLbl": "Enter the email address associated with your account", "resetLbl": "Resend Code in", "resetPwdLbl": "Reset Password", "restorePurchaseProducts": "Restore Products", "resultLbl": "RESULT", "retryLbl": "Try Again", "reversedByAdmin": "Reversed By <PERSON><PERSON>", "reviewAnsBtn": "Review Answers", "reviewAnswerLbl": "Review Answer", "rewardByScratchingCard": "Reward By Scratching Card", "rewardsLbl": "Rewards", "roomAlreadyCreated": "Room already created", "roomCodeInvalid": "Room code is invalid", "roomCodeLbl": "Room code", "roomDelete": "Are you sure that you want to delete this battle room?", "roomDeletedOwnerLbl": "Room deleted by owner", "roomIsFull": "Room is full", "save": "Save", "scoreLbl": "Score", "scratchHere": "Scratch Here!", "selectAllValues": "Please select all values", "selectCategory": "Select Category", "selectNoQusLbl": "Select Number Of Questions", "selectPayoutOption": "Select payout option", "selectProfileLbl": "Please select profile photo", "selectProfilePhotoLbl": "Select Avatar", "selectSubCategory": "Select Subcategory", "selectTimeLbl": "Select Time Period In Minutes", "selfChallenge": "Self Challenge", "selfChallengeLbl": "Self Challenge Mode", "selfChallengeResult": "Self Challenge Result", "selfExamZone": "Self Exam Zone", "settingLbl": "Settings", "shareAppLbl": "Share App", "shareNowLbl": "Share Now", "shareRoomCodeLbl": "Share this room code to friends \n and ask them to join", "shareRoomCodeRndLbl": "Share this room code to friend \n and ask them to join", "shareScoreBtn": "Share Your Score", "showAdsLbl": "Would like to watch ad to get more coins?", "showLess": "Show Less", "showOptions": "Show Options", "signUpLbl": "Sign Up", "skip": "<PERSON><PERSON>", "soundLbl": "Sound", "startLbl": "Start", "statisticsLabel": "Statistics", "stayLoggedLbl": "Stay logged in!", "step_1": "STEP 1", "step_1_desc": "Invite your friends to sign up", "step_1_title": "Sign Up", "step_2": "STEP 2", "step_2_desc": "Your friends download app", "step_2_title": "Download app", "step_3": "STEP 3", "step_3_desc": "You and your friends get rewarded", "step_3_title": "Get Re<PERSON>s", "steps": "Steps", "storeLbl": "Store", "subCategoriesLbl": "Sub Categories", "categoriesLbl": "Categories", "submitBtn": "Submit", "submittingButton": "Submitting", "successfullyRequested": "Successfully requested", "termAgreement": "By SigningUp/Logging in, You agree to our", "termOfService": "Terms of Service", "termsAndConditions": "Terms & Conditions", "theme": "Theme", "theyWillGet": "They will get", "title1": "Test Your Knowledge", "title2": "The Ultimate Quiz Challenge!", "title3": "Get Ready to Quiz", "toGetCoins": "to get coins.", "total": "Total", "totalCoins": "Total Coins", "totalEarnings": "Total Earnings", "totalQuestions": "Total Questions", "totalRewardsEarned": "Total Rewards Earned", "trackRequest": "Track Request", "transaction": "Transaction", "trueAndFalse": "True / False", "truefalse": "True | False", "truefalseQuizResult": "True False Quiz Result", "unAttemptedLbl": "Un-Attempted", "unabaleToCreateRoom": "Unable to create room", "unableToFindRoom": "Unable to find room", "unableToJoinRoom": "Unable to join room", "unableToSubmitAnswer": "Unable to submit answer", "unauthorizedAccess": "Unauthorized access", "unlockLbl": "Unlock", "unlockPremiumDescription": "Double your Coins and achieve a higher Score.", "unlockedLbl": "Unlocked", "upcomingLbl": "Upcoming", "update": "Update", "updateApplication": "There is an update available. Please update to use this app", "updateBookmarkFailure": "Unable to update bookmark status", "updateLbl": "Update", "updatingLbl": "Updating", "uploadProfilePictureLbl": "uploadProfilePicture", "uploadingBtn": "Uploading...", "useLbl": "Use", "useMyReferral": "Use my referral code", "used5050lifeline": "Used 50-50 lifeline", "usedAudiencePolllifeline": "Used Audience-poll lifeline", "usedHintLifeline": "Used Hint lifeline", "usedReferCode": "Used Refer Code", "usedResetTimerlifeline": "Used Reset-timer lifeline", "usedSkiplifeline": "Used Skip lifeline", "user-disabled:": "Account is disabled", "user-not-found": "User not found", "userLoginLbl": "User Login", "validEmail": "enter valid email", "validMobMsg": "Please enter valid mobile number", "verifyEmail": "Please verify your email", "vibrationLbl": "Vibration", "victoryLbl": "Victory", "viewAll": "View all", "viewAllRules": "View all rules", "viewMore": "View More", "vsLbl": "VS", "waitGameWillStartLbl": "Please wait, game will start soon.", "waitOtherComplete": "Wait For Other to complete...", "waitingLbl": "Waiting..", "wallet": "Wallet", "warning": "Warning", "watchedRewardAd": "Watched Reward Ad", "weak-password": "Password is weak", "welcomeBonus": "Welcome Bonus", "wellDone": "Well done!", "winnerLbl": "Winner", "won": "Won", "wonAudioQuiz": "Won Audio Quiz", "wonBattle": "Won Battle", "wonContest": "Won Contest", "wonDailyQuiz": "Won Daily Quiz", "wonFunNLearn": "Won Fun N Learn Quiz", "wonGroupBattle": "Won Group Battle", "wonGuessTheWord": "Won Guess The Word Quiz", "wonMathQuiz": "Won Math Quiz", "wonQuizZone": "Won Quiz Zone Quiz", "wonTrueFalse": "Won True / False Quiz", "wrong-password": "Password is incorrect", "wrongDetails": "Wrong Details", "yesBtn": "Yes", "yesDeleteAcc": "Yes, Delete", "yesLogoutLbl": "Yes, <PERSON><PERSON><PERSON>", "youLeftLbl": "You left the game", "youLeftTheExam": "You left the exam", "youLossLbl": "You Loss", "youLostLbl": "YOU LOST", "youWillGet": "You will get", "youWin": "You win", "youWonLbl": "You won", "yourAnsLbl": "Your Answer", "yourCoins": "Your Coins", "yourRefCOdeLbl": "You Referral Code", "categoryLbl": "Category", "rendering": "Rendering...", "battleInviteMessageIntro": "Dive into the ultimate quiz battle showdown on", "battleInviteMessageJoin": "Join the squad using code:", "battleInviteMessageEnd": "let's crush it!", "just": "Just", "watchedAds": "Watched Ads", "referText1": "Hey there! Earn", "referText2": "coins by applying my referral code:", "referText3": "Compete for top prizes! Don't miss out—sign up now.\nTerms and conditions apply.\nInstall the app by clicking this link:", "cancelPaymentRequest": "Canceled Payment Request", "cancelPaymentConfirmation": "Are you sure, you want to cancel this request ?", "adsPreference": "Ads Preference", "selectLanguage": "Select Language", "startsOnLbl": "Starts On", "iapProcessingTitle": "Securing your purchase.", "iapProcessingMessage": "Please stay on this screen for a smooth transaction.", "quizCategory": "Quiz Category", "noLoginMethodsWarning": "All login methods are disabled.\nPlease enable one of them for app to work.", "signInApple": "Sign in with Apple", "signInGoogle": "Sign in with Google", "signInPhone": "Sign in with Phone", "forIOSMustEnableAppleLogin": "For IOS, you must enable Apple Login"}