name: flutterquiz
description: A new Flutter application.
publish_to: "none"
# Release Version: 2.2.4 (do not remove or edit this)
version: 1.1.50+83
environment:
  sdk: '>=3.5.1 <4.0.0'

dependencies:
  app_tracking_transparency: ^2.0.1
  awesome_notifications: 0.10.1
  cached_network_image: ^3.4.1
  cloud_firestore: ^5.4.2
  connectivity_plus: ^6.0.5
  country_code_picker: ^3.0.0
  cupertino_icons: ^1.0.8
  in_app_review: ^2.0.10
  dotted_border: ^3.0.1
  encrypt: ^5.0.3
  firebase_analytics: ^11.3.2
  firebase_auth: ^5.3.0
  firebase_core: ^3.5.0
  firebase_messaging: ^15.1.2
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.1
  flutter_cache_manager: ^3.4.1
  flutter_inappwebview: ^6.1.5
  flutter_local_notifications: ^19.2.1
  flutter_localizations:
    sdk: flutter
  flutter_svg: ^2.0.10+1
  # flutter_tex: ^4.0.9
  flutter_widget_from_html: ^0.16.0
  google_fonts: ^6.2.1
  google_mobile_ads: ^6.0.0
  google_sign_in: ^6.2.1
  hive_flutter: ^1.1.0
  http: ^1.2.2
  iabtcf_consent_info: ^3.4.0
  iconsax_plus: ^1.0.0
  image_cropper: ^9.1.0
  image_picker: ^1.1.2
  internet_connection_checker: ^3.0.1
  intl: ^0.20.2  # تأكد من استخدام أحدث إصدار
  flutter_image_compress: ^2.1.0
  path: ^1.8.3
  camera: ^0.11.1
  just_audio: ^0.9.40
  lottie: ^3.1.2
  package_info_plus: ^8.0.2
  path_provider: ^2.1.4
  permission_handler: ^12.0.0+1
  pin_code_fields: ^8.0.1
  salomon_bottom_bar: ^3.3.2
  scratcher: ^2.5.0
  screenshot: ^3.0.0
  share_plus: ^11.0.0
  shimmer: ^3.0.0
  shared_preferences: ^2.3.2
  sign_in_with_apple: ^7.0.1
  syncfusion_flutter_pdfviewer: ^29.2.7
  unity_ads_plugin: ^0.3.20
  url_launcher: ^6.3.0
  wakelock_plus: ^1.2.8
  purchases_flutter: ^8.4.4
  youtube_player_flutter: ^9.1.1
  audioplayers: ^6.4.0  # أو أحدث إصدار متاح
  iconsax: ^0.0.8
  font_awesome_flutter: ^10.8.0
  agora_rtc_engine: ^6.2.6  # حزمة Agora للمحادثة الصوتية

dev_dependencies:
  change_app_package_name: ^1.4.0
  dart_code_metrics_presets: ^2.16.0
  flutter_launcher_icons: ^0.14.0
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter
  very_good_analysis: ^8.0.0

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/ic_launcher.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/ic_launcher.png"
  adaptive_icon_monochrome: "assets/images/ic_launcher.png"

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/profile/
    - assets/images/emojis/
    - assets/languages/
    - assets/animations/
    - assets/sounds/
    - assets/files/
