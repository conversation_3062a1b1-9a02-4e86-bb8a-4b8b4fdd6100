import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_tex/flutter_tex.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';

import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';

class QuestionContainer extends StatefulWidget {
  const QuestionContainer({
    required this.isMathQuestion,
    super.key,
    this.question,
    this.questionColor,
    this.questionNumber,
  });
  final Question? question;
  final Color? questionColor;
  final int? questionNumber;
  final bool isMathQuestion;

  @override
  State<QuestionContainer> createState() => _QuestionContainerState();
}

class _QuestionContainerState extends State<QuestionContainer> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // عرض نص السؤال فقط إذا كان أكثر من حرف واحد
        if (widget.question != null && widget.question!.shouldShowQuestionText)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: Container(
                  margin: const EdgeInsets.all(10),
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(
                    horizontal:
                        MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
                    vertical: MediaQuery.of(context).size.height *
                        UiUtils.vtMarginPct,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(9),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                    //  color: Colors.white,
                    // color: Theme.of(context).primaryColor,
                  ),
                  // child: widget.isMathQuestion
                  //     ? TeXView(
                  //         onRenderFinished: (height) {
                  //           setState(() {});
                  //         },
                  //         style: TeXViewStyle(
                  //           //    contentColor: Theme.of(context).colorScheme.onPrimary,
                  //           //    backgroundColor: Theme.of(context).primaryColor,
                  //           contentColor: Colors.black,
                  //           backgroundColor: Colors.white,
                  //           sizeUnit: TeXViewSizeUnit.pixels,
                  //           textAlign: TeXViewTextAlign.center,
                  //           padding: const TeXViewPadding.all(10),
                  //           fontStyle: TeXViewFontStyle(
                  //             //   fontSize: context.read<SettingsCubit>().getSettings().playAreaFontSize.toInt() + 5,
                  //             fontFamily: 'IBMPlexSansArabic-Regular',
                  //           ),
                  //         ),
                  //         fonts: const [
                  //           TeXViewFont(
                  //             fontFamily: 'IBMPlexSansArabic-Regular',
                  //             src: 'assets/fonts/IBMPlexSansArabic-Regular.ttf',
                  //           ),
                  //           TeXViewFont(
                  //             fontFamily: 'IBMPlexSansArabic-Bold',
                  //             src: 'assets/fonts/IBMPlexSansArabic-Bold.ttf',
                  //           ),
                  //         ],
                  //         child: TeXViewDocument(
                  //           widget.question!.question!,
                  //         ),
                  //         // renderingEngine: const TeXViewRenderingEngine.katex(),
                  //       )
                  child: Text(
                    widget.questionNumber == null
                        ? '${widget.question!.question}'
                        : '${widget.questionNumber}. ${widget.question!.question}',
                    style: GoogleFonts.ibmPlexSansArabic(
                        textStyle: TextStyle(
                      fontSize: 17,
                      color: Theme.of(context).colorScheme.primary,
                    )),
                  ),

                  //                   Text(
                  //   questionText,
                  //   textAlign: TextAlign.center,
                  //   style: GoogleFonts.ibmPlexSansArabic(
                  //     textStyle: TextStyle(

                  //       height: 1.125,
                  //       color:Colors.black87,
                  //       fontSize: textSize,
                  //     ),
                  //   ),
                  // );
                ),
              ),

              /// Show Marks if given
              if (widget.question!.marks!.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: Text(
                    '[${widget.question!.marks}]',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      color: widget.questionColor ??
                          Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              ],
            ],
          ),
        const SizedBox(height: 15),

        // عرض الصورة مع إمكانية التكبير
        if (widget.question!.imageUrl != null &&
            widget.question!.imageUrl!.isNotEmpty) ...[
          GestureDetector(
            onTap: () {
              _showFullScreenImage(
                  context, widget.question!.imageUrl!); // عرض الصورة بحجم كامل
            },
            child: Stack(
              children: [
                Container(
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color:
                        Theme.of(context).primaryColor.withValues(alpha: 0.3),
                  ),
                  height: MediaQuery.of(context).size.height * (0.12),
                  padding: const EdgeInsets.all(8),
                  child: CachedNetworkImage(
                    errorWidget: (context, image, _) => Center(
                      child: Icon(
                        Icons.error,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    imageBuilder: (context, imageProvider) {
                      return Container(
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: imageProvider,
                            fit: BoxFit.contain,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                      );
                    },
                    imageUrl: widget.question!.imageUrl!,
                    placeholder: (context, url) => const Center(
                      child: CircularProgressContainer(),
                    ),
                  ),
                ),
                const Positioned(
                  top: 8,
                  right: 8,
                  child: Icon(
                    Icons.zoom_out_map,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 5),
        ],
      ],
    );
  }

  void _showFullScreenImage(BuildContext context, String imageUrl) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    showDialog(
      context: context,
      builder: (context) {
        return GestureDetector(
          onTap: () {
            Navigator.of(context).pop(); // إغلاق النافذة عند النقر في أي مكان
          },
          child: Dialog(
            backgroundColor: Colors.transparent,
            insetPadding: const EdgeInsets.all(10),
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              placeholder: (context, url) => const Center(
                child: CircularProgressIndicator(),
              ),
              errorWidget: (context, url, error) => const Icon(Icons.error),
              imageBuilder: (context, imageProvider) {
                return Container(
                  width: screenWidth,
                  height: screenHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.contain, // عرض الصورة بشكل ملائم
                    ),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 10,
                        offset: Offset(0, 5),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
