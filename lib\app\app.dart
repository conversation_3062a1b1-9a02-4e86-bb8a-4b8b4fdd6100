// import 'dart:io' show Platform;
// import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/ads/interstitial_ad_cubit.dart';
import 'package:flutterquiz/features/ads/rewarded_ad_cubit.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/badges/badgesRepository.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/battleRoom/battleRoomRepository.dart';
import 'package:flutterquiz/features/battleRoom/cubits/battleRoomCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/multiUserBattleRoomCubit.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/audioQuestionBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/guessTheWordBookmarkCubit.dart';
import 'package:flutterquiz/features/exam/cubits/examCubit.dart';
import 'package:flutterquiz/features/exam/examRepository.dart';
import 'package:flutterquiz/features/localization/appLocalizationCubit.dart';
import 'package:flutterquiz/features/localization/quizLocalizationCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/cubits/comprehensionCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/contestCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizzone_category_cubit.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/unlock_premium_category_cubit.dart';
import 'package:flutterquiz/features/quiz/cubits/unlockedLevelCubit.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/features/settings/settingsLocalDataSource.dart';
import 'package:flutterquiz/features/settings/settingsRepository.dart';
import 'package:flutterquiz/features/statistic/cubits/statisticsCubit.dart';
import 'package:flutterquiz/features/statistic/statisticRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/features/systemConfig/system_config_repository.dart';
import 'package:flutterquiz/model/singletons_data.dart';
import 'package:flutterquiz/ui/screens/subscription/SubscriptionService.dart';
import 'package:flutterquiz/ui/screens/subscription/subscription_manager.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:flutterquiz/ui/styles/theme/themeCubit.dart';
import 'package:flutterquiz/utils/awesome_notification_manager.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

Future<Widget> initializeApp() async {
  try {
    final stopwatch = Stopwatch()..start();

    // تأكد من تهيئة Flutter
    WidgetsFlutterBinding.ensureInitialized();

    // قائمة من Future للتنفيذ المتوازي
    final futures = <Future>[];

    // تهيئة Hive
    futures.add(
      Hive.initFlutter().then((_) async {
        await Future.wait([
          Hive.openBox<dynamic>(authBox),
          Hive.openBox<dynamic>(settingsBox),
          Hive.openBox<dynamic>(userDetailsBox),
          Hive.openBox<dynamic>(examBox),
        ]);
      }),
    );

    if (!kIsWeb) {
      futures.add(_initSystemSettings());

      // تكوين Firestore بعد تهيئة Firebase
      FirebaseFirestore.instance.settings = const Settings(
        persistenceEnabled: false,
        cacheSizeBytes: 1048576,
      );

      // تهيئة الإشعارات
      futures.add(awesomeNotificationManager.init());
    }

    // تهيئة مدير الاشتراكات
    futures.add(SubscriptionManager.instance.initialize());

    await Future.wait(futures);

    log('Total initialization time: ${stopwatch.elapsedMilliseconds}ms');
    return const MyApp();

  } catch (e, stack) {
    log('Error in initializeApp: $e');
    log('Stack trace: $stack');
    rethrow;
  }
}

Future<void> _initSystemSettings() async {
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(statusBarColor: Colors.transparent),
  );
}

// Removed unused initialization functions

Future<void> initPlatformState() async {
  appData.appUserID = await Purchases.appUserID;

  Purchases.addCustomerInfoUpdateListener((customerInfo) async {
    appData.appUserID = await Purchases.appUserID;

    CustomerInfo customerInfo = await Purchases.getCustomerInfo();
    EntitlementInfo? entitlement = customerInfo.entitlements.all[entitlementID];
    appData.entitlementIsActive = entitlement?.isActive ?? false;

    //  setState(() {});
  });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    precacheImage(const AssetImage(Assets.mapFinded), context);
    precacheImage(const AssetImage(Assets.mapFinding), context);
    precacheImage(const AssetImage(Assets.scratchCardCover), context);

    return MultiBlocProvider(
      providers: [
        // BlocProvider<SubscriptionCubit>(
        //   create: (context) => SubscriptionCubit(),
        // ),
        BlocProvider<ThemeCubit>(
          create: (_) => ThemeCubit(SettingsLocalDataSource()),
        ),
        BlocProvider<SettingsCubit>(
          create: (_) => SettingsCubit(SettingsRepository()),
        ),
        BlocProvider<AuthCubit>(create: (_) => AuthCubit(AuthRepository())),
        BlocProvider<AppLocalizationCubit>(
          create: (_) => AppLocalizationCubit(SettingsLocalDataSource()),
        ),
        BlocProvider<QuizLanguageCubit>(
          create: (_) => QuizLanguageCubit(SettingsLocalDataSource()),
        ),
        BlocProvider<UserDetailsCubit>(
          create: (_) => UserDetailsCubit(ProfileManagementRepository()),
        ),
        //bookmark questions of quiz zone
        BlocProvider<BookmarkCubit>(
          create: (_) => BookmarkCubit(BookmarkRepository()),
        ),
        //bookmark questions of guess the word
        BlocProvider<GuessTheWordBookmarkCubit>(
          create: (_) => GuessTheWordBookmarkCubit(BookmarkRepository()),
        ),

        //audio question bookmark cubit
        BlocProvider<AudioQuestionBookmarkCubit>(
          create: (_) => AudioQuestionBookmarkCubit(BookmarkRepository()),
        ),

        //it will be use in multiple dialogs and screen
        BlocProvider<MultiUserBattleRoomCubit>(
          create: (_) => MultiUserBattleRoomCubit(BattleRoomRepository()),
        ),

        BlocProvider<BattleRoomCubit>(
          create: (_) => BattleRoomCubit(BattleRoomRepository()),
        ),

        //system config
        BlocProvider<SystemConfigCubit>(
          create: (_) => SystemConfigCubit(SystemConfigRepository()),
        ),
        //to configure badges
        BlocProvider<BadgesCubit>(
          create: (_) => BadgesCubit(BadgesRepository()),
        ),
        //statistic cubit
        BlocProvider<StatisticCubit>(
          create: (_) => StatisticCubit(StatisticRepository()),
        ),
        //Interstitial ad cubit
        BlocProvider<InterstitialAdCubit>(create: (_) => InterstitialAdCubit()),
        //Rewarded ad cubit
        BlocProvider<RewardedAdCubit>(create: (_) => RewardedAdCubit()),
        //exam cubit
        BlocProvider<ExamCubit>(create: (_) => ExamCubit(ExamRepository())),

        //Setting this cubit globally so we can fetch again once
        //set quiz categories success
        BlocProvider<ComprehensionCubit>(
          create: (_) => ComprehensionCubit(QuizRepository()),
        ),

        BlocProvider<ContestCubit>(
          create: (_) => ContestCubit(QuizRepository()),
        ),
        //
        //Setting this cubit globally so we can fetch again once
        //set quiz categories success
        BlocProvider<QuizCategoryCubit>(
          create: (_) => QuizCategoryCubit(QuizRepository()),
        ),
        BlocProvider<QuizoneCategoryCubit>(
          create: (_) => QuizoneCategoryCubit(QuizRepository()),
        ),
        BlocProvider<UnlockedLevelCubit>(
          create: (_) => UnlockedLevelCubit(QuizRepository()),
        ),
        //
        //Setting this cubit globally so we can fetch again once
        //set quiz categories success
        BlocProvider<SubCategoryCubit>(
          create: (_) => SubCategoryCubit(QuizRepository()),
        ),
        BlocProvider<UnlockPremiumCategoryCubit>(
          create: (_) => UnlockPremiumCategoryCubit(QuizRepository()),
        ),
        BlocProvider<SubscriptionCubit>(
          create: (_) => SubscriptionCubit(),
        ),
      ],
      child: Builder(
        builder: (context) {
          // Watching themeCubit means if any change occurs in themeCubit,
          // it will rebuild the child
          final currentTheme = context.watch<ThemeCubit>().state.appTheme;
          final isRTL = context.watch<AppLocalizationCubit>().state.isRTL;

          return AnnotatedRegion<SystemUiOverlayStyle>(
            value: (currentTheme == AppTheme.light
                    ? SystemUiOverlayStyle.dark
                    : SystemUiOverlayStyle.light)
                .copyWith(statusBarColor: Colors.transparent),
            child: MaterialApp(
              title: appName,
              builder: (_, widget) {
                return ScrollConfiguration(
                  behavior: const _GlobalScrollBehavior(),
                  child: Directionality(
                    textDirection:
                        isRTL ? TextDirection.rtl : TextDirection.rtl,
                    child: widget!,
                  ),
                );
              },
              locale: const Locale('ar', 'SA'),
              // locale:
              //     !enableDevicePreview ? null : DevicePreview.locale(context),
              theme: appThemeData[currentTheme],
              debugShowCheckedModeBanner: false,
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              initialRoute: Routes.splash,
              onGenerateRoute: Routes.onGenerateRouted,
            ),
          );
        },
      ),
    );
  }
}

class _GlobalScrollBehavior extends ScrollBehavior {
  const _GlobalScrollBehavior();

  @override
  ScrollPhysics getScrollPhysics(_) => const BouncingScrollPhysics();
}
