// استيراد المكتبات اللازمة للصفحة
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

/// صفحة عرض خطط الاشتراك والمدفوعات
/// تعرض الباقات المتاحة وتتيح للمستخدم الاشتراك
class Paywall extends StatefulWidget {
  final Offering? offering;
  final Widget Function(String message)? onError;

  const Paywall({
    super.key,
    this.offering,
    this.onError,
  });

  @override
  // ignore: library_private_types_in_public_api
  _PaywallState createState() => _PaywallState();
}

class _PaywallState extends State<Paywall> with SingleTickerProviderStateMixin {
  // متغير لعرض حالة التحميل
  bool _isLoading = false;

  // متغيرات للرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// دالة لفتح الروابط الخارجية
  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          "الاشتراكات المميزة",
          style: GoogleFonts.ibmPlexSansArabic(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withOpacity(0.8),
              Theme.of(context).primaryColor.withOpacity(0.6),
              Colors.white,
            ],
            stops: const [0.0, 0.3, 0.5, 0.8],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth < 600;
              return Stack(
                children: [
                  AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: _fadeAnimation.value,
                        child: Transform.translate(
                          offset: Offset(0, _slideAnimation.value),
                          child: child,
                        ),
                      );
                    },
                    child: widget.offering == null
                        ? widget.onError?.call('لا تتوفر باقات حالياً') ??
                            Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'عذراً، لا تتوفر باقات حالياً',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'يرجى المحاولة لاحقاً',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.7),
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            )
                        : SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Padding(
                        padding: EdgeInsets.fromLTRB(
                          isSmallScreen ? 16 : 32,
                          20,
                          isSmallScreen ? 16 : 32,
                          20
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            _buildHeader(isSmallScreen),
                            SizedBox(height: isSmallScreen ? 20 : 30),
                            _buildFeaturesSection(isSmallScreen),
                            SizedBox(height: isSmallScreen ? 20 : 30),
                            if (widget.offering?.availablePackages.isNotEmpty == true)
                              _buildPackagesSection(isSmallScreen),
                            SizedBox(height: isSmallScreen ? 20 : 30),
                            _buildInfoSection(isSmallScreen),
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (_isLoading)
                    Container(
                      color: Colors.black54,
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                    ),
                ],
              );
            }
          ),
        ),
      ),
    );
  }

  /// بناء ترويسة الصفحة مع العنوان الرئيسي
  Widget _buildHeader(bool isSmallScreen) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.workspace_premium,
              color: Theme.of(context).primaryColor,
              size: 40,
            ),
          ),
          const SizedBox(height: 15),
          Text(
            "اشترك الآن",
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: isSmallScreen ? 22 : 26,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            "واستمتع بتجربة تعليمية متكاملة",
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم عرض الباقات المتاحة
  Widget _buildPackagesSection(bool isSmallScreen) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.workspace_premium,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 10),
              Text(
                "اختر باقتك المفضلة",
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          ...widget.offering!.availablePackages.map((package) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 15),
              child: _buildPackageCard(package),
            );
          }),
        ],
      ),
    );
  }

  /// تحسين عرض بطاقة الباقة
  Widget _buildPackageCard(Package package) {
    // تحديد نوع الباقة
    String subscriptionType = '';
    IconData packageIcon;
    Color accentColor;
    String periodText = '';

    if (package.storeProduct.identifier.toLowerCase().contains('weekly')) {
      subscriptionType = 'اشتراك أسبوعي';
      packageIcon = Icons.calendar_today;
      accentColor = Colors.green;
      periodText = 'أسبوع';
    } else if (package.storeProduct.identifier.toLowerCase().contains('monthly')) {
      subscriptionType = 'اشتراك شهري';
      packageIcon = Icons.calendar_month;
      accentColor = Colors.blue;
      periodText = 'شهر';
    } else {
      subscriptionType = 'اشتراك سنوي';
      packageIcon = Icons.calendar_view_month;
      accentColor = Colors.purple;
      periodText = 'سنة';
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: accentColor.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _purchasePackage(package),
          borderRadius: BorderRadius.circular(15),
          child: Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(
                color: accentColor.withOpacity(0.3),
                width: 1.5,
              ),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Column(
              children: [
                // نوع الباقة مع الأيقونة
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: accentColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        packageIcon,
                        color: accentColor,
                        size: 22,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Text(
                      subscriptionType,
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const Spacer(),
                    if (package.storeProduct.identifier.toLowerCase().contains('yearly'))
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '🎯 أفضل قيمة',
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 15),

                // تفاصيل السعر والمميزات
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getFormattedDescription(package.storeProduct.description),
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 14,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 10),
                          if (_calculatePricePerMonth(package) != null && package.storeProduct.identifier.toLowerCase().contains('yearly'))
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: accentColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                _calculatePricePerMonth(package)!,
                                style: GoogleFonts.ibmPlexSansArabic(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: accentColor,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 10),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color: accentColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Text(
                            package.storeProduct.priceString,
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: accentColor,
                            ),
                          ),
                          Text(
                            'لكل $periodText',
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// تنسيق وصف الباقة
  String _getFormattedDescription(String description) {
    return description
        .replaceAll('•', '\n•')
        .trim();
  }



  /// حساب السعر الشهري للباقات
  String? _calculatePricePerMonth(Package package) {
    try {
      final price = double.parse(package.storeProduct.price.toString());
      if (package.storeProduct.identifier.toLowerCase().contains('yearly')) {
        return 'توفير ${(100 - (price / 12 / 6.99 * 100)).toStringAsFixed(0)}٪ - ${(price / 12).toStringAsFixed(2)} شهرياً';
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  /// بناء قسم المميزات المتاحة مع الاشتراك
  Widget _buildFeaturesSection(bool isSmallScreen) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "مميزات الاشتراك",
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 15),
          _buildFeatureItem(
            Icons.check_circle_rounded,
            "الوصول إلى كامل الأقسام والمستويات",
            "استمتع بتجربة تعليمية متكاملة بدون قيود",
          ),
          const Divider(height: 20),
          _buildFeatureItem(
            Icons.block,
            "بدون إعلانات",
            "تعلم بدون مقاطعات أو إعلانات مزعجة",
          ),
          // const Divider(height: 20),
          // _buildFeatureItem(
          //   Icons.update,
          //   "تحديثات مستمرة",
          //   "احصل على أحدث المحتويات التعليمية أولاً بأول",
          // ),
        ],
      ),
    );
  }

  /// بناء عنصر ميزة واحد مع أيقونة وعنوان ووصف
  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 15),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قسم المعلومات الإضافية والروابط المهمة
  Widget _buildInfoSection(bool isSmallScreen) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 10),
              Text(
                "معلومات الاشتراك",
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              children: [
                _buildInfoItem(
                  Icons.autorenew,
                  "يتم تجديد الاشتراك تلقائياً ما لم يتم إلغاؤه",
                ),
                const Divider(height: 15),
                _buildInfoItem(
                  Icons.cancel_outlined,
                  "يمكنك إلغاء اشتراكك في أي وقت من إعدادات حسابك",
                ),
              ],
            ),
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                onPressed: () => _launchURL('https://mujtahidacademy.com/privacy-policy'),
                child: Text(
                  "سياسة الخصوصية",
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 14,
                    color: Theme.of(context).primaryColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              Text(
                "|",
                style: GoogleFonts.ibmPlexSansArabic(
                  color: Colors.grey,
                ),
              ),
              TextButton(
                onPressed: () => _launchURL('https://mujtahidacademy.com/terms'),
                child: Text(
                  "شروط الاستخدام",
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 14,
                    color: Theme.of(context).primaryColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر معلومات مع أيقونة
  Widget _buildInfoItem(IconData icon, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: Colors.grey[600],
          size: 18,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.grey[800],
            ),
          ),
        ),
      ],
    );
  }

  /// معالجة عملية الشراء والاشتراك
  Future<void> _purchasePackage(Package package) async {
    setState(() => _isLoading = true);
    try {
      final purchaseResult = await Purchases.purchasePackage(package);
      final isPurchased = purchaseResult.entitlements.all['premium_access']?.isActive ?? false;

      if (isPurchased && mounted) {
        // عرض رسالة النجاح
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 50,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      "تم الاشتراك بنجاح!",
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 15),
                    Text(
                      "شكراً لاشتراكك معنا! يمكنك الآن الاستمتاع بجميع المميزات المتاحة في التطبيق",
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        color: Colors.grey[700],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 25),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).primaryColor.withOpacity(0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          padding: const EdgeInsets.symmetric(
                            vertical: 15,
                          ),
                        ),
                        onPressed: () {
                          Navigator.of(context).pop(); // إغلاق الحوار
                          Navigator.of(context).pop(); // العودة للصفحة الرئيسية
                        },
                        child: Text(
                          "العودة للرئيسية",
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ في عملية الشراء، يرجى المحاولة مرة أخرى',
              style: GoogleFonts.ibmPlexSansArabic(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}