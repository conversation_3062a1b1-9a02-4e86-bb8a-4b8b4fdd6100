import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/quiz/cubits/quizCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/models/category.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';

import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';

class SelfChallengeScreen extends StatefulWidget {
  const SelfChallengeScreen({super.key});

  @override
  State<SelfChallengeScreen> createState() => _SelfChallengeScreenState();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(builder: (_) => const SelfChallengeScreen());
  }
}

class _SelfChallengeScreenState extends State<SelfChallengeScreen> {
  static const String _defaultSelectedCategoryValue = selectCategoryKey;
  static const String _defaultSelectedSubcategoryValue = selectSubCategoryKey;

  //to display category and suncategory
  String? selectedCategory = _defaultSelectedCategoryValue;
  String? selectedSubcategory = _defaultSelectedSubcategoryValue;

  //id to pass for selfChallengeQuestionsScreen
  String? selectedCategoryId = '';
  String? selectedSubcategoryId = '';

  //minutes for self challenge
  int? selectedMinutes;

  //nunber of questions
  int? selectedNumberOfQuestions;

  late final _quizType =
      UiUtils.getCategoryTypeNumberFromQuizType(QuizTypes.selfChallenge);
  late final _subType = UiUtils.subTypeFromQuizType(QuizTypes.selfChallenge);

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, _getCategories);
  }

  void _getCategories() {
    context.read<QuizCategoryCubit>().getQuizCategoryWithUserId(
          languageId: UiUtils.getCurrentQuizLanguageId(context),
          type: _quizType,
          subType: _subType,
        );
  }

  void startSelfChallenge() {
    //
    if (context.read<SubCategoryCubit>().state is SubCategoryFetchFailure) {
      //If there is not any sub category then fetch the all quesitons from given category
      if ((context.read<SubCategoryCubit>().state as SubCategoryFetchFailure)
              .errorMessage ==
          errorCodeDataNotFound) {
        //

        if (selectedCategory != _defaultSelectedCategoryValue &&
            selectedMinutes != null &&
            selectedNumberOfQuestions != null) {
          //to see what keys to pass in arguments see static function route of SelfChallengeQuesitonsScreen

          Navigator.of(context).pushNamed(
            Routes.selfChallengeQuestions,
            arguments: {
              'numberOfQuestions': selectedNumberOfQuestions.toString(),
              'categoryId': selectedCategoryId, //
              'minutes': selectedMinutes,
              'subcategoryId': '',
            },
          );
          return;
        } else {
          ScaffoldMessenger.of(context).removeCurrentSnackBar();
          UiUtils.showSnackBar(
            context.tr(
              convertErrorCodeToLanguageKey(errorCodeSelectAllValues),
            )!,
            context,
          );
          return;
        }
      }
    }

    if (selectedCategory != _defaultSelectedCategoryValue &&
        selectedSubcategory != _defaultSelectedSubcategoryValue &&
        selectedMinutes != null &&
        selectedNumberOfQuestions != null) {
      //to see what keys to pass in arguments see static function route of SelfChallengeQuesitonsScreen

      Navigator.of(context).pushNamed(
        Routes.selfChallengeQuestions,
        arguments: {
          'numberOfQuestions': selectedNumberOfQuestions.toString(),
          'categoryId': '', //categoryId
          'minutes': selectedMinutes,
          'subcategoryId': selectedSubcategoryId,
        },
      );
    } else {
      ScaffoldMessenger.of(context).removeCurrentSnackBar();
      UiUtils.showSnackBar(
        context.tr(
          convertErrorCodeToLanguageKey(errorCodeSelectAllValues),
        )!,
        context,
      );
    }
  }

  Widget _buildDropdownIcon() {
    return Container(
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).primaryColor.withOpacity(0.1),
      ),
      child: Icon(
        Icons.keyboard_arrow_down_rounded,
        size: 25,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  //using for category and subcategory
  Widget _buildDropdown({
    required bool forCategory,
    required List<Map<String, String?>> values, //keys of value will be name and id
    required String keyValue, // need to have this keyValues for fade animation
  }) {
    return DropdownButton<String>(
      key: Key(keyValue),
      dropdownColor: Theme.of(context).colorScheme.surface,
      borderRadius: BorderRadius.circular(15),
      //same as background of dropdown color
      style: GoogleFonts.ibmPlexSansArabic(
        textStyle: TextStyle(
          color: Theme.of(context).colorScheme.onTertiary,
          fontSize: 16,
        ),
      ),
      isExpanded: true,
      onChanged: (value) {
        ScaffoldMessenger.of(context).removeCurrentSnackBar();
        if (!forCategory) {
          // if it's for subcategory

          //if no subcategory selected then do nothing
          if (value != _defaultSelectedSubcategoryValue) {
            final index =
                values.indexWhere((element) => element['name'] == value);
            setState(() {
              selectedSubcategory = value;
              selectedSubcategoryId = values[index]['id'];
            });
          }
        } else {
          //if no category selected then do nothing
          if (value != _defaultSelectedCategoryValue) {
            final index =
                values.indexWhere((element) => element['name'] == value);
            setState(() {
              selectedCategory = value;
              selectedCategoryId = values[index]['id'];
              selectedSubcategory = _defaultSelectedSubcategoryValue; //
            });

            context
                .read<SubCategoryCubit>()
                .fetchSubCategory(selectedCategoryId!);
          } else {
            _getCategories();
          }
        }
      },
      icon: _buildDropdownIcon(),
      underline: const SizedBox(),
      //values is map of name and id. only passing name to dropdown
      items: values.map((e) => e['name']).toList().map((name) {
        return DropdownMenuItem(
          value: name,
          child: name! == selectCategoryKey || name == selectSubCategoryKey
              ? Text(context.tr(name)!)
              : Text(name),
        );
      }).toList(),
      value: forCategory ? selectedCategory : selectedSubcategory,
    );
  }

  //dropdown container with border
  Widget _buildDropdownContainer(Widget child) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      alignment: Alignment.center,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          width: 1.5,
        ),
      ),
      child: child,
    );
  }

  Widget _buildSubCategoryDropdownContainer(SubCategoryState state) {
    if (state is SubCategoryFetchSuccess) {
      return _buildDropdown(
        forCategory: false,
        values: state.subcategoryList
            .map((e) => {'name': e.subcategoryName, 'id': e.id})
            .toList(),
        keyValue: 'selectSubcategorySuccess${state.categoryId}',
      );
    }

    return Opacity(
      opacity: 0.75,
      child: _buildDropdown(
        forCategory: false,
        values: [
          {'name': _defaultSelectedSubcategoryValue},
        ],
        keyValue: 'selectSubcategory',
      ),
    );
  }

  //for selecting time and question
  Widget _buildSelectTimeAndQuestionContainer({
    required Color borderColor,
    bool? forSelectQuestion,
    int? value,
    Color? textColor,
    Color? backgroundColor,
  }) {
    final isSelected = forSelectQuestion!
        ? selectedNumberOfQuestions == value
        : selectedMinutes == value;

    return Hero(
      tag: 'select_${forSelectQuestion}_$value',
      child: Material(
        color: Colors.transparent,
        child: GestureDetector(
          onTap: () {
            setState(() {
              if (forSelectQuestion) {
                selectedNumberOfQuestions = value;
              } else {
                selectedMinutes = value;
              }
            });
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            alignment: Alignment.center,
            margin: const EdgeInsets.only(right: 15),
            height: 60,
            width: 70,
            decoration: BoxDecoration(
              color: isSelected ? Theme.of(context).primaryColor : Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: isSelected
                      ? Theme.of(context).primaryColor.withOpacity(0.3)
                      : Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  spreadRadius: 1,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).primaryColor.withOpacity(0.2),
                width: 1.5,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '$value',
                  style: TextStyle(
                    color: isSelected ? Colors.white : Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 22,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  forSelectQuestion ? 'سؤال' : 'دقيقة',
                  style: TextStyle(
                    color: isSelected ? Colors.white.withOpacity(0.9) : Colors.grey,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    final config = context.read<SystemConfigCubit>();
    final maxMinutes = config.selfChallengeMaxMinutes < 3
        ? 1
        : config.selfChallengeMaxMinutes ~/ 3;
    final maxQuestions = config.selfChallengeMaxQuestions < 5
        ? 1
        : config.selfChallengeMaxQuestions ~/ 5;

    return PopScope(
      onPopInvokedWithResult: (_, __) =>
          ScaffoldMessenger.of(context).removeCurrentSnackBar(),
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 18),
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            context.tr('selfChallenge')!,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          centerTitle: true,
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
                Theme.of(context).primaryColor.withOpacity(0.6),
                Colors.white,
              ],
              stops: const [0.0, 0.3, 0.5, 0.8],
            ),
          ),
          child: Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                top: size.height * 0.12, // إضافة مساحة أكبر في الأعلى
                bottom: size.height * UiUtils.vtMarginPct,
                left: size.width * UiUtils.hzMarginPct,
                right: size.width * UiUtils.hzMarginPct,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header image and title
                  Center(
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 30, top: 10),
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).primaryColor.withOpacity(0.1),
                            blurRadius: 20,
                            spreadRadius: 1,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.person_pin_circle_rounded,
                            size: 60,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(height: 10),
                          Text(
                            "تحدي نفسك",
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Text(
                            "اختبر معلوماتك بالوقت والأسئلة التي تريدها",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Category Selection Card
                  Container(
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).primaryColor.withOpacity(0.1),
                          blurRadius: 20,
                          spreadRadius: 1,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Header
                        Container(
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.category_rounded,
                                color: Theme.of(context).primaryColor,
                                size: 24,
                              ),
                              const SizedBox(width: 10),
                              Text(
                                context.tr('selectCategory')!,
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Dropdown
                        Padding(
                          padding: const EdgeInsets.all(15),
                          child: BlocConsumer<QuizCategoryCubit, QuizCategoryState>(
                            bloc: context.read<QuizCategoryCubit>(),
                            listener: (context, state) {
                              if (state is QuizCategorySuccess) {
                                setState(() {
                                  selectedCategory = state.categories.first.categoryName;
                                  selectedCategoryId = state.categories.first.id;
                                });
                                context
                                    .read<SubCategoryCubit>()
                                    .fetchSubCategory(state.categories.first.id!);
                              }
                              if (state is QuizCategoryFailure) {
                                if (state.errorMessage == errorCodeUnauthorizedAccess) {
                                  showAlreadyLoggedInDialog(context);
                                  return;
                                }

                                UiUtils.showSnackBar(
                                  context.tr(
                                    convertErrorCodeToLanguageKey(
                                      state.errorMessage,
                                    ),
                                  )!,
                                  context,
                                  showAction: true,
                                  duration: const Duration(days: 365),
                                  onPressedAction: _getCategories,
                                );
                              }
                            },
                            builder: (context, state) {
                              var categories = <Category>[];
                              if (state is QuizCategorySuccess) {
                                categories = state.categories
                                  ..removeWhere((c) => c.isPremium && !c.hasUnlocked);
                              }

                              return AnimatedOpacity(
                                duration: const Duration(milliseconds: 300),
                                opacity: state is QuizCategorySuccess ? 1.0 : 0.7,
                                child: _buildDropdownContainer(
                                  AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 500),
                                    child: state is QuizCategorySuccess
                                        ? _buildDropdown(
                                            forCategory: true,
                                            values: categories
                                                .map(
                                                  (e) => {
                                                    'name': e.categoryName,
                                                    'id': e.id,
                                                  },
                                                )
                                                .toList(),
                                            keyValue: 'selectCategorySuccess',
                                          )
                                        : Opacity(
                                            opacity: 0.75,
                                            child: _buildDropdown(
                                              forCategory: true,
                                              values: [
                                                {
                                                  'name': _defaultSelectedCategoryValue,
                                                  'id': '0',
                                                }
                                              ],
                                              keyValue: 'selectCategory',
                                            ),
                                          ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Subcategory Selection Card
                  BlocConsumer<SubCategoryCubit, SubCategoryState>(
                    bloc: context.read<SubCategoryCubit>(),
                    listener: (context, state) {
                      if (state is SubCategoryFetchSuccess) {
                        setState(() {
                          selectedSubcategory =
                              state.subcategoryList.first.subcategoryName;
                          selectedSubcategoryId = state.subcategoryList.first.id;
                        });
                      } else if (state is SubCategoryFetchFailure) {
                        if (state.errorMessage == errorCodeUnauthorizedAccess) {
                          showAlreadyLoggedInDialog(context);
                          return;
                        }

                        if (state.errorMessage == errorCodeDataNotFound) {
                          return;
                        }

                        UiUtils.showSnackBar(
                          context.tr(
                            convertErrorCodeToLanguageKey(
                              state.errorMessage,
                            ),
                          )!,
                          context,
                          duration: const Duration(days: 365),
                          onPressedAction: () {
                            context
                                .read<SubCategoryCubit>()
                                .fetchSubCategory(selectedCategoryId!);
                          },
                        );
                      }
                    },
                    builder: (context, state) {
                      if (state is SubCategoryFetchFailure) {
                        if (state.errorMessage == errorCodeDataNotFound) {
                          return const SizedBox();
                        }
                      }
                      return AnimatedOpacity(
                        duration: const Duration(milliseconds: 300),
                        opacity: state is SubCategoryFetchSuccess ? 1.0 : 0.7,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Theme.of(context).primaryColor.withOpacity(0.1),
                                blurRadius: 20,
                                spreadRadius: 1,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              // Header
                              Container(
                                padding: const EdgeInsets.all(15),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(20),
                                    topRight: Radius.circular(20),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.folder_special_rounded,
                                      color: Theme.of(context).primaryColor,
                                      size: 24,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      context.tr('selectSubCategory')!,
                                      style: TextStyle(
                                        color: Theme.of(context).primaryColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Dropdown
                              Padding(
                                padding: const EdgeInsets.all(15),
                                child: _buildDropdownContainer(
                                  AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 500),
                                    child: _buildSubCategoryDropdownContainer(state),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),

                  // Questions Selection Card
                  Container(
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).primaryColor.withOpacity(0.1),
                          blurRadius: 20,
                          spreadRadius: 1,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Header
                        Container(
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.quiz_rounded,
                                color: Theme.of(context).primaryColor,
                                size: 24,
                              ),
                              const SizedBox(width: 10),
                              Text(
                                context.tr('selectNoQusLbl')!,
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Questions Selection
                        Padding(
                          padding: const EdgeInsets.all(15),
                          child: SizedBox(
                            height: 80,
                            child: ListView(
                              scrollDirection: Axis.horizontal,
                              children: List.generate(
                                maxQuestions,
                                (index) => config.selfChallengeMaxQuestions < 5
                                    ? config.selfChallengeMaxQuestions
                                    : (index + 1) * 5,
                              )
                                  .map(
                                    (e) => _buildSelectTimeAndQuestionContainer(
                                      forSelectQuestion: true,
                                      value: e,
                                      borderColor: selectedNumberOfQuestions == e
                                          ? Theme.of(context).primaryColor
                                          : Colors.grey.shade400,
                                      backgroundColor: selectedNumberOfQuestions == e
                                          ? Theme.of(context).primaryColor
                                          : Colors.white,
                                      textColor: selectedNumberOfQuestions == e
                                          ? Colors.white
                                          : Theme.of(context).primaryColor,
                                    ),
                                  )
                                  .toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Time Selection Card
                  Container(
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).primaryColor.withOpacity(0.1),
                          blurRadius: 20,
                          spreadRadius: 1,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Header
                        Container(
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.timer_rounded,
                                color: Theme.of(context).primaryColor,
                                size: 24,
                              ),
                              const SizedBox(width: 10),
                              Text(
                                context.tr('selectTimeLbl')!,
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Time Selection
                        Padding(
                          padding: const EdgeInsets.all(15),
                          child: SizedBox(
                            height: 80,
                            child: ListView(
                              scrollDirection: Axis.horizontal,
                              children: List.generate(
                                maxMinutes,
                                (index) => config.selfChallengeMaxMinutes < 3
                                    ? config.selfChallengeMaxMinutes
                                    : (index + 1) * 3,
                              )
                                  .map(
                                    (e) => _buildSelectTimeAndQuestionContainer(
                                      forSelectQuestion: false,
                                      value: e,
                                      backgroundColor: selectedMinutes == e
                                          ? Theme.of(context).primaryColor
                                          : Colors.white,
                                      textColor: selectedMinutes == e
                                          ? Colors.white
                                          : Theme.of(context).primaryColor,
                                      borderColor: selectedMinutes == e
                                          ? Theme.of(context).primaryColor
                                          : Colors.grey.shade400,
                                    ),
                                  )
                                  .toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30),

                  // Start Challenge Button
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).primaryColor.withOpacity(0.3),
                          blurRadius: 20,
                          spreadRadius: 2,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Hero(
                      tag: 'start_challenge',
                      child: Material(
                        color: Colors.transparent,
                        child: CustomRoundedButton(
                          elevation: 0,
                          widthPercentage: 1,
                          backgroundColor: Theme.of(context).primaryColor,
                          buttonTitle: "ابدأ التحدي",
                          fontWeight: FontWeight.bold,
                          radius: 20,
                          onTap: startSelfChallenge,
                          showBorder: false,
                          titleColor: Colors.white,
                          shadowColor: Theme.of(context).primaryColor.withOpacity(0.5),
                          height: 60,
                          textSize: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
