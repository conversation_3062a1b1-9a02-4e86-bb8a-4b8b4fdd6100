import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/ads/rewarded_ad_cubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/utils/ui_utils.dart';

/// نافذة عرض الإعلان قبل النتائج مع رسالة تشجيعية للاشتراك
class PreResultAdDialog extends StatelessWidget {
  final VoidCallback onContinue;
  final VoidCallback? onSubscribe;

  const PreResultAdDialog({
    super.key,
    required this.onContinue,
    this.onSubscribe,
  });

  @override
  Widget build(BuildContext context) {
    final isSubscribed = context.read<UserDetailsCubit>().removeAds();
    final isAdsEnabled = context.read<SystemConfigCubit>().isAdsEnable;

    // إذا كان مشترك أو الإعلانات معطلة، انتقل مباشرة للنتائج
    if (isSubscribed || !isAdsEnabled) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onContinue();
      });
      return const SizedBox.shrink();
    }

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة الإعلان
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.play_circle_filled,
                size: 50,
                color: Theme.of(context).primaryColor,
              ),
            ),

            const SizedBox(height: 20),

            // العنوان
            Text(
              'شاهد إعلان لرؤية النتائج',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 15),

            // الوصف
            Text(
              'شاهد إعلان قصير لرؤية نتائج الاختبار والحصول على التفاصيل الكاملة',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 25),

            // أزرار الإجراءات
            Row(
              children: [
                // زر مشاهدة الإعلان
                Expanded(
                  flex: 2,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _showRewardedAd(context);
                    },
                    icon: const Icon(Icons.play_arrow, color: Colors.white),
                    label: Text(
                      'شاهد الإعلان',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 10),

                // زر التخطي
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      onContinue();
                    },
                    child: Text(
                      'تخطي',
                      style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.2),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // رسالة الاشتراك
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.amber.withOpacity(0.3),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'اشترك لإزالة جميع الإعلانات نهائياً!',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.amber.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (onSubscribe != null) ...[
                    const SizedBox(height: 10),
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          onSubscribe!();
                        },
                        child: Text(
                          'اشترك الآن',
                          style: TextStyle(
                            color: Colors.amber.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.amber.withOpacity(0.1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showRewardedAd(BuildContext context) {
    final rewardedAdCubit = context.read<RewardedAdCubit>();

    if (rewardedAdCubit.state is! RewardedAdLoaded) {
      // إذا لم يكن الإعلان محمل، انتقل للنتائج مباشرة
      UiUtils.showSnackBar(
        'الإعلان غير جاهز، سيتم عرض النتائج',
        context,
      );
      onContinue();
      return;
    }

    rewardedAdCubit.showAd(
      context: context,
      onAdDismissedCallback: () {
        // بعد انتهاء الإعلان، انتقل للنتائج
        onContinue();
      },
    );
  }
}
