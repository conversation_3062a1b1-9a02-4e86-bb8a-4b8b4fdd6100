import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/profileManagement/cubits/deleteAccountCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateUserDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/uploadProfileCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/menu/widgets/all.dart';
import 'package:flutterquiz/ui/screens/menu/widgets/quiz_language_selector_sheet.dart';
import 'package:flutterquiz/ui/widgets/login_dialog.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:url_launcher/url_launcher.dart';

class MenuOption {
  final String name;
  final IconData icon;
  
  const MenuOption(this.name, this.icon);
}

class MenuScreen extends StatefulWidget {
  const MenuScreen({required this.isGuest, this.fromNav = false, super.key});

  final bool isGuest;
  final bool fromNav;

  @override
  State<MenuScreen> createState() => _MenuScreenState();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<DeleteAccountCubit>(
            create: (_) => DeleteAccountCubit(ProfileManagementRepository()),
          ),
          BlocProvider<UploadProfileCubit>(
            create: (_) => UploadProfileCubit(ProfileManagementRepository()),
          ),
          BlocProvider<UpdateUserDetailCubit>(
            create: (_) => UpdateUserDetailCubit(ProfileManagementRepository()),
          ),
        ],
        child: MenuScreen(isGuest: routeSettings.arguments! as bool),
      ),
    );
  }
}

class _MenuScreenState extends State<MenuScreen> {
  List<MenuOption> menu = [];

  @override
  void initState() {
    super.initState();
    menu = [
      const MenuOption('notificationLbl', Icons.notifications_outlined),
      const MenuOption('bookmarkLbl', Icons.bookmark_outline),
   //   const MenuOption('statisticsLabel', Icons.bar_chart_outlined),
      const MenuOption('language', Icons.language_outlined),
      const MenuOption('aboutQuizApp', Icons.info_outline),
      // const MenuOption('howToPlayLbl', Icons.help_outline),
      // const MenuOption('shareAppLbl', Icons.share_outlined), // Temporarily removed
      const MenuOption('rateUsLbl', Icons.star_outline),
      const MenuOption('logoutLbl', Icons.logout_outlined),
      const MenuOption('deleteAccountLbl', Icons.delete_outline),
    ];

    final sysConfig = context.read<SystemConfigCubit>();

    if (!sysConfig.isLanguageModeEnabled) {
      menu.removeWhere((e) => e.name == 'language');
    }

    if (!(sysConfig.isQuizZoneEnabled ||
        sysConfig.isGuessTheWordEnabled ||
        sysConfig.isAudioQuizEnabled)) {
      menu.removeWhere((e) => e.name == 'bookmarkLbl');
    }

    if (widget.isGuest) {
      menu
        ..removeWhere((e) => e.name == 'logoutLbl')
        ..removeWhere((e) => e.name == 'deleteAccountLbl');
    }
  }

  void _handleProfileEdit() {
    if (widget.isGuest) {
      showLoginDialog(
        context,
        onTapYes: () => context
          ..shouldPop()
          ..shouldPop()
          ..pushNamed(Routes.login),
      );
    } else {
      Navigator.of(context).pushNamed(Routes.selectProfile, arguments: false);
    }
  }

  void _onPressed(String index, BuildContext context) {
    if (widget.isGuest && 
        !['language', 'aboutQuizApp', 'howToPlayLbl', 'shareAppLbl', 'rateUsLbl'].contains(index)) {
      showLoginDialog(
        context,
        onTapYes: () => context
          ..shouldPop()
          ..shouldPop()
          ..pushNamed(Routes.login),
      );
      return;
    }

    switch (index) {
      case 'language':
        showQuizLanguageSelectorSheet(context);
        break;
      case 'aboutQuizApp':
        Navigator.of(context).pushNamed(Routes.aboutApp);
        break;
      case 'howToPlayLbl':
        Navigator.of(context).pushNamed(Routes.appSettings, arguments: howToPlayLbl);
        break;
      case 'shareAppLbl':
        // TODO: Share functionality temporarily disabled
        try {
          UiUtils.share(
            '${context.read<SystemConfigCubit>().appUrl}\n${context.read<SystemConfigCubit>().shareAppText}',
            context: context,
          );
        } catch (e) {
          UiUtils.showSnackBar(e.toString(), context);
        }
        break;
      case 'rateUsLbl':
        launchUrl(Uri.parse(context.read<SystemConfigCubit>().appUrl));
        break;
      case 'notificationLbl':
        Navigator.of(context).pushNamed(Routes.notification);
        break;
      case 'bookmarkLbl':
        Navigator.of(context).pushNamed(Routes.bookmark);
        break;
      case 'statisticsLabel':
        Navigator.of(context).pushNamed(Routes.statistics);
        break;
      case 'logoutLbl':
        showLogoutDialog(context);
        break;
      case 'deleteAccountLbl':
        showDeleteAccountDialog(context);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final primaryColor = Theme.of(context).primaryColor;
    final topPadding = widget.fromNav 
        ? MediaQuery.of(context).padding.top + 25.0
        : 20.0;
    
    return Scaffold(
      appBar: !widget.fromNav
          ? AppBar(
              backgroundColor: primaryColor,
              elevation: 0,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: colorScheme.surface,
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              title: Text(
                context.tr('menuLbl') ?? 'Menu',
                style: TextStyle(
                  color: colorScheme.surface,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          : null,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              primaryColor,
              colorScheme.surface,
            ],
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              top: topPadding,
              left: 20.0,
              right: 20.0,
              child: _buildProfileCard(colorScheme),
            ),
            Positioned(
              top: topPadding + 140.0,
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -8),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: colorScheme.onSurface.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    Expanded(
                      child: _buildMenuGrid(colorScheme),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCard(ColorScheme colorScheme) {
    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      bloc: context.read<UserDetailsCubit>(),
      builder: (context, state) {
        if (state is UserDetailsFetchSuccess) {
          return Container(
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Row(
                    children: [
                      Hero(
                        tag: 'profile_image',
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 3,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 10,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: CircleAvatar(
                            radius: 35,
                            backgroundColor: colorScheme.primary,
                            backgroundImage: widget.isGuest
                                ? null
                                : CachedNetworkImageProvider(state.userProfile.profileUrl!),
                            child: widget.isGuest
                                ? const Icon(
                                    Icons.account_circle_outlined,
                                    size: 45,
                                    color: Colors.white,
                                  )
                                : null,
                          ),
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              widget.isGuest
                                  ? context.tr('helloGuest') ?? 'Guest'
                                  : state.userProfile.name ?? '',
                              style: const TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            if (!widget.isGuest && state.userProfile.email != null) ...[
                              const SizedBox(height: 6),
                              Text(
                                state.userProfile.email!,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          onPressed: _handleProfileEdit,
                          icon: const Icon(
                            Icons.edit_outlined,
                            color: Colors.white,
                            size: 22,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }
        return const SizedBox(height: 120);
      },
    );
  }

  Widget _buildMenuGrid(ColorScheme colorScheme) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    
    // حساب عدد الأعمدة بناءً على حجم الشاشة
    final crossAxisCount = size.width >= 1024 ? 4 : (isTablet ? 3 : 2);
    
    // تعديل الهوامش والمسافات للأجهزة اللوحية
    final horizontalPadding = size.width >= 1024 ? 60.0 : (isTablet ? 40.0 : 20.0);
    final spacing = size.width >= 1024 ? 30.0 : (isTablet ? 25.0 : 15.0);
    
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 1200, // تحديد الحد الأقصى للعرض
        ),
        child: GridView.builder(
          padding: EdgeInsets.fromLTRB(
            horizontalPadding,
            20.0,
            horizontalPadding,
            20.0
          ),
          physics: const BouncingScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: size.width >= 1024 ? 1.1 : (isTablet ? 1.0 : 0.9),
          ),
          itemCount: menu.length,
          itemBuilder: (context, index) => _buildMenuItem(menu[index], colorScheme, index),
        ),
      ),
    );
  }

  Widget _buildMenuItem(MenuOption item, ColorScheme colorScheme, int index) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    final isLargeTablet = size.width >= 1024;
    
    final iconSize = isLargeTablet ? 36.0 : (isTablet ? 32.0 : 26.0);
    final padding = isLargeTablet ? 22.0 : (isTablet ? 18.0 : 14.0);
    final fontSize = isLargeTablet ? 16.0 : (isTablet ? 15.0 : 13.0);
    
    return Hero(
      tag: 'menu_item_$index',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onPressed(item.name, context),
          borderRadius: BorderRadius.circular(24.0),
          child: Container(
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(24.0),
              border: Border.all(
                color: colorScheme.primary.withOpacity(0.1),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(padding),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    item.icon,
                    color: colorScheme.primary,
                    size: iconSize,
                  ),
                ),
                const SizedBox(height: 14.0),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Text(
                    context.tr(item.name) ?? item.name,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
