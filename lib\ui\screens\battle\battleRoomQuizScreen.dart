import 'dart:async';
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/battleRoom/battleRoomRepository.dart';
import 'package:flutterquiz/features/battleRoom/cubits/battleRoomCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/messageCubit.dart';
import 'package:flutterquiz/features/battleRoom/models/message.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/battle/battle_voice_chat_provider.dart';
import 'package:flutterquiz/ui/screens/battle/voice_chat_controller.dart';
import 'package:flutterquiz/ui/screens/battle/widgets/messageBoxContainer.dart';
import 'package:flutterquiz/ui/screens/battle/widgets/messageContainer.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/exitGameDialog.dart';
import 'package:flutterquiz/ui/widgets/questionsContainer.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/internet_connectivity.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

// رسام الخلفية المزخرفة للمعركة
class BattleBackgroundPainter extends CustomPainter {
  final Color color;

  BattleBackgroundPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // رسم الأشكال الزخرفية
    final random = Random(42); // استخدام بذرة ثابتة للحصول على نفس النمط دائمًا

    // رسم دوائر عشوائية
    for (var i = 0; i < 15; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = 5.0 + random.nextDouble() * 20.0;

      canvas.drawCircle(
        Offset(x, y),
        radius,
        paint,
      );
    }

    // رسم خطوط متقاطعة
    for (var i = 0; i < 10; i++) {
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;
      final endX = random.nextDouble() * size.width;
      final endY = random.nextDouble() * size.height;

      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        paint..strokeWidth = 1.0,
      );
    }

    // رسم مستطيلات بزوايا دائرية
    for (var i = 0; i < 5; i++) {
      final left = random.nextDouble() * size.width;
      final top = random.nextDouble() * size.height;
      final width = 20.0 + random.nextDouble() * 50.0;
      final height = 20.0 + random.nextDouble() * 50.0;

      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(left, top, width, height),
        const Radius.circular(8),
      );

      canvas.drawRRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class BattleRoomQuizScreen extends StatefulWidget {
  const BattleRoomQuizScreen({
    required this.playWithBot,
    required this.quizType,
    super.key,
  });

  final QuizTypes quizType;
  final bool playWithBot;

  static Route<dynamic> route(RouteSettings routeSettings) {
    final args = routeSettings.arguments! as Map;
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<UpdateBookmarkCubit>(
            create: (_) => UpdateBookmarkCubit(BookmarkRepository()),
          ),
          BlocProvider<MessageCubit>(
            create: (_) => MessageCubit(BattleRoomRepository()),
          ),
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
        ],
        child: BattleRoomQuizScreen(
          playWithBot: args['play_with_bot'] as bool? ?? false,
          quizType: args['quiz_type'] as QuizTypes,
        ),
      ),
    );
  }

  @override
  State<BattleRoomQuizScreen> createState() => _BattleRoomQuizScreenState();
}

class _BattleRoomQuizScreenState extends State<BattleRoomQuizScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // تم حذف متغير _isSpeakerEnabled غير المستخدم
  late AnimationController timerAnimationController = AnimationController(
    vsync: this,
    duration: Duration(
      seconds: context.read<SystemConfigCubit>().quizTimer(widget.quizType),
    ),
  )
    ..addStatusListener(currentUserTimerAnimationStatusListener)
    ..forward();

  late AnimationController opponentUserTimerAnimationController =
      AnimationController(
    vsync: this,
    duration: Duration(
      seconds: context.read<SystemConfigCubit>().quizTimer(widget.quizType),
    ),
  )..forward();

  //to animate the question container
  late AnimationController questionAnimationController;
  late AnimationController questionContentAnimationController;

  //to slide the question container from right to left
  late Animation<double> questionSlideAnimation;

  //to scale up the second question
  late Animation<double> questionScaleUpAnimation;

  //to scale down the second question
  late Animation<double> questionScaleDownAnimation;

  //to slide the question content from right to left
  late Animation<double> questionContentAnimation;

  late AnimationController messageAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 300),
    reverseDuration: const Duration(milliseconds: 300),
  );
  late Animation<double> messageAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: messageAnimationController,
      curve: Curves.easeOutBack,
    ),
  );

  late AnimationController opponentMessageAnimationController =
      AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 300),
    reverseDuration: const Duration(milliseconds: 300),
  );
  late Animation<double> opponentMessageAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: opponentMessageAnimationController,
      curve: Curves.easeOutBack,
    ),
  );

  late AnimationController messageBoxAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 350),
  );
  late Animation<double> messageBoxAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: messageBoxAnimationController,
      curve: Curves.easeInOut,
    ),
  );

  late int currentQuestionIndex = 0;

  //if user left the by pressing home button or lock screen
  //this will be true
  bool showYouLeftQuiz = false;

  //to track if setting dialog is open
  bool isSettingDialogOpen = false;

  bool isExitDialogOpen = false;

  // وحدة تحكم المحادثة الصوتية
  final VoiceChatController _voiceChatController = VoiceChatController();

  final double bottomPadding = 10;
  final double timerHeightAndWidthPercentage = 0.1; // نسبة ارتفاع وعرض المؤقت

  //current user message timer
  Timer? currentUserMessageDisappearTimer;
  int currentUserMessageDisappearTimeInSeconds = 4;

  //opponent user message timer
  Timer? opponentUserMessageDisappearTimer;
  int opponentUserMessageDisappearTimeInSeconds = 4;

  //To track users latest message

  List<Message> latestMessagesByUsers = [];

  late final _currUserId = context.read<UserDetailsCubit>().userId();

  final List<String> botSubmittedAnswers = [];

  @override
  void initState() {
    super.initState();

    WakelockPlus.enable();

    //Add empty latest messages
    latestMessagesByUsers
      ..add(Message.empty())
      ..add(Message.empty());
    //

    Future.delayed(Duration.zero, () {
      if (!widget.playWithBot) {
        context.read<UpdateScoreAndCoinsCubit>().updateCoins(
              coins: context.read<BattleRoomCubit>().getEntryFee(),
              title: playedBattleKey,
              addCoin: false,
            );
        context.read<UserDetailsCubit>().updateCoins(
              addCoin: false,
              coins: context.read<BattleRoomCubit>().getEntryFee(),
            );
      }

      // تهيئة المحادثة الصوتية
      _initializeVoiceChat();
    });

    initializeAnimation();
    initMessageListener();
    questionContentAnimationController.forward();
    WidgetsBinding.instance.addObserver(this);
  }

  // تهيئة المحادثة الصوتية
  Future<void> _initializeVoiceChat() async {
    final battleRoomCubit = context.read<BattleRoomCubit>();
    final state = battleRoomCubit.state;

    if (state is BattleRoomUserFound &&
        state.battleRoom.isVoiceChatEnabled == true &&
        state.battleRoom.voiceChannelId != null &&
        state.battleRoom.voiceChannelId!.isNotEmpty) {
      debugPrint("بدء تهيئة المحادثة الصوتية والانضمام إلى القناة...");

      try {
        // الانضمام إلى القناة الصوتية
        final uid = int.tryParse(_currUserId) ?? 0;

        // حفظ معرف القناة ومعرف المستخدم
        final channelId = state.battleRoom.voiceChannelId!;

        // تهيئة المحادثة الصوتية مباشرة باستخدام وحدة التحكم
        final initialized = await _voiceChatController.initialize();

        if (!initialized) {
          debugPrint("فشل في تهيئة المحادثة الصوتية");
          return;
        }

        // الانضمام إلى القناة
        final success = await _voiceChatController.joinChannel(channelId, uid);

        if (success) {
          debugPrint("تم تهيئة المحادثة الصوتية والانضمام إلى القناة بنجاح");

          // محاولة تمكين مكبر الصوت بعد الانضمام
          final speakerSuccess =
              await _voiceChatController.enableSpeakerphoneAfterJoin(
            delayMs: 1500, // تأخير أطول للتأكد من اكتمال الانضمام
          );

          if (speakerSuccess) {
            debugPrint("تم تمكين مكبر الصوت بعد الانضمام بنجاح");
          } else {
            debugPrint(
                "فشل في تمكين مكبر الصوت بعد الانضمام، محاولة مرة أخرى...");

            // محاولة ثانية بعد تأخير إضافي
            await Future.delayed(const Duration(seconds: 2));
            final secondAttempt = await _voiceChatController.toggleSpeakerphone(
              true,
              maxRetries: 5,
              delayMs: 800,
            );

            if (secondAttempt) {
              debugPrint("نجحت المحاولة الثانية لتمكين مكبر الصوت");
            } else {
              debugPrint("فشلت جميع محاولات تمكين مكبر الصوت");
            }
          }
        } else {
          debugPrint("فشل في تهيئة المحادثة الصوتية أو الانضمام إلى القناة");
        }
      } catch (e) {
        debugPrint("خطأ غير متوقع في تهيئة المحادثة الصوتية: $e");
      }
    } else {
      debugPrint("المحادثة الصوتية غير ممكنة أو معرف القناة غير متوفر");
    }
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    timerAnimationController
      ..removeStatusListener(currentUserTimerAnimationStatusListener)
      ..dispose();
    opponentUserTimerAnimationController.dispose();
    questionAnimationController.dispose();
    questionContentAnimationController.dispose();
    messageAnimationController.dispose();
    opponentMessageAnimationController.dispose();
    currentUserMessageDisappearTimer?.cancel();
    opponentUserMessageDisappearTimer?.cancel();
    _voiceChatController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  bool appWasPaused = false;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    //delete battle room
    if (state == AppLifecycleState.paused) {
      appWasPaused = true;
      //if user minimize or change the app

      deleteMessages(context.read<BattleRoomCubit>().getRoomId());
      context.read<BattleRoomCubit>().deleteUserFromRoom(_currUserId);
      context.read<BattleRoomCubit>().deleteBattleRoom(isGroupBattle: false);
    }
    //show you left the game
    if (state == AppLifecycleState.resumed && appWasPaused) {
      if (!context.read<BattleRoomCubit>().opponentLeftTheGame(_currUserId)) {
        setState(() {
          showYouLeftQuiz = true;
        });
      }

      timerAnimationController.stop();
      opponentUserTimerAnimationController.stop();
    }
  }

  void initMessageListener() {
    //to set listener for opponent message
    Future.delayed(Duration.zero, () {
      final roomId = context.read<BattleRoomCubit>().getRoomId();
      context.read<MessageCubit>().subscribeToMessages(roomId);
    });
  }

  void initializeAnimation() {
    questionAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    questionContentAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );

    questionSlideAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    questionScaleUpAnimation = Tween<double>(begin: 0, end: 0.1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0, 0.5, curve: Curves.easeInQuad),
      ),
    );
    questionScaleDownAnimation = Tween<double>(begin: 0, end: 0.05).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0.5, 1, curve: Curves.easeOutQuad),
      ),
    );
    questionContentAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionContentAnimationController,
        curve: Curves.easeInQuad,
      ),
    );
  }

  void toggleSettingDialog() {
    isSettingDialogOpen = !isSettingDialogOpen;
  }

  //listener for current user timer
  void currentUserTimerAnimationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      submitAnswer('-1');
    }
  }

  //to submit the answer
  Future<void> submitAnswer(String submittedAnswer) async {
    timerAnimationController.stop();

    //submitted answer will be id of the answerOption
    final battleRoomCubit = context.read<BattleRoomCubit>();
    final questions = battleRoomCubit.getQuestions();

    if (!questions[currentQuestionIndex].attempted) {
      //update answer locally
      battleRoomCubit.updateQuestionAnswer(
        questions[currentQuestionIndex].id,
        submittedAnswer,
      );

      //need to give the delay so user can see the correct answer or incorrect
      await Future<void>.delayed(
        const Duration(seconds: inBetweenQuestionTimeInSeconds),
      );

      /// update answer and current points in database
      final correctAnswer = AnswerEncryption.decryptCorrectAnswer(
        rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
        correctAnswer: questions[currentQuestionIndex].correctAnswer!,
      );

      final sysConfig = context.read<SystemConfigCubit>();
      battleRoomCubit.submitAnswer(
        _currUserId,
        submittedAnswer,
        isAnswerCorrect: submittedAnswer == correctAnswer,
        UiUtils.determineBattleCorrectAnswerPoints(
          timerAnimationController.value,
          sysConfig.quizTimer(widget.quizType),
          sysConfig.quizCorrectAnswerCreditScore(widget.quizType),
          widget.quizType == QuizTypes.oneVsOneBattle
              ? sysConfig.oneVsOneBattleQuickestCorrectAnswerExtraScore
              : sysConfig.randomBattleQuickestCorrectAnswerExtraScore,
          widget.quizType == QuizTypes.oneVsOneBattle
              ? sysConfig.oneVsOneBattleSecondQuickestCorrectAnswerExtraScore
              : sysConfig.randomBattleSecondQuickestCorrectAnswerExtraScore,
        ),
      );

      if (widget.playWithBot) {
        submitRobotAnswer();
      }
    }
  }

  void submitRobotAnswer() {
    opponentUserTimerAnimationController.stop();

    //submitted answer will be id of the answerOption
    final battleRoomCubit = context.read<BattleRoomCubit>();
    final questions = battleRoomCubit.getQuestions();

    final correctAnswer = AnswerEncryption.decryptCorrectAnswer(
      rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
      correctAnswer: questions[currentQuestionIndex].correctAnswer!,
    );

    final options = questions[currentQuestionIndex].answerOptions!.toList();
    final randomIdx = Random.secure().nextInt(options.length);
    final submittedAnswer = options[randomIdx].id!;

    botSubmittedAnswers.add(submittedAnswer);

    final sysConfig = context.read<SystemConfigCubit>();
    battleRoomCubit.submitAnswer(
      context.read<BattleRoomCubit>().getOpponentUserDetails(_currUserId).uid,
      submittedAnswer,
      isAnswerCorrect: submittedAnswer == correctAnswer,
      UiUtils.determineBattleCorrectAnswerPoints(
        opponentUserTimerAnimationController.value,
        sysConfig.quizTimer(QuizTypes.randomBattle),
        sysConfig.quizCorrectAnswerCreditScore(QuizTypes.randomBattle),
        widget.quizType == QuizTypes.oneVsOneBattle
            ? sysConfig.oneVsOneBattleQuickestCorrectAnswerExtraScore
            : sysConfig.randomBattleQuickestCorrectAnswerExtraScore,
        widget.quizType == QuizTypes.oneVsOneBattle
            ? sysConfig.oneVsOneBattleSecondQuickestCorrectAnswerExtraScore
            : sysConfig.randomBattleSecondQuickestCorrectAnswerExtraScore,
      ),
    );
  }

  //if user has submitted the answer for current question
  bool hasSubmittedAnswerForCurrentQuestion() {
    return context
        .read<BattleRoomCubit>()
        .getQuestions()[currentQuestionIndex]
        .attempted;
  }

  //next question
  void changeQuestion() {
    questionAnimationController.forward(from: 0).then((value) {
      //need to dispose the animation controllers
      questionAnimationController.dispose();
      questionContentAnimationController.dispose();
      //initializeAnimation again
      setState(() {
        initializeAnimation();
        currentQuestionIndex++;
      });
      //load content(options, image etc) of question
      questionContentAnimationController.forward();
    });
  }

  void deleteMessages(String battleRoomId) {
    //to delete messages by given user
    context.read<MessageCubit>().deleteMessages(battleRoomId, _currUserId);
  }

  //for changing ui and other trigger other actions based on realtime changes that occurred in game
  void battleRoomListener(
    BuildContext context,
    BattleRoomState state,
    BattleRoomCubit battleRoomCubit,
  ) {
    Future.delayed(Duration.zero, () async {
      if (await InternetConnectivity.isUserOffline()) {
        await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            shadowColor: Colors.transparent,
            actions: [
              TextButton(
                onPressed: () async {
                  if (!await InternetConnectivity.isUserOffline()) {
                    Navigator.of(context).pop(true);
                  }
                },
                child: Text(
                  context.tr('retryLbl')!,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
            content: Text(
              context.tr('noInternet')!,
            ),
          ),
        );
      }
    });

    if (state is BattleRoomUserFound) {
      final opponentUserDetails =
          battleRoomCubit.getOpponentUserDetails(_currUserId);
      final currentUserDetails =
          battleRoomCubit.getCurrentUserDetails(_currUserId);

      //if user has left the game
      if (state.hasLeft) {
        timerAnimationController.stop();
        opponentUserTimerAnimationController.stop();
      } else {
        //check if opponent user has submitted the answer
        if (opponentUserDetails.answers.length == (currentQuestionIndex + 1)) {
          opponentUserTimerAnimationController.stop();
        }
        //if both users submitted the answer then change question
        if (state.battleRoom.user1!.answers.length ==
            state.battleRoom.user2!.answers.length) {
          //
          //if user has not submitted the answers for all questions then move to next question
          //
          if (state.battleRoom.user1!.answers.length !=
              state.questions.length) {
            //
            //since submitting answer locally will change the cubit state
            //to avoid calling changeQuestion() called twice
            //need to add this condition
            //
            if (!state.questions[currentUserDetails.answers.length].attempted) {
              //stop any timer
              timerAnimationController.stop();
              opponentUserTimerAnimationController.stop();
              //change the question
              changeQuestion();
              //run timer again
              timerAnimationController.forward(from: 0);
              opponentUserTimerAnimationController.forward(from: 0);
            }
          }
          //else move to result screen
          else {
            //stop timers if any running
            timerAnimationController.stop();
            opponentUserTimerAnimationController.stop();

            //delete messages by current user
            deleteMessages(battleRoomCubit.getRoomId());
            //navigate to result
            if (isSettingDialogOpen) {
              Navigator.of(context).pop();
            }
            if (isExitDialogOpen) {
              Navigator.of(context).pop();
            }
            Navigator.of(context).pushReplacementNamed(
              Routes.result,
              arguments: {
                'questions': state.questions,
                'battleRoom': state.battleRoom,
                'numberOfPlayer': 2,
                'play_with_bot': widget.playWithBot,
                'quizType': QuizTypes.oneVsOneBattle,
                'entryFee': state.battleRoom.entryFee,
              },
            );

            battleRoomCubit.deleteBattleRoom(isGroupBattle: false);
          }
        }
      }
    }
  }

  void setCurrentUserMessageDisappearTimer() {
    if (currentUserMessageDisappearTimeInSeconds != 4) {
      currentUserMessageDisappearTimeInSeconds = 4;
    }

    currentUserMessageDisappearTimer =
        Timer.periodic(const Duration(seconds: 1), (timer) {
      if (currentUserMessageDisappearTimeInSeconds == 0) {
        //
        timer.cancel();
        messageAnimationController.reverse();
      } else {
        currentUserMessageDisappearTimeInSeconds--;
      }
    });
  }

  void setOpponentUserMessageDisappearTimer() {
    if (opponentUserMessageDisappearTimeInSeconds != 4) {
      opponentUserMessageDisappearTimeInSeconds = 4;
    }

    opponentUserMessageDisappearTimer =
        Timer.periodic(const Duration(seconds: 1), (timer) {
      if (opponentUserMessageDisappearTimeInSeconds == 0) {
        //
        timer.cancel();
        opponentMessageAnimationController.reverse();
      } else {
        opponentUserMessageDisappearTimeInSeconds--;
      }
    });
  }

  Future<void> messagesListener(MessageState state) async {
    if (state is MessageFetchedSuccess) {
      //current user message

      if (context
          .read<MessageCubit>()
          .getUserLatestMessage(
            //fetch user id
            _currUserId,
            messageId: latestMessagesByUsers[0].messageId,
            //latest user message id
          )
          .messageId
          .isNotEmpty) {
        //Assign latest message
        latestMessagesByUsers[0] =
            context.read<MessageCubit>().getUserLatestMessage(
                  _currUserId,
                  messageId: latestMessagesByUsers[0].messageId,
                );

        //Display latest message by current user
        //means timer is running
        if (currentUserMessageDisappearTimeInSeconds > 0 &&
            currentUserMessageDisappearTimeInSeconds < 4) {
          currentUserMessageDisappearTimer?.cancel();
          setCurrentUserMessageDisappearTimer();
        } else {
          await messageAnimationController.forward();
          setCurrentUserMessageDisappearTimer();
        }
      }

      // opponent user message

      if (context
          .read<MessageCubit>()
          .getUserLatestMessage(
            //fetch opponent user id
            context
                .read<BattleRoomCubit>()
                .getOpponentUserDetails(_currUserId)
                .uid,
            messageId: latestMessagesByUsers[1].messageId,
            //latest user message id
          )
          .messageId
          .isNotEmpty) {
        //Assign latest message
        latestMessagesByUsers[1] =
            context.read<MessageCubit>().getUserLatestMessage(
                  context
                      .read<BattleRoomCubit>()
                      .getOpponentUserDetails(_currUserId)
                      .uid,
                  messageId: latestMessagesByUsers[1].messageId,
                );

        //Display latest message by opponent user
        //means timer is running

        //means timer is running
        if (opponentUserMessageDisappearTimeInSeconds > 0 &&
            opponentUserMessageDisappearTimeInSeconds < 4) {
          opponentUserMessageDisappearTimer?.cancel();
          setOpponentUserMessageDisappearTimer();
        } else {
          await opponentMessageAnimationController.forward();
          setOpponentUserMessageDisappearTimer();
        }
      }
    }
  }

  Widget _buildCurrentUserMessageContainer() {
    return PositionedDirectional(
      start: 10,
      bottom: (bottomPadding * 2.5) +
          MediaQuery.of(context).size.width * timerHeightAndWidthPercentage,
      child: ScaleTransition(
        scale: messageAnimation,
        alignment: const Alignment(-0.5, 1),
        child: const MessageContainer(
          quizType: QuizTypes.oneVsOneBattle,
          isCurrentUser: true,
        ), //-0.5 left side nad 0.5 is right side,
      ),
    );
  }

  Widget _buildOpponentUserMessageContainer() {
    return PositionedDirectional(
      end: 10,
      bottom: (bottomPadding * 2.5) +
          MediaQuery.of(context).size.width * timerHeightAndWidthPercentage,
      child: ScaleTransition(
        scale: opponentMessageAnimation,
        alignment: const Alignment(0.5, 1),
        child: const MessageContainer(
          quizType: QuizTypes.oneVsOneBattle,
          isCurrentUser: false,
        ), //-0.5 left side nad 0.5 is right side,
      ),
    );
  }

  Widget _buildYouWonContainer(VoidCallback onPressed) {
    final textStyle = GoogleFonts.ibmPlexSansArabic(
      textStyle: TextStyle(color: Theme.of(context).primaryColor),
    );
    return Container(
      alignment: Alignment.center,
      color: Theme.of(context).colorScheme.surface.withOpacity(0.1),
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: AlertDialog(
        shadowColor: Colors.transparent,
        title: Text(
          context.tr('youWonLbl')!,
          style: textStyle,
        ),
        content: Text(
          context.tr('opponentLeftLbl')!,
          style: textStyle,
        ),
        actions: [
          CupertinoButton(
            onPressed: onPressed,
            child: Text(
              context.tr('okayLbl')!,
              style: textStyle,
            ),
          ),
        ],
      ),
    );
  }

  //if opponent user has left the game this dialog will be shown
  Widget _buildYouWonGameDialog() {
    return showYouLeftQuiz
        ? const SizedBox()
        : BlocBuilder<BattleRoomCubit, BattleRoomState>(
            bloc: context.read<BattleRoomCubit>(),
            builder: (context, state) {
              if (state is BattleRoomUserFound) {
                //show you won game only opponent user has left the game
                if (context
                    .read<BattleRoomCubit>()
                    .opponentLeftTheGame(_currUserId)) {
                  return _buildYouWonContainer(() {
                    deleteMessages(context.read<BattleRoomCubit>().getRoomId());

                    context.read<UpdateScoreAndCoinsCubit>().updateCoins(
                          coins:
                              context.read<BattleRoomCubit>().getEntryFee() * 2,
                          title: wonBattleKey,
                          addCoin: true,
                        );
                    context.read<UserDetailsCubit>().updateCoins(
                          addCoin: true,
                          coins:
                              context.read<BattleRoomCubit>().getEntryFee() * 2,
                        );
                    Navigator.of(context).pop();
                  });
                }
              }
              return const SizedBox();
            },
          );
  }

  //if currentUser has left the game
  Widget _buildCurrentUserLeftTheGame() {
    return showYouLeftQuiz
        ? ColoredBox(
            color: Theme.of(context).colorScheme.surface.withOpacity(0.12),
            child: Center(
              child: AlertDialog(
                shadowColor: Colors.transparent,
                content: Text(
                  context.tr('youLeftLbl')!,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                actions: [
                  CupertinoButton(
                    child: Text(
                      context.tr('okayLbl')!,
                      style: TextStyle(color: Theme.of(context).primaryColor),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),
          )
        : const SizedBox();
  }

  Widget _buildMessageButton() {
    return widget.playWithBot
        ? const SizedBox.shrink()
        : AnimatedBuilder(
            animation: messageBoxAnimationController,
            builder: (context, child) {
              return InkWell(
                onTap: () {
                  if (messageBoxAnimationController.isCompleted) {
                    messageBoxAnimationController.reverse();
                  } else {
                    messageBoxAnimationController.forward();
                  }
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.2),
                      width: 1.5,
                    ),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        CupertinoIcons.ellipses_bubble_fill,
                        color: Theme.of(context).primaryColor,
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        context.tr('messages')!,
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
  }

  Widget _buildMessageBoxContainer() {
    return Align(
      alignment: Alignment.topCenter,
      child: SlideTransition(
        position: messageBoxAnimation.drive(
          Tween<Offset>(begin: const Offset(1.5, 0), end: Offset.zero),
        ),
        child: Container(
          margin: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top + 60,
            left: 16,
            right: 16,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: MessageBoxContainer(
            quizType: QuizTypes.oneVsOneBattle,
            topPadding: 0,
            battleRoomId: context.read<BattleRoomCubit>().getRoomId(),
            closeMessageBox: messageBoxAnimationController.reverse,
          ),
        ),
      ),
    );
  }

  // تم حذف دالة _buildVoiceChatControls غير المستخدمة

  // تم حذف الدوال غير المستخدمة

  void onBackPressed(BattleRoomCubit battleRoomCubit) {
    isExitDialogOpen = true;
    //show warning
    showDialog<void>(
      context: context,
      builder: (context) {
        return ExitGameDialog(
          onTapYes: () {
            timerAnimationController.stop();
            opponentUserTimerAnimationController.stop();

            //delete messages
            deleteMessages(battleRoomCubit.getRoomId());
            battleRoomCubit
              ..deleteUserFromRoom(_currUserId)
              ..deleteBattleRoom(isGroupBattle: false);

            Navigator.of(context).pop();
            Navigator.of(context).pop();
          },
        );
      },
    ).then((value) => isExitDialogOpen = true);
  }

  @override
  Widget build(BuildContext context) {
    final battleRoomCubit = context.read<BattleRoomCubit>();
    return PopScope(
        canPop: showYouLeftQuiz &&
            !messageBoxAnimationController.isCompleted &&
            !battleRoomCubit.opponentLeftTheGame(_currUserId),
        onPopInvokedWithResult: (didPop, _) {
          if (didPop) return;
          onBackPressed(battleRoomCubit);
        },
        child: BattleVoiceChatProvider(
          controller: _voiceChatController,
          child: Scaffold(
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.arrow_back_ios_new,
                      color: Colors.white, size: 18),
                ),
                onPressed: () {
                  if (showYouLeftQuiz) {
                    Navigator.pop(context);
                  }
                  if (battleRoomCubit.opponentLeftTheGame(_currUserId)) {
                    return;
                  }
                  onBackPressed(battleRoomCubit);
                },
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: _buildMessageButton(),
                ),
              ],
            ),
            body: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withOpacity(0.8),
                    Theme.of(context).primaryColor.withOpacity(0.6),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: MultiBlocListener(
                listeners: [
                  BlocListener<BattleRoomCubit, BattleRoomState>(
                    bloc: battleRoomCubit,
                    listener: (context, state) {
                      battleRoomListener(context, state, battleRoomCubit);
                    },
                  ),
                  BlocListener<MessageCubit, MessageState>(
                    bloc: context.read<MessageCubit>(),
                    listener: (context, state) {
                      messagesListener(state);
                    },
                  ),
                  BlocListener<UpdateScoreAndCoinsCubit,
                      UpdateScoreAndCoinsState>(
                    listener: (context, state) {
                      if (state is UpdateScoreAndCoinsFailure) {
                        if (state.errorMessage == errorCodeUnauthorizedAccess) {
                          timerAnimationController.stop();
                          opponentUserTimerAnimationController.stop();
                          showAlreadyLoggedInDialog(context);
                        }
                      }
                    },
                  ),
                ],
                child: SafeArea(
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      // الخلفية المزخرفة
                      Positioned.fill(
                        child: CustomPaint(
                          painter: BattleBackgroundPainter(
                            color: Colors.white.withOpacity(0.05),
                          ),
                        ),
                      ),

                      // حاوية المستخدمين في الأعلى
                      Positioned(
                        top: 10,
                        left: 0,
                        right: 0,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            children: [
                              // معلومات المستخدم الحالي
                              Expanded(
                                child: BlocBuilder<BattleRoomCubit,
                                    BattleRoomState>(
                                  bloc: battleRoomCubit,
                                  builder: (context, state) {
                                    if (state is BattleRoomUserFound) {
                                      final currentUserDetails = battleRoomCubit
                                          .getCurrentUserDetails(_currUserId);
                                      return _buildUserProfileCard(
                                        name: currentUserDetails.name,
                                        profileUrl:
                                            currentUserDetails.profileUrl,
                                        correctAnswers: currentUserDetails
                                            .correctAnswers
                                            .toString(),
                                        totalQuestions: battleRoomCubit
                                            .getQuestions()
                                            .length
                                            .toString(),
                                        isMuted:
                                            BattleVoiceChatProvider.of(context)!
                                                .controller
                                                .isMicMuted,
                                        onMuteToggle: state.battleRoom
                                                        .isVoiceChatEnabled ==
                                                    true &&
                                                state.battleRoom
                                                        .voiceChannelId !=
                                                    null &&
                                                state.battleRoom.voiceChannelId!
                                                    .isNotEmpty
                                            ? () {
                                                setState(() {
                                                  // كتم/إلغاء كتم الميكروفون
                                                  BattleVoiceChatProvider.of(
                                                          context)!
                                                      .controller
                                                      .toggleMute();
                                                });
                                              }
                                            : null,
                                        isCurrentUser: true,
                                      );
                                    }
                                    return const SizedBox();
                                  },
                                ),
                              ),

                              // مؤشر المواجهة
                              Container(
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 10),
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  shape: BoxShape.circle,
                                ),
                                child: const Text(
                                  "VS",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),

                              // معلومات المنافس
                              Expanded(
                                child: BlocBuilder<BattleRoomCubit,
                                    BattleRoomState>(
                                  bloc: battleRoomCubit,
                                  builder: (context, state) {
                                    if (state is BattleRoomUserFound) {
                                      final opponent = battleRoomCubit
                                          .getOpponentUserDetails(_currUserId);
                                      return _buildUserProfileCard(
                                        name: opponent.name,
                                        profileUrl: opponent.profileUrl,
                                        correctAnswers:
                                            opponent.correctAnswers.toString(),
                                        totalQuestions: battleRoomCubit
                                            .getQuestions()
                                            .length
                                            .toString(),
                                        isMuted: true,
                                        isCurrentUser: false,
                                      );
                                    }
                                    return const SizedBox();
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // حاوية الأسئلة
                      Positioned(
                        top: 100,
                        bottom:
                            80, // زيادة المسافة من الأسفل لترك مساحة لأزرار التحكم في المحادثة الصوتية
                        left: 0,
                        right: 0,
                        child: BlocBuilder<BattleRoomCubit, BattleRoomState>(
                          bloc: battleRoomCubit,
                          builder: (context, state) {
                            return Container(
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(24),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 20,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(24),
                                child: QuestionsContainer(
                                  topPadding: 0,
                                  timerAnimationController:
                                      timerAnimationController,
                                  quizType: QuizTypes.oneVsOneBattle,
                                  answerMode: context
                                      .read<SystemConfigCubit>()
                                      .answerMode,
                                  lifeLines: const {},
                                  hasSubmittedAnswerForCurrentQuestion:
                                      hasSubmittedAnswerForCurrentQuestion,
                                  questions: battleRoomCubit.getQuestions(),
                                  submitAnswer: submitAnswer,
                                  questionContentAnimation:
                                      questionContentAnimation,
                                  questionScaleDownAnimation:
                                      questionScaleDownAnimation,
                                  questionScaleUpAnimation:
                                      questionScaleUpAnimation,
                                  questionSlideAnimation:
                                      questionSlideAnimation,
                                  currentQuestionIndex: currentQuestionIndex,
                                  questionAnimationController:
                                      questionAnimationController,
                                  questionContentAnimationController:
                                      questionContentAnimationController,
                                ),
                              ),
                            );
                          },
                        ),
                      ),

                      // عناصر أخرى
                      _buildMessageBoxContainer(),
                      _buildCurrentUserMessageContainer(),
                      _buildOpponentUserMessageContainer(),
                      // تم إزالة شريط التحكم في المحادثة الصوتية لأنه غير مهم
                      _buildYouWonGameDialog(),
                      _buildCurrentUserLeftTheGame(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ));
  }

  // بطاقة ملف المستخدم الجديدة
  Widget _buildUserProfileCard({
    required String name,
    required String profileUrl,
    required String correctAnswers,
    required String totalQuestions,
    required bool isMuted,
    bool isCurrentUser = false,
    VoidCallback? onMuteToggle,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isCurrentUser
            ? Colors.white.withOpacity(0.2)
            : Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          if (!isCurrentUser) ...[
            // معلومات المنافس (على اليمين)
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // زر كتم صوت المنافس - يظهر فقط إذا كانت المحادثة الصوتية مفعلة
                  BlocBuilder<BattleRoomCubit, BattleRoomState>(
                    builder: (context, state) {
                      if (state is BattleRoomUserFound &&
                          state.battleRoom.isVoiceChatEnabled == true &&
                          state.battleRoom.voiceChannelId != null &&
                          state.battleRoom.voiceChannelId!.isNotEmpty) {
                        // الحصول على معرف المنافس
                        final opponentUidStr = context
                            .read<BattleRoomCubit>()
                            .getOpponentUserDetails(_currUserId)
                            .uid;
                        final opponentUid = int.tryParse(opponentUidStr) ?? 0;

                        // حالة ميكروفون المنافس
                        final isOpponentMuted = opponentUid > 0
                            ? BattleVoiceChatProvider.of(context)!
                                .controller
                                .isRemoteMicMuted(opponentUid)
                            : true; // إذا كان المعرف غير صالح، نفترض أن الميكروفون مكتوم

                        // مستوى صوت المنافس - مع التحقق من وجود وحدة التحكم
                        final voiceChatProvider =
                            BattleVoiceChatProvider.of(context);
                        final opponentVolumeLevel =
                            (voiceChatProvider != null && opponentUid > 0)
                                ? voiceChatProvider.controller
                                    .getRemoteUserVolumeLevel(opponentUid)
                                : 0.0;

                        // تحديد الألوان بناءً على حالة الميكروفون
                        final Color micColor =
                            isOpponentMuted ? Colors.red : Colors.green;

                        return GestureDetector(
                          onTap: () {
                            final voiceChatProvider =
                                BattleVoiceChatProvider.of(context);
                            if (voiceChatProvider != null && opponentUid > 0) {
                              setState(() {
                                // كتم/إلغاء كتم ميكروفون المنافس
                                voiceChatProvider.controller
                                    .toggleRemoteMute(opponentUid);
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            margin: const EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              color: micColor.withOpacity(0.2),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: micColor.withOpacity(0.3),
                                width: 1.5,
                              ),
                              boxShadow: [
                                if (!isOpponentMuted &&
                                    opponentVolumeLevel > 0.05)
                                  BoxShadow(
                                    color: micColor
                                        .withOpacity(0.3 * opponentVolumeLevel),
                                    blurRadius: 8 * opponentVolumeLevel,
                                    spreadRadius: 2 * opponentVolumeLevel,
                                  ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                // أيقونة الميكروفون
                                Icon(
                                  isOpponentMuted ? Icons.mic_off : Icons.mic,
                                  color: micColor,
                                  size: 14,
                                ),

                                // مؤشر مستوى الصوت
                                if (!isOpponentMuted &&
                                    opponentVolumeLevel > 0.05)
                                  Positioned.fill(
                                    child: CustomPaint(
                                      painter: VoiceLevelPainter(
                                        level: opponentVolumeLevel,
                                        color: micColor,
                                        barCount: 4,
                                        barWidth: 2,
                                        spacing: 1,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      }
                      return const SizedBox();
                    },
                  ),

                  // معلومات المنافس
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          name,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.end,
                        ),
                        const SizedBox(height: 4),
                        // عدد الإجابات الصحيحة
                        Text(
                          "$correctAnswers/$totalQuestions",
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            // صورة المنافس مع مؤشر حالة الميكروفون
            Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1.5,
                    ),
                  ),
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.grey[300],
                    backgroundImage: profileUrl.isNotEmpty &&
                            profileUrl != "null" &&
                            Uri.tryParse(profileUrl)?.hasScheme == true
                        ? NetworkImage(profileUrl)
                        : null,
                    child: profileUrl.isEmpty ||
                            profileUrl == "null" ||
                            Uri.tryParse(profileUrl)?.hasScheme != true
                        ? const Icon(Icons.person,
                            size: 16, color: Colors.white)
                        : null,
                  ),
                ),

                // مؤشر حالة الميكروفون للمنافس
                BlocBuilder<BattleRoomCubit, BattleRoomState>(
                  builder: (context, state) {
                    if (state is BattleRoomUserFound &&
                        state.battleRoom.isVoiceChatEnabled == true &&
                        state.battleRoom.voiceChannelId != null &&
                        state.battleRoom.voiceChannelId!.isNotEmpty) {
                      // الحصول على معرف المنافس
                      final opponentUidStr = context
                          .read<BattleRoomCubit>()
                          .getOpponentUserDetails(_currUserId)
                          .uid;
                      final opponentUid = int.tryParse(opponentUidStr) ?? 0;

                      // حالة ميكروفون المنافس - مع التحقق من وجود وحدة التحكم
                      final voiceChatProvider =
                          BattleVoiceChatProvider.of(context);
                      final isOpponentMuted = (voiceChatProvider != null &&
                              opponentUid > 0)
                          ? voiceChatProvider.controller
                              .isRemoteMicMuted(opponentUid)
                          : true; // إذا كان المعرف غير صالح أو لا توجد وحدة تحكم، نفترض أن الميكروفون مكتوم

                      // تحديد الألوان بناءً على حالة الميكروفون
                      final Color micColor =
                          isOpponentMuted ? Colors.red : Colors.green;

                      return Positioned(
                        left: 0,
                        bottom: 0,
                        child: Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            color: micColor,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            isOpponentMuted ? Icons.mic_off : Icons.mic,
                            color: Colors.white,
                            size: 6,
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ] else ...[
            // صورة المستخدم الحالي مع مؤشر حالة الميكروفون
            Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1.5,
                    ),
                  ),
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.grey[300],
                    backgroundImage: profileUrl.isNotEmpty &&
                            profileUrl != "null" &&
                            Uri.tryParse(profileUrl)?.hasScheme == true
                        ? NetworkImage(profileUrl)
                        : null,
                    child: profileUrl.isEmpty ||
                            profileUrl == "null" ||
                            Uri.tryParse(profileUrl)?.hasScheme != true
                        ? const Icon(Icons.person,
                            size: 16, color: Colors.white)
                        : null,
                  ),
                ),

                // مؤشر حالة الميكروفون للمستخدم الحالي
                BlocBuilder<BattleRoomCubit, BattleRoomState>(
                  builder: (context, state) {
                    if (state is BattleRoomUserFound &&
                        state.battleRoom.isVoiceChatEnabled == true &&
                        state.battleRoom.voiceChannelId != null &&
                        state.battleRoom.voiceChannelId!.isNotEmpty) {
                      // حالة ميكروفون المستخدم الحالي - مع التحقق من وجود وحدة التحكم
                      final voiceChatProvider =
                          BattleVoiceChatProvider.of(context);
                      final isLocalMuted = voiceChatProvider != null
                          ? voiceChatProvider.controller.isMicMuted
                          : true; // إذا لا توجد وحدة تحكم، نفترض أن الميكروفون مكتوم

                      // تحديد الألوان بناءً على حالة الميكروفون
                      final Color micColor =
                          isLocalMuted ? Colors.red : Colors.green;

                      return Positioned(
                        left: 0,
                        bottom: 0,
                        child: Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            color: micColor,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            isLocalMuted ? Icons.mic_off : Icons.mic,
                            color: Colors.white,
                            size: 6,
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
            const SizedBox(width: 8),
            // معلومات المستخدم الحالي (على اليسار)
            Expanded(
              child: Row(
                children: [
                  // معلومات المستخدم
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          name,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        // عدد الإجابات الصحيحة
                        Text(
                          "$correctAnswers/$totalQuestions",
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // زر كتم صوت المستخدم الحالي - يظهر فقط إذا كانت المحادثة الصوتية مفعلة
                  BlocBuilder<BattleRoomCubit, BattleRoomState>(
                    builder: (context, state) {
                      if (state is BattleRoomUserFound &&
                          state.battleRoom.isVoiceChatEnabled == true &&
                          state.battleRoom.voiceChannelId != null &&
                          state.battleRoom.voiceChannelId!.isNotEmpty &&
                          onMuteToggle != null) {
                        // مستوى صوت المستخدم الحالي - مع التحقق من وجود وحدة التحكم
                        final voiceChatProvider =
                            BattleVoiceChatProvider.of(context);
                        final localVolumeLevel = voiceChatProvider != null
                            ? voiceChatProvider.controller
                                .getLocalUserVolumeLevel()
                            : 0.0;

                        // تحديد الألوان بناءً على حالة الميكروفون
                        final Color micColor =
                            isMuted ? Colors.red : Colors.green;

                        return GestureDetector(
                          onTap: onMuteToggle,
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: micColor.withOpacity(0.2),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: micColor.withOpacity(0.3),
                                width: 1.5,
                              ),
                              boxShadow: [
                                if (!isMuted && localVolumeLevel > 0.05)
                                  BoxShadow(
                                    color: micColor
                                        .withOpacity(0.3 * localVolumeLevel),
                                    blurRadius: 8 * localVolumeLevel,
                                    spreadRadius: 2 * localVolumeLevel,
                                  ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                // أيقونة الميكروفون
                                Icon(
                                  isMuted ? Icons.mic_off : Icons.mic,
                                  color: micColor,
                                  size: 14,
                                ),

                                // مؤشر مستوى الصوت
                                if (!isMuted && localVolumeLevel > 0.05)
                                  Positioned.fill(
                                    child: CustomPaint(
                                      painter: VoiceLevelPainter(
                                        level: localVolumeLevel,
                                        color: micColor,
                                        barCount: 4,
                                        barWidth: 2,
                                        spacing: 1,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// رسام مستوى الصوت المخصص
/// يرسم أشرطة مستوى الصوت
class VoiceLevelPainter extends CustomPainter {
  final double level;
  final Color color;
  final int barCount;
  final double barWidth;
  final double spacing;

  VoiceLevelPainter({
    required this.level,
    required this.color,
    this.barCount = 4,
    this.barWidth = 2,
    this.spacing = 1,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final totalWidth = (barWidth + spacing) * barCount - spacing;
    final startX = (size.width - totalWidth) / 2;
    final maxHeight = size.height * 0.8;

    for (int i = 0; i < barCount; i++) {
      final barHeight = maxHeight * ((i + 1) / barCount) * level;
      final barX = startX + i * (barWidth + spacing);
      final barY = (size.height - barHeight) / 2;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(barX, barY, barWidth, barHeight),
          const Radius.circular(1),
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(VoiceLevelPainter oldDelegate) {
    return oldDelegate.level != level || oldDelegate.color != color;
  }
}
