import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_tex/flutter_tex.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/audioQuestionBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/guessTheWordBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/customAppbar.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';

class BookmarkScreen extends StatefulWidget {
  const BookmarkScreen({super.key});

  @override
  State<BookmarkScreen> createState() => _BookmarkScreenState();
}

class _BookmarkScreenState extends State<BookmarkScreen>
    with SingleTickerProviderStateMixin {
  late TabController tabController;
  late List<(String, Widget)> tabs = <(String, Widget)>[
    (quizZone, _buildQuizZoneQuestions()),
    (guessTheWord, _buildGuessTheWordQuestions()),
    (audioQuestionsKey, _buildAudioQuestions()),
  ];

  late final isLatexModeEnabled =
      context.read<SystemConfigCubit>().isLatexModeEnabled;

  @override
  void initState() {
    super.initState();

    // Remove disabled quizzes
    final sysConfig = context.read<SystemConfigCubit>();
    if (!sysConfig.isQuizZoneEnabled) {
      tabs.removeWhere((t) => t.$1 == quizZone);
    }
    if (!sysConfig.isGuessTheWordEnabled) {
      tabs.removeWhere((t) => t.$1 == guessTheWord);
    }
    if (!sysConfig.isAudioQuizEnabled) {
      tabs.removeWhere((t) => t.$1 == audioQuestionsKey);
    }

    tabController = TabController(length: tabs.length, vsync: this);
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  void openBottomSheet({
    required String question,
    required String? imageUrl,
    bool isLatex = false,
  }) {
    showModalBottomSheet<void>(
      shape: const RoundedRectangleBorder(
        borderRadius: UiUtils.bottomSheetTopRadius,
      ),
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      isScrollControlled: true,
      context: context,
      builder: (_) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: UiUtils.bottomSheetTopRadius,
          ),
          height: MediaQuery.sizeOf(context).height * .7,
          margin: MediaQuery.of(context).viewInsets,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 15),

              /// Title
              Text(
                context.tr(tabs[tabController.index].$1)!,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onTertiary,
                ),
              ),
              const Divider(),

              if (isLatex) ...[
                Expanded(
                    child: SingleChildScrollView(
                  child: Text(
                    question,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeights.regular,
                      color: Theme.of(context).colorScheme.onTertiary,
                    ),
                  ),
                  // TeXView(
                  //   child: TeXViewColumn(
                  //     children: [
                  //       TeXViewDocument(
                  //         question,
                  //         style: TeXViewStyle(
                  //           fontStyle: TeXViewFontStyle(
                  //             sizeUnit: TeXViewSizeUnit.pixels,
                  //             fontSize: 18,
                  //             fontWeight: TeXViewFontWeight.bold,
                  //           ),
                  //           margin: const TeXViewMargin.only(
                  //             bottom: 30,
                  //             sizeUnit: TeXViewSizeUnit.pixels,
                  //           ),
                  //         ),
                  //       ),
                  //       if (imageUrl != null && imageUrl != '') ...[
                  //         TeXViewContainer(
                  //           child: TeXViewImage.network(imageUrl),
                  //           style: const TeXViewStyle(
                  //             margin: TeXViewMargin.only(
                  //               bottom: 15,
                  //               sizeUnit: TeXViewSizeUnit.pixels,
                  //             ),
                  //             borderRadius: TeXViewBorderRadius.all(
                  //               10,
                  //               sizeUnit: TeXViewSizeUnit.pixels,
                  //             ),
                  //           ),
                  //         ),
                  //       ],
                  //     ],
                  //   ),
                  //   style: TeXViewStyle(
                  //     margin: const TeXViewMargin.all(
                  //       20,
                  //       sizeUnit: TeXViewSizeUnit.pixels,
                  //     ),
                  //     contentColor: Theme.of(context).colorScheme.onTertiary,
                  //     fontStyle: TeXViewFontStyle(
                  //       sizeUnit: TeXViewSizeUnit.pixels,
                  //       fontSize: 16,
                  //       fontWeight: TeXViewFontWeight.normal,
                  //     ),
                  //   ),
                  //   // renderingEngine: const TeXViewRenderingEngine.katex(),
                  // ),
                )),
              ] else ...[
                Flexible(
                  fit: FlexFit.tight,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width *
                          UiUtils.hzMarginPct,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 15),

                        Text(
                          question,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeights.regular,
                            fontSize: 18,
                            color: Theme.of(context).colorScheme.onTertiary,
                          ),
                        ),

                        /// Image
                        if (imageUrl != null && imageUrl != '') ...[
                          const SizedBox(height: 30),
                          Center(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              width: MediaQuery.of(context).size.width * .9,
                              height: MediaQuery.of(context).size.width * .5,
                              alignment: Alignment.center,
                              child: CachedNetworkImage(
                                placeholder: (_, __) => const Center(
                                  child: CircularProgressContainer(),
                                ),
                                imageUrl: imageUrl,
                                imageBuilder: (context, imageProvider) {
                                  return InteractiveViewer(
                                    boundaryMargin: const EdgeInsets.all(20),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        image: DecorationImage(
                                          image: imageProvider,
                                          fit: BoxFit.cover,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  );
                                },
                                errorWidget: (_, i, e) {
                                  return Center(
                                    child: Icon(
                                      Icons.error,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],

                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuizZoneQuestions() {
    final bookmarkCubit = context.read<BookmarkCubit>();
    return BlocBuilder<BookmarkCubit, BookmarkState>(
      builder: (context, state) {
        if (state is BookmarkFetchSuccess) {
          if (state.questions.isEmpty) {
            return noBookmarksFound();
          }

          return Stack(
            children: [
              Align(
                alignment: Alignment.topCenter,
                child: SizedBox(
                  height: MediaQuery.of(context).size.height * .65,
                  child: ListView.separated(
                    itemBuilder: (_, index) {
                      final question = state.questions[index];

                      //providing updateBookmarkCubit to every bookmarked question
                      return BlocProvider<UpdateBookmarkCubit>(
                        create: (_) =>
                            UpdateBookmarkCubit(BookmarkRepository()),
                        //using builder so we can access the recently provided cubit
                        child: Builder(
                          builder: (context) => BlocConsumer<
                              UpdateBookmarkCubit, UpdateBookmarkState>(
                            bloc: context.read<UpdateBookmarkCubit>(),
                            listener: (_, state) {
                              if (state is UpdateBookmarkSuccess) {
                                bookmarkCubit
                                    .removeBookmarkQuestion(question.id!);
                              }
                              if (state is UpdateBookmarkFailure) {
                                UiUtils.showSnackBar(
                                  context.tr(
                                    convertErrorCodeToLanguageKey(
                                      errorCodeUpdateBookmarkFailure,
                                    ),
                                  )!,
                                  context,
                                );
                              }
                            },
                            builder: (context, state) {
                              return BookmarkCard(
                                queId: question.id!,
                                index: '${index + 1}',
                                title: question.question!,
                                type: '1',
                                // type QuizZone
                                isLatex: isLatexModeEnabled,
                                onTap: () {
                                  openBottomSheet(
                                    question: question.question!,
                                    imageUrl: question.imageUrl,
                                    isLatex: isLatexModeEnabled,
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      );
                    },
                    itemCount: state.questions.length,
                    separatorBuilder: (_, i) =>
                        const SizedBox(height: UiUtils.listTileGap),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 25),
                  child: BlocBuilder<BookmarkCubit, BookmarkState>(
                    builder: (context, state) {
                      if (state is BookmarkFetchSuccess &&
                          state.questions.isNotEmpty) {
                        return CustomRoundedButton(
                          widthPercentage: 1,
                          backgroundColor: Theme.of(context).primaryColor,
                          buttonTitle: context.tr('playBookmarkBtn'),
                          radius: 8,
                          showBorder: false,
                          fontWeight: FontWeights.semiBold,
                          height: 58,
                          titleColor: Theme.of(context).colorScheme.surface,
                          onTap: () {
                            Navigator.of(context).pushNamed(
                              Routes.bookmarkQuiz,
                              arguments: QuizTypes.quizZone,
                            );
                          },
                          elevation: 6.5,
                          textSize: 18,
                        );
                      }
                      return const SizedBox();
                    },
                  ),
                ),
              ),
            ],
          );
        }
        if (state is BookmarkFetchFailure) {
          return ErrorContainer(
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessageCode),
            showErrorImage: true,
            errorMessageColor: Theme.of(context).primaryColor,
            onTapRetry: () {
              context.read<BookmarkCubit>().getBookmark();
            },
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildAudioQuestions() {
    final bookmarkCubit = context.read<AudioQuestionBookmarkCubit>();
    return BlocBuilder<AudioQuestionBookmarkCubit, AudioQuestionBookMarkState>(
      bloc: bookmarkCubit,
      builder: (context, state) {
        if (state is AudioQuestionBookmarkFetchSuccess) {
          if (state.questions.isEmpty) {
            return noBookmarksFound();
          }

          return Stack(
            children: [
              Align(
                alignment: Alignment.topCenter,
                child: SizedBox(
                  height: MediaQuery.of(context).size.height * .65,
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      final question = state.questions[index];

                      //providing updateBookmarkCubit to every bookmarekd question
                      return BlocProvider<UpdateBookmarkCubit>(
                        create: (_) =>
                            UpdateBookmarkCubit(BookmarkRepository()),
                        //using builder so we can access the recently provided cubit
                        child: Builder(
                          builder: (context) => BlocConsumer<
                              UpdateBookmarkCubit, UpdateBookmarkState>(
                            bloc: context.read<UpdateBookmarkCubit>(),
                            listener: (context, state) {
                              if (state is UpdateBookmarkSuccess) {
                                bookmarkCubit
                                    .removeBookmarkQuestion(question.id!);
                              }
                              if (state is UpdateBookmarkFailure) {
                                UiUtils.showSnackBar(
                                  context.tr(
                                    convertErrorCodeToLanguageKey(
                                      errorCodeUpdateBookmarkFailure,
                                    ),
                                  )!,
                                  context,
                                );
                              }
                            },
                            builder: (context, state) {
                              return BookmarkCard(
                                queId: question.id!,
                                index: '${index + 1}',
                                title: question.question!,
                                type: '4',
                                onTap: state is UpdateBookmarkInProgress
                                    ? () {}
                                    : () {
                                        openBottomSheet(
                                          question: question.question!,
                                          imageUrl: '',
                                        );
                                      }, // type Audio Quiz
                              );
                            },
                          ),
                        ),
                      );
                    },
                    itemCount: state.questions.length,
                    separatorBuilder: (_, i) => SizedBox(
                      height: MediaQuery.of(context).size.height * 0.015,
                    ),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 25),
                  child: BlocBuilder<AudioQuestionBookmarkCubit,
                      AudioQuestionBookMarkState>(
                    builder: (context, state) {
                      if (state is AudioQuestionBookmarkFetchSuccess &&
                          state.questions.isNotEmpty) {
                        return CustomRoundedButton(
                          widthPercentage: 1,
                          backgroundColor: Theme.of(context).primaryColor,
                          buttonTitle: context.tr('playBookmarkBtn'),
                          radius: 8,
                          showBorder: false,
                          fontWeight: FontWeight.w500,
                          height: 58,
                          titleColor: Theme.of(context).colorScheme.surface,
                          onTap: () {
                            Navigator.of(context).pushNamed(
                              Routes.bookmarkQuiz,
                              arguments: QuizTypes.audioQuestions,
                            );
                          },
                          elevation: 6.5,
                          textSize: 18,
                        );
                      }
                      return const SizedBox();
                    },
                  ),
                ),
              ),
            ],
          );
        }
        if (state is AudioQuestionBookmarkFetchFailure) {
          return ErrorContainer(
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessageCode),
            showErrorImage: true,
            errorMessageColor: Theme.of(context).primaryColor,
            onTapRetry: () {
              context.read<AudioQuestionBookmarkCubit>().getBookmark();
            },
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Center noBookmarksFound() => Center(
        child: Text(
          context.tr('noBookmarkQueLbl')!,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onTertiary,
            fontSize: 20,
          ),
        ),
      );

  Widget _buildGuessTheWordQuestions() {
    final bookmarkCubit = context.read<GuessTheWordBookmarkCubit>();
    return BlocBuilder<GuessTheWordBookmarkCubit, GuessTheWordBookmarkState>(
      bloc: context.read<GuessTheWordBookmarkCubit>(),
      builder: (context, state) {
        if (state is GuessTheWordBookmarkFetchSuccess) {
          if (state.questions.isEmpty) {
            return noBookmarksFound();
          }

          return Stack(
            children: [
              SizedBox(
                height: MediaQuery.of(context).size.height * .65,
                child: ListView.separated(
                  separatorBuilder: (_, i) => SizedBox(
                    height: MediaQuery.of(context).size.height * 0.015,
                  ),
                  itemBuilder: (context, index) {
                    final question = state.questions[index];

                    //providing updateBookmarkCubit to every bookmarked question
                    return BlocProvider<UpdateBookmarkCubit>(
                      create: (context) =>
                          UpdateBookmarkCubit(BookmarkRepository()),
                      //using builder so we can access the recently provided cubit
                      child: Builder(
                        builder: (context) => BlocConsumer<UpdateBookmarkCubit,
                            UpdateBookmarkState>(
                          bloc: context.read<UpdateBookmarkCubit>(),
                          listener: (context, state) {
                            if (state is UpdateBookmarkSuccess) {
                              bookmarkCubit.removeBookmarkQuestion(
                                question.id,
                              );
                            }
                            if (state is UpdateBookmarkFailure) {
                              UiUtils.showSnackBar(
                                context.tr(
                                  convertErrorCodeToLanguageKey(
                                    errorCodeUpdateBookmarkFailure,
                                  ),
                                )!,
                                context,
                              );
                            }
                          },
                          builder: (context, state) {
                            return BookmarkCard(
                              queId: question.id,
                              index: '${index + 1}',
                              title: question.question,
                              type: '3',
                              onTap: () {
                                openBottomSheet(
                                  question: question.question,
                                  imageUrl: question.image,
                                );
                              },
                            );
                          },
                        ),
                      ),
                    );
                  },
                  itemCount: state.questions.length,
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 25),
                  child: BlocBuilder<GuessTheWordBookmarkCubit,
                      GuessTheWordBookmarkState>(
                    builder: (context, state) {
                      if (state is GuessTheWordBookmarkFetchSuccess &&
                          state.questions.isNotEmpty) {
                        return CustomRoundedButton(
                          widthPercentage: 1,
                          backgroundColor: Theme.of(context).primaryColor,
                          buttonTitle: context.tr('playBookmarkBtn'),
                          radius: 8,
                          showBorder: false,
                          fontWeight: FontWeight.w500,
                          height: 58,
                          titleColor: Theme.of(context).colorScheme.surface,
                          onTap: () {
                            Navigator.of(context).pushNamed(
                              Routes.bookmarkQuiz,
                              arguments: QuizTypes.guessTheWord,
                            );
                          },
                          elevation: 6.5,
                          textSize: 18,
                        );
                      }
                      return const SizedBox();
                    },
                  ),
                ),
              ),
            ],
          );
        }
        if (state is GuessTheWordBookmarkFetchFailure) {
          return ErrorContainer(
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessageCode),
            showErrorImage: true,
            errorMessageColor: Theme.of(context).primaryColor,
            onTapRetry: () =>
                context.read<GuessTheWordBookmarkCubit>().getBookmark(),
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: QAppBar(
        title: Text(
          context.tr(bookmarkLbl) ?? "المفضلة",
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          isScrollable: true,
          controller: tabController,
          tabs: tabs
              .map(
                (tab) => Tab(
                  text: context.tr(tab.$1) ?? "المفضلة",
                ),
              )
              .toList(),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withOpacity(0.8),
              Theme.of(context).primaryColor.withOpacity(0.6),
              Colors.white,
            ],
            stops: const [0.0, 0.3, 0.5, 0.8],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: MediaQuery.of(context).size.height * 0.02,
            horizontal: MediaQuery.of(context).size.width * 0.05,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                  blurRadius: 20,
                  spreadRadius: 2,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: TabBarView(
                controller: tabController,
                children: tabs.map((tab) => tab.$2).toList(),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class BookmarkCard extends StatelessWidget {
  const BookmarkCard({
    required this.index,
    required this.title,
    required this.queId,
    required this.type,
    required this.onTap,
    super.key,
    this.isLatex = false,
  });

  final String index;
  final String title;
  final String queId;
  final String type;
  final bool isLatex;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 1,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // رقم السؤال
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withOpacity(0.7),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                    blurRadius: 5,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              width: 40,
              height: 40,
              child: Center(
                child: Text(
                  index,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 15),

            // عنوان السؤال
            Expanded(
              child: isLatex
                  ? Text(
                      title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    )
                  // ? TeXView(
                  //     child: TeXViewGroup(
                  //       children: [
                  //         TeXViewGroupItem(
                  //           id: '-',
                  //           child: TeXViewDocument(
                  //             title,
                  //             style: TeXViewStyle(
                  //               contentColor: Theme.of(context).primaryColor,
                  //               fontStyle: TeXViewFontStyle(
                  //                 sizeUnit: TeXViewSizeUnit.pixels,
                  //                 fontSize: 16,
                  //                 fontWeight: TeXViewFontWeight.w500,
                  //               ),
                  //               margin: const TeXViewMargin.only(
                  //                 bottom: 10,
                  //                 sizeUnit: TeXViewSizeUnit.pixels,
                  //               ),
                  //             ),
                  //           ),
                  //         ),
                  //       ],
                  //       onTap: (_) => onTap(),
                  //     ),
                  //     // renderingEngine: const TeXViewRenderingEngine.katex(),
                  //   )
                  : Text(
                      title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
            ),

            // زر الحذف
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.red.withOpacity(0.1),
              ),
              child: IconButton(
                onPressed: () {
                  context.read<UpdateBookmarkCubit>().updateBookmark(
                        queId,
                        '0',
                        type,
                      );
                },
                icon: const Icon(
                  Icons.delete_outline_rounded,
                  color: Colors.red,
                  size: 22,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 40,
                  minHeight: 40,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
