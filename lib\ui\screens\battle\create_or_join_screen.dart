import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/ads/rewarded_ad_cubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/battleRoomCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/multiUserBattleRoomCubit.dart';
// import 'package:flutterquiz/features/inAppPurchase/in_app_product.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/features/systemConfig/model/room_code_char_type.dart';
import 'package:flutterquiz/ui/screens/battle/widgets/battle_invite_card.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/ui/widgets/watchRewardAdDialog.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class CreateOrJoinRoomScreen extends StatefulWidget {
  const CreateOrJoinRoomScreen({
    required this.quizType,
    required this.title,
    super.key,
  });

  final QuizTypes quizType;
  final String title;

  @override
  State<CreateOrJoinRoomScreen> createState() => _CreateOrJoinRoomScreenState();
}

class _CreateOrJoinRoomScreenState extends State<CreateOrJoinRoomScreen> {
  late final bool isInAppPurchaseEnabled;
  // List<InAppProduct> iapProducts = [];

  List<String> questionsNumbersList = [
    '10',
    '15',
    '20',
    '30',
    '40',
    '60',
    '80',
  ];

  String selectedQuestionsNumber = '10';
  String selectedCategory = selectCategoryKey;
  String selectedCategoryId = '0';
  String selectedCategoryImage = '';
  TextEditingController customEntryFee = TextEditingController(text: '');
  late final int minEntryCoins;
  late List<int> entryFees;
  late int entryFee = entryFees.first;
  bool enableVoiceChat = false;

  /// Screen Dimensions
  double get scrWidth => MediaQuery.of(context).size.width;

  double get scrHeight => MediaQuery.of(context).size.height;

  @override
  void initState() {
    super.initState();
    WakelockPlus.enable();
    isInAppPurchaseEnabled =
        context.read<SystemConfigCubit>().isCoinStoreEnabled;
    minEntryCoins = widget.quizType == QuizTypes.oneVsOneBattle
        ? context.read<SystemConfigCubit>().oneVsOneBattleMinimumEntryFee
        : context.read<SystemConfigCubit>().groupBattleMinimumEntryFee;

    entryFees = [
      minEntryCoins,
      minEntryCoins * 2,
      minEntryCoins * 3,
      minEntryCoins * 4,
    ];

    Future.delayed(Duration.zero, () {
      context.read<RewardedAdCubit>().createRewardedAd(context);
      if (isCategoryEnabled()) {
        _getCategories();
      }
    });
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    super.dispose();
  }

  void _getCategories() {
    context.read<QuizCategoryCubit>().getQuizCategoryWithUserId(
          languageId: UiUtils.getCurrentQuizLanguageId(context),
          type: UiUtils.getCategoryTypeNumberFromQuizType(widget.quizType),
          subType: UiUtils.subTypeFromQuizType(widget.quizType),
        );
  }

  bool isCategoryEnabled() {
    return widget.quizType == QuizTypes.oneVsOneBattle
        ? context.read<SystemConfigCubit>().isCategoryEnabledForOneVsOneBattle
        : context.read<SystemConfigCubit>().isCategoryEnabledForGroupBattle;
  }

  RoomCodeCharType get roomCodeCharType =>
      widget.quizType == QuizTypes.oneVsOneBattle
          ? context.read<SystemConfigCubit>().oneVsOneBattleRoomCodeCharType
          : context.read<SystemConfigCubit>().groupBattleRoomCodeCharType;

  void _continueAfterRewardAd() {
    // لا نعطي عملات، فقط نكمل العملية
    // يمكن إضافة أي منطق آخر هنا إذا لزم الأمر
  }

  Widget _buildDropDown({
    required List<Map<String, String?>> values,
    required String keyValue,
  }) {
    selectedCategory = values.map((e) => e['name']).toList().first!;
    selectedCategoryId = values.map((e) => e['id']).toList().first!;
    selectedCategoryImage = values.map((e) => e['image']).toList().first!;

    return StatefulBuilder(
      builder: (context, setState) {
        final colorScheme = Theme.of(context).colorScheme;

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: colorScheme.surface,
          ),
          margin: EdgeInsets.symmetric(
            horizontal: MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: DropdownButton<String>(
            key: Key(keyValue),
            underline: const SizedBox(),
            borderRadius: BorderRadius.circular(8),
            dropdownColor: colorScheme.surface,
            style: TextStyle(
              color: colorScheme.onTertiary,
              fontSize: 16,
              fontWeight: FontWeights.regular,
            ),
            isExpanded: true,
            alignment: Alignment.center,
            icon: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: colorScheme.onTertiary.withOpacity(0.4),
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.keyboard_arrow_down_rounded,
                color: colorScheme.onTertiary,
              ),
            ),
            value: selectedCategory,
            hint: Text(
              context.tr(selectCategoryKey)!,
              style: TextStyle(
                color: colorScheme.onTertiary.withOpacity(0.4),
                fontSize: 16,
                fontWeight: FontWeights.regular,
              ),
            ),
            onChanged: (value) {
              setState(() {
                selectedCategory = value!;

                // set id for selected category
                for (final v in values) {
                  if (v['name'] == selectedCategory) {
                    selectedCategoryId = v['id']!;
                    selectedCategoryImage = v['image']!;
                  }
                }
              });
            },
            items: values.map((e) => e['name']).toList().map((name) {
              return DropdownMenuItem(
                value: name,
                child: name == selectCategoryKey
                    ? Text(context.tr(selectCategoryKey)!)
                    : Text(name!),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  // Widget _buildQuestionsNumberDropDown({
  //   required List<String> values,
  //   required String keyValue,
  // }) {
  //   selectedQuestionsNumber = values.first;

  //   return StatefulBuilder(
  //     builder: (context, setState) {
  //       final colorScheme = Theme.of(context).colorScheme;

  //       return Container(
  //         decoration: BoxDecoration(
  //           borderRadius: BorderRadius.circular(8),
  //           color: colorScheme.surface,
  //         ),
  //         margin: EdgeInsets.symmetric(
  //           horizontal: MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
  //         ),
  //         padding: const EdgeInsets.symmetric(horizontal: 20),
  //         child: DropdownButton<String>(
  //           key: Key(keyValue),
  //           underline: const SizedBox(),
  //           borderRadius: BorderRadius.circular(8),
  //           dropdownColor: colorScheme.surface,
  //           style: TextStyle(
  //             color: colorScheme.onTertiary,
  //             fontSize: 16,
  //             fontWeight: FontWeights.regular,
  //           ),
  //           isExpanded: true,
  //           alignment: Alignment.center,
  //           icon: Container(
  //             decoration: BoxDecoration(
  //               border: Border.all(
  //                 color: colorScheme.onTertiary.withOpacity(0.4),
  //               ),
  //               borderRadius: BorderRadius.circular(6),
  //             ),
  //             child: Icon(
  //               Icons.keyboard_arrow_down_rounded,
  //               color: colorScheme.onTertiary,
  //             ),
  //           ),
  //           value: selectedQuestionsNumber,
  //           hint: Text(
  //             context.tr(selectQuestionsNumberKey) ?? 'اختر عدد الاسئلة',
  //             style: TextStyle(
  //               color: colorScheme.onTertiary.withOpacity(0.4),
  //               fontSize: 16,
  //               fontWeight: FontWeights.regular,
  //             ),
  //           ),
  //           onChanged: (value) {
  //             setState(() {
  //               selectedQuestionsNumber = value!;
  //             });
  //           },
  //           items: values.map((e) {
  //             return DropdownMenuItem(
  //               value: e,
  //               child: e == selectQuestionsNumberKey
  //                   ? Text(context.tr(selectQuestionsNumberKey)!)
  //                   : Text(e),
  //             );
  //           }).toList(),
  //         ),
  //       );
  //     },
  //   );
  // }

  ///
  void showCreateRoomBottomSheet() {
    customEntryFee = TextEditingController(text: '');
    entryFee = entryFees.first;
    context.tr(
      widget.quizType == QuizTypes.oneVsOneBattle ? 'randomLbl' : 'groupPlay',
    );

    context
        .read<MultiUserBattleRoomCubit>()
        .updateState(MultiUserBattleRoomInitial(), cancelSubscription: true);
    context
        .read<BattleRoomCubit>()
        .updateState(BattleRoomInitial(), cancelSubscription: true);

    showModalBottomSheet<void>(
      isDismissible: true,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      backgroundColor: Colors.transparent,
      context: context,
      enableDrag: true,
      builder: (_) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white,
                    Colors.white.withOpacity(0.95),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(30)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              height: scrHeight * 0.75,
              margin: MediaQuery.of(context).viewInsets,
              padding: const EdgeInsets.only(top: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // مؤشر السحب
                  Center(
                    child: Container(
                      width: 50,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),

                  /// Create Room title
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.add_circle_outline,
                            color: Theme.of(context).primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 10),
                          Text(
                            widget.quizType == QuizTypes.oneVsOneBattle
                                ? "إنشاء تحدي فردي"
                                : "إنشاء تحدي جماعي",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  /// select category text
                  if (isCategoryEnabled()) ...[
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: MediaQuery.of(context).size.width * 0.1,
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 10),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.category_outlined,
                              color: Theme.of(context).primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 10),
                            Text(
                              "اختر الفئة",
                              style: TextStyle(
                                fontSize: 16,
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),

                    /// Select Category Drop Down
                    BlocConsumer<QuizCategoryCubit, QuizCategoryState>(
                      bloc: context.read<QuizCategoryCubit>(),
                      listener: (_, state) {
                        if (state is QuizCategorySuccess) {
                          selectedCategory =
                              state.categories.first.categoryName!;
                          selectedCategoryId = state.categories.first.id!;
                        }

                        if (state is QuizCategoryFailure) {
                          if (state.errorMessage ==
                              errorCodeUnauthorizedAccess) {
                            showAlreadyLoggedInDialog(context);
                            return;
                          }

                          showDialog<bool>(
                            context: context,
                            builder: (context) => AlertDialog(
                              shadowColor: Colors.transparent,
                              actions: [
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(true),
                                  child: Text(
                                    context.tr(retryLbl)!,
                                    style: TextStyle(
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                              content: Text(
                                context.tr(
                                  convertErrorCodeToLanguageKey(
                                    state.errorMessage,
                                  ),
                                )!,
                              ),
                            ),
                          ).then((value) {
                            if (value != null && value) {
                              _getCategories();
                            }
                          });
                        }
                      },
                      builder: (_, state) {
                        return AnimatedSwitcher(
                          duration: const Duration(milliseconds: 500),
                          child: state is QuizCategorySuccess
                              ? Column(
                                  children: [
                                    _buildDropDown(
                                      values: state.categories
                                          .where((c) => !c.isPremium)
                                          .map(
                                            (e) => {
                                              'name': e.categoryName,
                                              'image': e.image,
                                              'id': e.id,
                                            },
                                          )
                                          .toList(),
                                      keyValue: 'selectCategorySuccess',
                                    ),
                                    // const SizedBox(height: 20),
                                    // _buildQuestionsNumberDropDown(
                                    //   values: questionsNumbersList,
                                    //   keyValue: selectedQuestionsNumber,
                                    // ),
                                  ],
                                )
                              : Opacity(
                                  opacity: 0.65,
                                  child: Column(
                                    children: [
                                      _buildDropDown(
                                        values: [
                                          {
                                            'name': selectCategoryKey,
                                            'id': '0',
                                            'image': '',
                                          },
                                        ],
                                        keyValue: selectCategoryKey,
                                      ),
                                      // _buildQuestionsNumberDropDown(
                                      //   values: questionsNumbersList,
                                      //   keyValue: selectedQuestionsNumber,
                                      // ),
                                    ],
                                  ),
                                ),
                        );
                      },
                    ),

                    const SizedBox(height: 20),
                  ],

                  // خيار المحادثة الصوتية
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.1,
                    ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 15, vertical: 10),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.mic,
                                color: Theme.of(context).primaryColor,
                                size: 20,
                              ),
                              const SizedBox(width: 10),
                              Text(
                                "المحادثة الصوتية",
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const Spacer(),
                              Switch(
                                value: enableVoiceChat,
                                activeColor: Colors.white,
                                activeTrackColor: Colors.green,
                                inactiveThumbColor: Colors.white,
                                inactiveTrackColor: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.3),
                                onChanged: (value) {
                                  setState(() {
                                    enableVoiceChat = value;
                                  });
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                          Padding(
                            padding: const EdgeInsets.only(right: 30),
                            child: Text(
                              "تمكين المحادثة الصوتية أثناء التحدي",
                              style: TextStyle(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.7),
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),

                  ///
                  // Padding(
                  //   padding: EdgeInsets.symmetric(
                  //     horizontal: MediaQuery.of(context).size.width *
                  //         UiUtils.hzMarginPct,
                  //   ),
                  //   child: Text(
                  //     context.tr('entryCoinsForBattle')!,
                  //     style: TextStyle(
                  //       fontSize: 16,
                  //       color: colorScheme.onTertiary,
                  //     ),
                  //   ),
                  // ),
                  // const SizedBox(height: 13),
                  // Padding(
                  //   padding: EdgeInsets.symmetric(
                  //     horizontal: MediaQuery.of(context).size.width *
                  //         UiUtils.hzMarginPct,
                  //   ),
                  //   child: StatefulBuilder(
                  //     builder: (context, state) {
                  //       return Row(
                  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //         mainAxisSize: MainAxisSize.min,
                  //         children: entryFees
                  //             .map((e) => _coinCard(e, state))
                  //             .toList(),
                  //       );
                  //     },
                  //   ),
                  // ),
                  // const SizedBox(height: 14),
                  // Container(
                  //   margin: EdgeInsets.symmetric(
                  //     horizontal: MediaQuery.of(context).size.width *
                  //         UiUtils.hzMarginPct,
                  //   ),
                  //   padding: const EdgeInsets.symmetric(horizontal: 20),
                  //   decoration: BoxDecoration(
                  //     color: colorScheme.surface,
                  //     borderRadius: BorderRadius.circular(8),
                  //   ),
                  //   child: TextField(
                  //     controller: customEntryFee,
                  //     keyboardType: TextInputType.number,
                  //     style: TextStyle(
                  //       color: colorScheme.onTertiary,
                  //       fontWeight: FontWeights.regular,
                  //       fontSize: 16,
                  //     ),
                  //     decoration: InputDecoration(
                  //       border: InputBorder.none,
                  //       hintText: context.tr('plsEnterTheCoins'),
                  //       hintStyle: TextStyle(
                  //         color: colorScheme.onTertiary.withOpacity(.4),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // const SizedBox(height: 35),
                  // Padding(
                  //   padding: EdgeInsets.symmetric(
                  //     horizontal: MediaQuery.of(context).size.width *
                  //         UiUtils.hzMarginPct,
                  //   ),
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       Column(
                  //         crossAxisAlignment: CrossAxisAlignment.start,
                  //         children: [
                  //           Text(
                  //             context.tr('yourCoins')!,
                  //             style: TextStyle(
                  //               color: colorScheme.onTertiary.withOpacity(0.6),
                  //               fontWeight: FontWeights.regular,
                  //               fontSize: 16,
                  //             ),
                  //           ),
                  //           Text(
                  //             '${context.watch<UserDetailsCubit>().getCoins()} ${context.tr(coinsLbl)}',
                  //             style: TextStyle(
                  //               color: colorScheme.onTertiary,
                  //               fontWeight: FontWeights.bold,
                  //               fontSize: 18,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //       if (isInAppPurchaseEnabled)
                  //         if (widget.quizType == QuizTypes.oneVsOneBattle)
                  //           BlocBuilder<BattleRoomCubit, BattleRoomState>(
                  //             builder: (context, state) {
                  //               return TextButton(
                  //                 onPressed: () {
                  //                   if (state is BattleRoomCreating) return;
                  //
                  //                   // Navigator.of(context).pushNamed(
                  //                   //   Routes.coinStore,
                  //                   //   arguments: {
                  //                   //     'isGuest': false,
                  //                   //     'iapProducts': iapProducts,
                  //                   //   },
                  //                   // );
                  //                 },
                  //                 style: TextButton.styleFrom(
                  //                   backgroundColor: colorScheme.surface,
                  //                   padding: const EdgeInsets.all(10),
                  //                   shape: RoundedRectangleBorder(
                  //                     borderRadius: BorderRadius.circular(8),
                  //                   ),
                  //                 ),
                  //                 child: Text(
                  //                   context.tr('buyCoins')!,
                  //                   style: TextStyle(
                  //                     color: state is BattleRoomCreating
                  //                         ? Theme.of(context).disabledColor
                  //                         : Theme.of(context).primaryColor,
                  //                     fontSize: 14,
                  //                     height: 1.2,
                  //                     fontWeight: FontWeights.medium,
                  //                   ),
                  //                 ),
                  //               );
                  //             },
                  //           )
                  //         else
                  //           BlocBuilder<MultiUserBattleRoomCubit,
                  //               MultiUserBattleRoomState>(
                  //             builder: (context, state) {
                  //               return TextButton(
                  //                 onPressed: () {
                  //                   if (state
                  //                       is MultiUserBattleRoomInProgress) {
                  //                     return;
                  //                   }
                  //
                  //                   // Navigator.of(context).pushNamed(
                  //                   //   Routes.coinStore,
                  //                   //   arguments: {
                  //                   //     'isGuest': false,
                  //                   //     'iapProducts': iapProducts,
                  //                   //   },
                  //                   // );
                  //                 },
                  //                 style: TextButton.styleFrom(
                  //                   backgroundColor: colorScheme.surface,
                  //                   padding: const EdgeInsets.all(10),
                  //                   shape: RoundedRectangleBorder(
                  //                     borderRadius: BorderRadius.circular(8),
                  //                   ),
                  //                 ),
                  //                 child: Text(
                  //                   context.tr('buyCoins')!,
                  //                   style: TextStyle(
                  //                     color:
                  //                         state is MultiUserBattleRoomInProgress
                  //                             ? Theme.of(context).disabledColor
                  //                             : Theme.of(context).primaryColor,
                  //                     fontSize: 14,
                  //                     height: 1.2,
                  //                     fontWeight: FontWeights.medium,
                  //                   ),
                  //                 ),
                  //               );
                  //             },
                  //           )
                  //       else
                  //         const SizedBox(),
                  //     ],
                  //   ),
                  // ),
                  const SizedBox(height: 25),
                  if (widget.quizType == QuizTypes.oneVsOneBattle)
                    BlocConsumer<BattleRoomCubit, BattleRoomState>(
                      bloc: context.read<BattleRoomCubit>(),
                      listener: (context, state) {
                        if (state is BattleRoomFailure) {
                          if (state.errorMessageCode ==
                              errorCodeUnauthorizedAccess) {
                            showAlreadyLoggedInDialog(context);
                            return;
                          }
                          UiUtils.errorMessageDialog(
                            context,
                            context.tr(
                              convertErrorCodeToLanguageKey(
                                state.errorMessageCode,
                              ),
                            ),
                          );
                        } else if (state is BattleRoomCreated) {
                          //wait for others
                          Navigator.of(context).pop();
                          inviteToRoomBottomSheet();
                        }
                      },
                      builder: (context, state) {
                        return Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: MediaQuery.of(context).size.width * 0.1,
                          ),
                          child: state is BattleRoomCreating
                              ? Container(
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                    ),
                                  ),
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: CustomRoundedButton(
                                    widthPercentage:
                                        MediaQuery.of(context).size.width,
                                    backgroundColor:
                                        Theme.of(context).primaryColor,
                                    radius: 20,
                                    showBorder: false,
                                    height: 60,
                                    onTap: () {
                                      if (state is BattleRoomCreating) {
                                        return;
                                      }

                                      final userProfile = context
                                          .read<UserDetailsCubit>()
                                          .getUserProfile();

                                      /// Create Room
                                      context
                                          .read<BattleRoomCubit>()
                                          .createRoom(
                                            categoryId: selectedCategoryId,
                                            categoryName: selectedCategory,
                                            categoryImage:
                                                selectedCategoryImage,
                                            entryFee: 0,
                                            name: userProfile.name,
                                            profileUrl: userProfile.profileUrl,
                                            uid: userProfile.userId,
                                            questionLanguageId: UiUtils
                                                .getCurrentQuizLanguageId(
                                              context,
                                            ),
                                            charType: context
                                                .read<SystemConfigCubit>()
                                                .oneVsOneBattleRoomCodeCharType,
                                            enableVoiceChat: enableVoiceChat,
                                          );
                                    },
                                    buttonTitle: "إنشاء الغرفة",
                                    titleColor: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        );
                      },
                    )
                  else
                    BlocConsumer<MultiUserBattleRoomCubit,
                        MultiUserBattleRoomState>(
                      bloc: context.read<MultiUserBattleRoomCubit>(),
                      listener: (_, state) {
                        if (state is MultiUserBattleRoomFailure) {
                          if (state.errorMessageCode ==
                              errorCodeUnauthorizedAccess) {
                            showAlreadyLoggedInDialog(context);
                            return;
                          }
                          UiUtils.errorMessageDialog(
                            context,
                            context.tr(
                              convertErrorCodeToLanguageKey(
                                state.errorMessageCode,
                              ),
                            ),
                          );
                        } else if (state is MultiUserBattleRoomSuccess) {
                          //wait for others
                          Navigator.of(context).pop();
                          inviteToRoomBottomSheet();
                        }
                      },
                      builder: (context, state) {
                        return Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: MediaQuery.of(context).size.width * 0.1,
                          ),
                          child: state is MultiUserBattleRoomInProgress
                              ? Container(
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                    ),
                                  ),
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: CustomRoundedButton(
                                    widthPercentage:
                                        MediaQuery.of(context).size.width,
                                    backgroundColor:
                                        Theme.of(context).primaryColor,
                                    radius: 20,
                                    showBorder: false,
                                    height: 60,
                                    onTap: () {
                                      if (state
                                          is MultiUserBattleRoomInProgress) {
                                        return;
                                      }

                                      final userProfile = context
                                          .read<UserDetailsCubit>()
                                          .getUserProfile();

                                      /// Create Room
                                      context
                                          .read<MultiUserBattleRoomCubit>()
                                          .createRoom(
                                            categoryId: selectedCategoryId,
                                            categoryName: selectedCategory,
                                            categoryImage:
                                                selectedCategoryImage,
                                            charType: context
                                                .read<SystemConfigCubit>()
                                                .groupBattleRoomCodeCharType,
                                            entryFee: 0,
                                            name: userProfile.name,
                                            profileUrl: userProfile.profileUrl,
                                            roomType: 'public',
                                            uid: userProfile.userId,
                                            questionLanguageId: UiUtils
                                                .getCurrentQuizLanguageId(
                                              context,
                                            ),
                                            enableVoiceChat: enableVoiceChat,
                                          );
                                    },
                                    buttonTitle: "إنشاء تحدي جماعي",
                                    titleColor: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        );
                      },
                    ),
                  const SizedBox(height: 19),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void showRoomDestroyed(BuildContext context) {
    showDialog<void>(
      barrierDismissible: false,
      context: context,
      builder: (_) => PopScope(
        canPop: false,
        child: AlertDialog(
          shadowColor: Colors.transparent,
          content: Text(
            context.tr('roomDeletedOwnerLbl')!,
            style: TextStyle(color: Theme.of(context).primaryColor),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: Text(
                context.tr('okayLbl')!,
                style: TextStyle(color: Theme.of(context).primaryColor),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _shareRoomCode({
    required BuildContext context,
    required String roomCode,
    required String categoryName,
    required int entryFee,
  }) {
    try {
      final msgIntro = context.tr('battleInviteMessageIntro');
      final msgJoin = context.tr('battleInviteMessageJoin');
      final msgEnd = context.tr('battleInviteMessageEnd');

      // final inviteMessage = '$msgIntro $appName, $msgJoin $roomCode \n'
      //     '${context.tr('categoryLbl')} : $categoryName, '
      //     '${context.tr('just')} $entryFee ${context.tr('coinsLbl')} $msgEnd';

      final inviteMessage = '$msgIntro $appName, $msgJoin $roomCode \n'
          '${context.tr('categoryLbl')} : $categoryName $msgEnd';

      UiUtils.share(inviteMessage, context: context);
    } catch (e) {
      UiUtils.showSnackBar(context.tr(defaultErrorMessageKey)!, context);
    }
  }

  void inviteToRoomBottomSheet() {
    showModalBottomSheet<void>(
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      backgroundColor: Colors.transparent,
      context: context,
      builder: (_) {
        if (widget.quizType == QuizTypes.oneVsOneBattle) {
          var shareText = context.tr('shareRoomCodeRndLbl')!;

          return BlocConsumer<BattleRoomCubit, BattleRoomState>(
            listener: (context, state) {
              if (state is BattleRoomUserFound) {
                //if game is ready to play
                if (state.battleRoom.readyToPlay!) {
                  //if user has joined room then navigate to quiz screen
                  if (state.battleRoom.user1!.uid !=
                      context
                          .read<UserDetailsCubit>()
                          .getUserProfile()
                          .userId) {
                    Navigator.of(context).pushReplacementNamed(
                      Routes.battleRoomQuiz,
                      arguments: {
                        'quiz_type': QuizTypes.oneVsOneBattle,
                        'play_with_bot': false,
                      },
                    );
                  }
                }

                //if owner deleted the room then show this dialog
                if (!state.isRoomExist) {
                  if (context
                          .read<UserDetailsCubit>()
                          .getUserProfile()
                          .userId !=
                      state.battleRoom.user1!.uid) {
                    //Room destroyed by owner
                    showRoomDestroyed(context);
                  }
                }
              }
            },
            builder: (context, battleState) {
              var showShareIcon = true;
              if (battleState is BattleRoomUserFound) {
                shareText = battleState.battleRoom.user2!.uid ==
                        context.read<UserDetailsCubit>().userId()
                    ? context.tr('waitGameWillStartLbl')!
                    : shareText;
                showShareIcon = battleState.battleRoom.user2!.uid !=
                    context.read<UserDetailsCubit>().userId();
              }

              return StatefulBuilder(
                builder: (context, state) {
                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.white,
                          Colors.white.withOpacity(0.95),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(30)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    height: scrHeight * 0.85,
                    padding: const EdgeInsets.only(top: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: scrWidth * UiUtils.hzMarginPct,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              PopScope(
                                canPop: battleState is! BattleRoomCreated &&
                                    battleState is! BattleRoomUserFound,
                                onPopInvoked: (didPop) {
                                  if (didPop) return;

                                  showExitOrDeleteRoomDialog();
                                },
                                child: InkWell(
                                  onTap: showExitOrDeleteRoomDialog,
                                  child: Icon(
                                    Icons.arrow_back_rounded,
                                    size: 24,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onTertiary,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                decoration: BoxDecoration(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.sports_esports,
                                      color: Theme.of(context).primaryColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      "غرفة التحدي",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (showShareIcon)
                                Builder(
                                  builder: (context) {
                                    return InkWell(
                                      onTap: () => _shareRoomCode(
                                        context: context,
                                        roomCode: context
                                            .read<BattleRoomCubit>()
                                            .getRoomCode(),
                                        categoryName: context
                                            .read<BattleRoomCubit>()
                                            .categoryName,
                                        entryFee: context
                                            .read<BattleRoomCubit>()
                                            .getEntryFee(),
                                      ),
                                      child: Icon(
                                        Icons.share,
                                        size: 20,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onTertiary,
                                      ),
                                    );
                                  },
                                )
                              else
                                const SizedBox(),
                            ],
                          ),
                        ),
                        const Divider(),
                        const SizedBox(height: 10),

                        /// Invite Code
                        BattleInviteCard(
                          categoryImage:
                              context.read<BattleRoomCubit>().categoryImage,
                          categoryName:
                              context.read<BattleRoomCubit>().categoryName,
                          entryFee:
                              context.read<BattleRoomCubit>().getEntryFee(),
                          roomCode:
                              context.read<BattleRoomCubit>().getRoomCode(),
                          shareText: shareText,
                        ),
                        const SizedBox(height: 20),
                        Expanded(
                          child: BlocBuilder<BattleRoomCubit, BattleRoomState>(
                            builder: (_, state) {
                              if (state is BattleRoomCreated) {
                                return GridView.count(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: scrWidth * UiUtils.hzMarginPct,
                                  ),
                                  crossAxisCount: 2,
                                  mainAxisSpacing: 20,
                                  crossAxisSpacing: 20,
                                  children: [
                                    inviteRoomUserCard(
                                      state.battleRoom.user1!.name,
                                      state.battleRoom.user1!.profileUrl,
                                      isCreator: true,
                                    ),
                                    inviteRoomUserCard(
                                      state.battleRoom.user2!.name.isEmpty
                                          ? context.tr('player2')!
                                          : state.battleRoom.user2!.name,
                                      state.battleRoom.user2!.profileUrl,
                                      isCreator: false,
                                    ),
                                  ],
                                );
                              }
                              if (state is BattleRoomUserFound) {
                                return GridView.count(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: scrWidth * UiUtils.hzMarginPct,
                                  ),
                                  crossAxisCount: 2,
                                  mainAxisSpacing: 20,
                                  crossAxisSpacing: 20,
                                  children: [
                                    inviteRoomUserCard(
                                      state.battleRoom.user1!.name,
                                      state.battleRoom.user1!.profileUrl,
                                      isCreator: true,
                                    ),
                                    inviteRoomUserCard(
                                      state.battleRoom.user2!.name.isEmpty
                                          ? context.tr('player2')!
                                          : state.battleRoom.user2!.name,
                                      state.battleRoom.user2!.profileUrl,
                                      isCreator: false,
                                    ),
                                  ],
                                );
                              }
                              return const SizedBox();
                            },
                          ),
                        ),

                        BlocBuilder<BattleRoomCubit, BattleRoomState>(
                          bloc: context.read<BattleRoomCubit>(),
                          builder: (context, state) {
                            if (state is BattleRoomCreated) {
                              return Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal:
                                        scrWidth * UiUtils.hzMarginPct + 10,
                                  ),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          blurRadius: 10,
                                          offset: const Offset(0, 5),
                                        ),
                                      ],
                                    ),
                                    child: TextButton(
                                      style: TextButton.styleFrom(
                                        backgroundColor:
                                            Theme.of(context).primaryColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 15),
                                      ),
                                      onPressed: () async {
                                        //need minimum 2 player to start the game
                                        //mark as ready to play in database
                                        if (state
                                            .battleRoom.user2!.uid.isEmpty) {
                                          UiUtils.errorMessageDialog(
                                            context,
                                            context.tr(
                                              convertErrorCodeToLanguageKey(
                                                errorCodeCanNotStartGame,
                                              ),
                                            ),
                                          );
                                        } else {
                                          context
                                              .read<BattleRoomCubit>()
                                              .startGame();
                                          await Future<void>.delayed(
                                            const Duration(milliseconds: 500),
                                          );
                                          //navigate to quiz screen
                                          await Navigator.of(context)
                                              .pushReplacementNamed(
                                            Routes.battleRoomQuiz,
                                            arguments: {
                                              'quiz_type':
                                                  QuizTypes.oneVsOneBattle,
                                              'play_with_bot': false,
                                            },
                                          );
                                        }
                                      },
                                      child: Text(
                                        context.tr('startLbl')!,
                                        style: TextStyle(
                                          fontSize: 18,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .surface,
                                        ),
                                      ),
                                    ),
                                  ));
                            }
                            if (state is BattleRoomUserFound) {
                              if (state.battleRoom.user1!.uid !=
                                  context
                                      .read<UserDetailsCubit>()
                                      .getUserProfile()
                                      .userId) {
                                return Container();
                              }

                              return Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal:
                                      scrWidth * UiUtils.hzMarginPct + 10,
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    style: TextButton.styleFrom(
                                      backgroundColor:
                                          Theme.of(context).primaryColor,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 15),
                                    ),
                                    onPressed: () async {
                                      //need minimum 2 player to start the game
                                      //mark as ready to play in database
                                      if (state.battleRoom.user2!.uid.isEmpty) {
                                        UiUtils.errorMessageDialog(
                                          context,
                                          context.tr(
                                            convertErrorCodeToLanguageKey(
                                              errorCodeCanNotStartGame,
                                            ),
                                          ),
                                        );
                                      } else {
                                        context
                                            .read<BattleRoomCubit>()
                                            .startGame();
                                        await Future<void>.delayed(
                                          const Duration(milliseconds: 500),
                                        );
                                        //navigate to quiz screen
                                        await Navigator.of(context)
                                            .pushReplacementNamed(
                                          Routes.battleRoomQuiz,
                                          arguments: {
                                            'quiz_type':
                                                QuizTypes.oneVsOneBattle,
                                            'play_with_bot': false,
                                          },
                                        );
                                      }
                                    },
                                    child: Text(
                                      "ابدأ اللعب",
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }
                            return const SizedBox();
                          },
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  );
                },
              );
            },
          );
        } else {
          return BlocConsumer<MultiUserBattleRoomCubit,
              MultiUserBattleRoomState>(
            listener: (context, state) {
              if (state is MultiUserBattleRoomSuccess) {
                //if game is ready to play
                if (state.battleRoom.readyToPlay!) {
                  //if user has joined room then navigate to quiz screen
                  if (state.battleRoom.user1!.uid !=
                      context
                          .read<UserDetailsCubit>()
                          .getUserProfile()
                          .userId) {
                    Navigator.of(context)
                        .pushReplacementNamed(Routes.multiUserBattleRoomQuiz);
                  }
                }

                //if owner deleted the room then show this dialog
                if (!state.isRoomExist) {
                  if (context
                          .read<UserDetailsCubit>()
                          .getUserProfile()
                          .userId !=
                      state.battleRoom.user1!.uid) {
                    //Room destroyed by owner
                    showRoomDestroyed(context);
                  }
                }
              }
            },
            builder: (context, battleState) {
              return StatefulBuilder(
                builder: (context, state) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: UiUtils.bottomSheetTopRadius,
                    ),
                    height: scrHeight * 0.85,
                    padding: const EdgeInsets.only(top: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: scrWidth * UiUtils.hzMarginPct,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              PopScope(
                                canPop:
                                    battleState is! MultiUserBattleRoomSuccess,
                                onPopInvoked: (didPop) {
                                  if (didPop) return;

                                  showExitOrDeleteRoomDialog();
                                },
                                child: InkWell(
                                  onTap: showExitOrDeleteRoomDialog,
                                  child: Icon(
                                    Icons.arrow_back_rounded,
                                    size: 24,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onTertiary,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                decoration: BoxDecoration(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.sports_esports,
                                      color: Theme.of(context).primaryColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      "غرفة التحدي",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Builder(
                                builder: (context) {
                                  return InkWell(
                                    onTap: () => _shareRoomCode(
                                      context: context,
                                      roomCode: context
                                          .read<MultiUserBattleRoomCubit>()
                                          .getRoomCode(),
                                      categoryName: context
                                          .read<MultiUserBattleRoomCubit>()
                                          .categoryName,
                                      entryFee: context
                                          .read<MultiUserBattleRoomCubit>()
                                          .getEntryFee(),
                                    ),
                                    child: Icon(
                                      Icons.share,
                                      size: 20,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onTertiary,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const Divider(),
                        const SizedBox(height: 15),

                        /// Invite Code
                        BattleInviteCard(
                          categoryImage: context
                              .read<MultiUserBattleRoomCubit>()
                              .categoryImage,
                          categoryName: context
                              .read<MultiUserBattleRoomCubit>()
                              .categoryName,
                          entryFee: context
                              .read<MultiUserBattleRoomCubit>()
                              .getEntryFee(),
                          roomCode: context
                              .read<MultiUserBattleRoomCubit>()
                              .getRoomCode(),
                          shareText: context.tr('shareRoomCodeLbl')!,
                        ),
                        const SizedBox(height: 20),
                        Expanded(
                          child: BlocBuilder<MultiUserBattleRoomCubit,
                              MultiUserBattleRoomState>(
                            builder: (_, state) {
                              if (state is MultiUserBattleRoomSuccess) {
                                return GridView.count(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: scrWidth * UiUtils.hzMarginPct,
                                  ),
                                  crossAxisCount: 2,
                                  mainAxisSpacing: 20,
                                  crossAxisSpacing: 20,
                                  children: [
                                    inviteRoomUserCard(
                                      state.battleRoom.user1!.name,
                                      state.battleRoom.user1!.profileUrl,
                                      isCreator: true,
                                    ),
                                    inviteRoomUserCard(
                                      state.battleRoom.user2!.name.isEmpty
                                          ? context.tr('player2')!
                                          : state.battleRoom.user2!.name,
                                      state.battleRoom.user2!.profileUrl,
                                      isCreator: false,
                                    ),
                                    inviteRoomUserCard(
                                      state.battleRoom.user3!.name.isEmpty
                                          ? context.tr('player3')!
                                          : state.battleRoom.user3!.name,
                                      state.battleRoom.user3!.profileUrl,
                                      isCreator: false,
                                    ),
                                    inviteRoomUserCard(
                                      state.battleRoom.user4!.name.isEmpty
                                          ? context.tr('player4')!
                                          : state.battleRoom.user4!.name,
                                      state.battleRoom.user4!.profileUrl,
                                      isCreator: false,
                                    ),
                                  ],
                                );
                              }
                              return const SizedBox();
                            },
                          ),
                        ),
                        const SizedBox(height: 20),

                        /// Start
                        BlocBuilder<MultiUserBattleRoomCubit,
                            MultiUserBattleRoomState>(
                          builder: (context, state) {
                            if (state is MultiUserBattleRoomSuccess) {
                              if (state.battleRoom.user1!.uid !=
                                  context
                                      .read<UserDetailsCubit>()
                                      .getUserProfile()
                                      .userId) {
                                return Container();
                              }
                              return Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal:
                                      scrWidth * UiUtils.hzMarginPct + 10,
                                ),
                                child: TextButton(
                                  onPressed: () {
                                    //need minimum 2 player to start the game
                                    //mark as ready to play in database
                                    if (state.battleRoom.user2!.uid.isEmpty) {
                                      UiUtils.errorMessageDialog(
                                        context,
                                        context.tr(
                                          convertErrorCodeToLanguageKey(
                                            errorCodeCanNotStartGame,
                                          ),
                                        ),
                                      );
                                    } else {
                                      //start quiz
                                      /*    widget.quizType==QuizTypes.battle?context.read<BattleRoomCubit>().startGame():*/ context
                                          .read<MultiUserBattleRoomCubit>()
                                          .startGame();
                                      //navigate to quiz screen
                                      widget.quizType ==
                                              QuizTypes.oneVsOneBattle
                                          ? Navigator.of(context)
                                              .pushReplacementNamed(
                                              Routes.battleRoomQuiz,
                                              arguments: {
                                                'quiz_type':
                                                    QuizTypes.oneVsOneBattle,
                                                'play_with_bot': false,
                                              },
                                            )
                                          : Navigator.of(context)
                                              .pushReplacementNamed(
                                              Routes.multiUserBattleRoomQuiz,
                                            );
                                    }
                                  },
                                  style: TextButton.styleFrom(
                                    backgroundColor:
                                        Theme.of(context).primaryColor,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: Text(
                                    context.tr('startLbl')!,
                                    style: TextStyle(
                                      fontSize: 18,
                                      color:
                                          Theme.of(context).colorScheme.surface,
                                    ),
                                  ),
                                ),
                              );
                            }
                            return const SizedBox();
                          },
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  );
                },
              );
            },
          );
        }
      },
    );
  }

  /// Delete room if user is creator, otherwise remove user from the room.
  Future<void> showExitOrDeleteRoomDialog() {
    void onTapYes() {
      final userId = context.read<UserDetailsCubit>().getUserProfile().userId;

      if (widget.quizType == QuizTypes.oneVsOneBattle) {
        var isCreator = false;
        final battleCubit = context.read<BattleRoomCubit>();

        if (battleCubit.state is BattleRoomUserFound) {
          isCreator = (battleCubit.state as BattleRoomUserFound)
                  .battleRoom
                  .user1!
                  .uid ==
              userId;
        } else {
          isCreator =
              (battleCubit.state as BattleRoomCreated).battleRoom.user1!.uid ==
                  userId;
        }

        if (isCreator) {
          battleCubit.deleteBattleRoom(isGroupBattle: false);
        } else {
          battleCubit.deleteUserFromRoom(userId!);
        }
      } else {
        final multiUserCubit = context.read<MultiUserBattleRoomCubit>();

        final isCreator = (multiUserCubit.state as MultiUserBattleRoomSuccess)
                .battleRoom
                .user1!
                .uid ==
            userId;

        if (isCreator) {
          multiUserCubit.deleteMultiUserBattleRoom();
        } else {
          multiUserCubit.deleteUserFromRoom(userId!);
        }
      }

      Navigator.of(context).pop();
      Navigator.of(context).pop();
    }

    final textStyle = GoogleFonts.ibmPlexSansArabic(
      textStyle: TextStyle(
        color: Theme.of(context).colorScheme.onTertiary,
      ),
    );

    return showDialog<void>(
      context: context,
      builder: (context) {
        return AlertDialog(
          titleTextStyle: textStyle,
          contentTextStyle: textStyle,
          content: Text(context.tr('roomDelete')!),
          actions: [
            CupertinoButton(
              onPressed: onTapYes,
              child: Text(
                context.tr('yesBtn')!,
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            CupertinoButton(
              onPressed: Navigator.of(context).pop,
              child: Text(
                context.tr('noBtn')!,
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget inviteRoomUserCard(
    String userName,
    String img, {
    required bool isCreator,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: colorScheme.surface,
      ),
      padding: const EdgeInsets.all(15),
      child: Column(
        children: [
          DecoratedBox(
            decoration: const BoxDecoration(shape: BoxShape.circle),
            child: QImage.circular(
              imageUrl: img.isNotEmpty ? img : Assets.friendImg,
              width: 50,
              height: 50,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            userName,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeights.regular,
              color: colorScheme.onTertiary,
            ),
          ),
          const SizedBox(height: 9),
          if (isCreator)
            Text(
              context.tr('creator')!,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeights.regular,
                color: colorScheme.onTertiary.withOpacity(0.4),
              ),
            )
          else
            Text(
              context.tr('addPlayer')!,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeights.regular,
                color:
                    Theme.of(context).colorScheme.onTertiary.withOpacity(0.3),
              ),
            ),
        ],
      ),
    );
  }

  void showAdDialog() {
    if (context.read<RewardedAdCubit>().state is! RewardedAdLoaded) {
      UiUtils.errorMessageDialog(
        context,
        context.tr(
          convertErrorCodeToLanguageKey(errorCodeNotEnoughCoins),
        ),
      );
      return;
    }
    showDialog<void>(
      context: context,
      builder: (_) => WatchRewardAdDialog(
        onTapYesButton: () => context.read<RewardedAdCubit>().showAd(
              context: context,
              onAdDismissedCallback: _continueAfterRewardAd,
            ),
      ),
    );
  }

  // Widget _coinCard(int coins, void Function(void Function()) state) {
  //   return GestureDetector(
  //     onTap: () => state(() => entryFee = coins),
  //     child: Container(
  //       width: 66,
  //       height: 86,
  //       decoration: BoxDecoration(
  //         borderRadius: BorderRadius.circular(10),
  //         color: entryFee == coins
  //             ? Theme.of(context).primaryColor
  //             : Theme.of(context).colorScheme.surface,
  //       ),
  //       child: Column(
  //         mainAxisAlignment: MainAxisAlignment.center,
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           SizedBox(
  //             height: 43,
  //             child: Center(
  //               child: Text(
  //                 coins.toString(),
  //                 style: TextStyle(
  //                   fontSize: 18,
  //                   fontWeight: FontWeights.regular,
  //                   color: entryFee == coins
  //                       ? Theme.of(context).colorScheme.surface
  //                       : Theme.of(context).colorScheme.onTertiary,
  //                 ),
  //               ),
  //             ),
  //           ),
  //           SizedBox(
  //             height: 43,
  //             child: Center(
  //               child: SvgPicture.asset(Assets.coin),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  void showJoinRoomBottomSheet() {
    final joinRoomCode = TextEditingController(text: '');
    // Reset Battle State to Initial.
    context
        .read<MultiUserBattleRoomCubit>()
        .updateState(MultiUserBattleRoomInitial(), cancelSubscription: true);
    context
        .read<BattleRoomCubit>()
        .updateState(BattleRoomInitial(), cancelSubscription: true);

    showModalBottomSheet<bool?>(
      isDismissible: true,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      backgroundColor: Colors.transparent,
      context: context,
      enableDrag: true,
      builder: (_) {
        return AnimatedContainer(
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white,
                Colors.white.withOpacity(0.95),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          height: scrHeight * 0.7,
          padding: const EdgeInsets.only(top: 20),
          duration: const Duration(milliseconds: 500),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // مؤشر السحب
              Center(
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 15),

              // Join Room title
              Center(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.login_rounded,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        "انضمام لغرفة",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 30),

              Align(
                child: Text(
                  "أدخل رمز الغرفة هنا",
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 30),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: PinCodeTextField(
                  appContext: context,
                  length: 6,
                  keyboardType: roomCodeCharType == RoomCodeCharType.onlyNumbers
                      ? TextInputType.number
                      : TextInputType.text,
                  textStyle: TextStyle(color: Theme.of(context).primaryColor),
                  pinTheme: PinTheme(
                    selectedFillColor:
                        Theme.of(context).primaryColor.withOpacity(0.1),
                    inactiveColor:
                        Theme.of(context).primaryColor.withOpacity(0.1),
                    activeColor:
                        Theme.of(context).primaryColor.withOpacity(0.1),
                    inactiveFillColor:
                        Theme.of(context).primaryColor.withOpacity(0.1),
                    selectedColor:
                        Theme.of(context).primaryColor.withOpacity(0.5),
                    shape: PinCodeFieldShape.box,
                    borderRadius: BorderRadius.circular(15),
                    fieldHeight: 50,
                    fieldWidth: 50,
                    activeFillColor:
                        Theme.of(context).primaryColor.withOpacity(0.2),
                  ),
                  cursorColor: Theme.of(context).primaryColor,
                  animationDuration: const Duration(milliseconds: 200),
                  enableActiveFill: true,
                  onChanged: (v) {},
                  controller: joinRoomCode,
                ),
              ),
              const SizedBox(height: 40),

              if (widget.quizType == QuizTypes.oneVsOneBattle)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30),
                  child: BlocConsumer<BattleRoomCubit, BattleRoomState>(
                    bloc: context.read<BattleRoomCubit>(),
                    listener: (context, state) {
                      if (state is BattleRoomUserFound) {
                        context.shouldPop();
                        inviteToRoomBottomSheet();
                      } else if (state is BattleRoomFailure) {
                        if (state.errorMessageCode ==
                            errorCodeUnauthorizedAccess) {
                          showAlreadyLoggedInDialog(context);
                          return;
                        }
                        UiUtils.errorMessageDialog(
                          context,
                          context.tr(
                            convertErrorCodeToLanguageKey(
                              state.errorMessageCode,
                            ),
                          ),
                        );
                      }
                    },
                    builder: (context, state) {
                      if (state is BattleRoomJoining) {
                        return const PopScope(
                          canPop: false,
                          child: CircularProgressContainer(),
                        );
                      }
                      //join here
                      return Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: CustomRoundedButton(
                          widthPercentage: MediaQuery.of(context).size.width,
                          backgroundColor: Theme.of(context).primaryColor,
                          radius: 20,
                          showBorder: false,
                          height: 60,
                          onTap: () {
                            // Close the Sheet if roomCode is Empty.
                            final roomCode = joinRoomCode.text.trim();
                            if (roomCode.isEmpty) {
                              Navigator.of(context).pop(true);
                              return;
                            }

                            final user = context
                                .read<UserDetailsCubit>()
                                .getUserProfile();

                            context.read<BattleRoomCubit>().joinRoom(
                                  currentCoin: user.coins!,
                                  name: user.name,
                                  uid: user.userId,
                                  profileUrl: user.profileUrl,
                                  roomCode: roomCode,
                                );
                          },
                          buttonTitle: "انضمام للغرفة",
                          titleColor: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                )
              else
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30),
                  child: BlocConsumer<MultiUserBattleRoomCubit,
                      MultiUserBattleRoomState>(
                    listener: (context, state) {
                      if (state is MultiUserBattleRoomSuccess) {
                        context.shouldPop();
                        inviteToRoomBottomSheet();
                      } else if (state is MultiUserBattleRoomFailure) {
                        if (state.errorMessageCode ==
                            errorCodeUnauthorizedAccess) {
                          showAlreadyLoggedInDialog(context);
                          return;
                        }
                        UiUtils.errorMessageDialog(
                          context,
                          context.tr(
                            convertErrorCodeToLanguageKey(
                              state.errorMessageCode,
                            ),
                          ),
                        );
                      }
                    },
                    bloc: context.read<MultiUserBattleRoomCubit>(),
                    builder: (_, state) {
                      if (state is MultiUserBattleRoomInProgress) {
                        return const PopScope(
                          canPop: false,
                          child: CircularProgressContainer(),
                        );
                      }

                      return Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: CustomRoundedButton(
                          widthPercentage: MediaQuery.of(context).size.width,
                          backgroundColor: Theme.of(context).primaryColor,
                          radius: 20,
                          showBorder: false,
                          height: 60,
                          onTap: () {
                            // Close Sheet if roomCode is Empty.
                            final roomCode = joinRoomCode.text.trim();
                            if (roomCode.isEmpty) {
                              Navigator.of(context).pop(true);
                              return;
                            }

                            final user = context
                                .read<UserDetailsCubit>()
                                .getUserProfile();

                            context.read<MultiUserBattleRoomCubit>().joinRoom(
                                  currentCoin: user.coins!,
                                  name: user.name,
                                  uid: user.userId,
                                  profileUrl: user.profileUrl,
                                  roomCode: roomCode,
                                );
                          },
                          buttonTitle: "انضمام للغرفة",
                          titleColor: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                ),
            ],
          ),
        );
      },
    ).then((value) {
      if (value != null && mounted) {
        return UiUtils.showSnackBar(
          "يرجى إدخال رمز الغرفة",
          context,
        );
      }
    });
  }

  void onBackTapJoinRoom() {
    if (widget.quizType == QuizTypes.oneVsOneBattle) {
      if (context.read<BattleRoomCubit>().state is! BattleRoomJoining) {
        Navigator.pop(context);
      }
    } else {
      if (context.read<MultiUserBattleRoomCubit>().state
          is! MultiUserBattleRoomInProgress) {
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: SingleChildScrollView(
        child: SizedBox(
          height: size.height,
          width: size.width,
          child: Stack(
            children: [
              /// خلفية محسنة مع تأثيرات بصرية
              Container(
                width: size.width,
                height: size.height,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withOpacity(0.8),
                      Theme.of(context).primaryColor.withOpacity(0.6),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Stack(
                  children: [
                    // دوائر زخرفية متداخلة للخلفية
                    Positioned(
                      top: -size.height * 0.1,
                      right: -size.width * 0.2,
                      child: Container(
                        width: size.width * 0.7,
                        height: size.width * 0.7,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: -size.height * 0.05,
                      left: -size.width * 0.1,
                      child: Container(
                        width: size.width * 0.6,
                        height: size.width * 0.6,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                    ),
                    // نمط شبكي خفيف للخلفية
                  ],
                ),
              ),

              /// Title & Back Button - تصميم محسن
              Align(
                alignment: Alignment.topCenter,
                child: Container(
                  margin: EdgeInsets.only(
                    top: size.height * 0.07,
                    left: size.width * 0.05,
                    right: size.width * 0.05,
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // زر الرجوع
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.2),
                        ),
                        child: IconButton(
                          onPressed: Navigator.of(context).pop,
                          icon: const Icon(
                            Icons.arrow_back_rounded,
                            size: 22,
                            color: Colors.white,
                          ),
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // عنوان الصفحة
                      Text(
                        widget.title,
                        style: const TextStyle(
                          fontSize: 22,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 16),
                      // أيقونة زخرفية
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.2),
                        ),
                        child: const Icon(
                          Icons.sports_esports,
                          color: Colors.white,
                          size: 22,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              /// رسم توضيحي للتحدي مع نص وصفي
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // رسم توضيحي للتحدي
                  Padding(
                    padding: EdgeInsets.only(top: size.height * 0.15),
                    child: Center(
                      child: Container(
                        width: size.width * 0.8,
                        height: size.width * 0.5,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 15,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // دائرة VS
                            Container(
                              width: 70,
                              height: 70,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Text(
                                  "VS",
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 24,
                                  ),
                                ),
                              ),
                            ),

                            // صورة اللاعب الأول
                            Positioned(
                              left: 20,
                              child: Container(
                                width: 90,
                                height: 90,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Theme.of(context).primaryColor,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 3,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 10,
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 45,
                                ),
                              ),
                            ),

                            // صورة اللاعب الثاني
                            Positioned(
                              right: 20,
                              child: Container(
                                width: 90,
                                height: 90,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Theme.of(context).primaryColor,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 3,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 10,
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 45,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // نص وصفي
                  Padding(
                    padding: EdgeInsets.only(
                      top: 20,
                      left: size.width * 0.1,
                      right: size.width * 0.1,
                    ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 15),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Text(
                        "تحدى أصدقائك في منافسة مباشرة واختبر معلوماتك معهم!",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              /// Bottom - Create/Join Container
              Positioned(
                left: 0,
                right: 0,
                bottom: size.height * 0.1,
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // زر إنشاء الغرفة
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: CustomRoundedButton(
                            widthPercentage:
                                MediaQuery.of(context).size.width * 0.8,
                            backgroundColor: Colors.white,
                            radius: 20,
                            showBorder: false,
                            height: 65,
                            onTap: showCreateRoomBottomSheet,
                            buttonTitle: "إنشاء غرفة",
                            titleColor: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        // زر الانضمام إلى الغرفة
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: CustomRoundedButton(
                            widthPercentage:
                                MediaQuery.of(context).size.width * 0.8,
                            backgroundColor: Theme.of(context).primaryColor,
                            radius: 20,
                            showBorder: true,
                            borderColor: Colors.white,
                            height: 65,
                            onTap: showJoinRoomBottomSheet,
                            buttonTitle: "انضمام لغرفة",
                            titleColor: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
