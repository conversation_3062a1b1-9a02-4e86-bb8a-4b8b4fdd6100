import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/audioQuestionBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/guessTheWordBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/musicPlayer/musicPlayerCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/quiz/models/guessTheWordQuestion.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionCubit.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/explanation_dialog.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/music_player_container.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/questionContainer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/report_question_bottom_sheet.dart';
import 'package:flutterquiz/ui/styles/colors.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import '../../../features/profileManagement/profileManagementRepository.dart';

class ReviewAnswersScreen extends StatefulWidget {
  const ReviewAnswersScreen({
    required this.questions,
    required this.guessTheWordQuestions,
    required this.quizType,
    super.key,
  });

  final List<Question> questions;
  final QuizTypes quizType;
  final List<GuessTheWordQuestion> guessTheWordQuestions;

  static Route<dynamic> route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments as Map?;
    //arguments will map and keys of the map are following
    //questions and guessTheWordQuestions
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<UpdateBookmarkCubit>(
            create: (context) => UpdateBookmarkCubit(BookmarkRepository()),
          ),
          BlocProvider<ReportQuestionCubit>(
            create: (_) => ReportQuestionCubit(ReportQuestionRepository()),
          ),
          // Add UpdateScoreAndCoinsCubit provider
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
        ],
        child: ReviewAnswersScreen(
          quizType: arguments!['quizType'] as QuizTypes,
          guessTheWordQuestions: arguments['guessTheWordQuestions']
                  as List<GuessTheWordQuestion>? ??
              <GuessTheWordQuestion>[],
          questions: arguments['questions'] as List<Question>? ?? <Question>[],
        ),
      ),
    );
  }

  @override
  State<ReviewAnswersScreen> createState() => _ReviewAnswersScreenState();
}

class _ReviewAnswersScreenState extends State<ReviewAnswersScreen> {
  late final _pageController = PageController();
  int _currQueIdx = 0;

  late final _firebaseId = context.read<UserDetailsCubit>().getUserFirebaseId();

  late final _isGuessTheWord = widget.quizType == QuizTypes.guessTheWord;
  late final _isAudioQuestions = widget.quizType == QuizTypes.audioQuestions;

  late final questionsLength = _isGuessTheWord
      ? widget.guessTheWordQuestions.length
      : widget.questions.length;

  late final _musicPlayerKeys = List.generate(
    widget.questions.length,
    (_) => GlobalKey<MusicPlayerContainerState>(),
    growable: false,
  );
  late final _correctAnswerIds = List.generate(
    widget.questions.length,
    (i) => AnswerEncryption.decryptCorrectAnswer(
      rawKey: _firebaseId,
      correctAnswer: widget.questions[i].correctAnswer!,
    ),
    growable: false,
  );

  bool get isLatex {
    return context.read<SystemConfigCubit>().isLatexModeEnabled &&
        switch (widget.quizType) {
          QuizTypes.quizZone => true,
          QuizTypes.trueAndFalse => true,
          QuizTypes.dailyQuiz => true,
          QuizTypes.selfChallenge => true,
          QuizTypes.exam => true,
          QuizTypes.mathMania => true,
          _ => false,
        };
  }

  void _showExplanationDialog() {
    if (!_hasExplanation()) return;

    final currentQuestion = widget.questions[_currQueIdx];
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ExplanationDialog(
          question: currentQuestion,
          // لا نحتاج لإيقاف المؤقت في صفحة المراجعة لأنه لا يوجد مؤقت
          onDialogOpened: null,
          onDialogClosed: null,
        );
      },
    );
  }

  void _onTapReportQuestion() {
    showReportQuestionBottomSheet(
      context: context,
      questionId: _isGuessTheWord
          ? widget.guessTheWordQuestions[_currQueIdx].id
          : widget.questions[_currQueIdx].id!,
      reportQuestionCubit: context.read<ReportQuestionCubit>(),
    );
  }

  void _onPageChanged(int idx) {
    if (_isAudioQuestions) {
      _musicPlayerKeys[_currQueIdx].currentState?.stopAudio();
      _musicPlayerKeys[idx].currentState?.playAudio();
    }
    setState(() => _currQueIdx = idx);
  }

  Color _optionBackgroundColor(String? optionId) {
    if (optionId == _correctAnswerIds[_currQueIdx]) {
      return kCorrectAnswerColor;
    }

    if (optionId == widget.questions[_currQueIdx].submittedAnswerId) {
      return kHurryUpTimerColor;
    }

    return Theme.of(context).primaryColor.withValues(alpha: 0.5);
  }

  Color _optionTextColor(String? optionId) {
    final correctAnswerId = _correctAnswerIds[_currQueIdx];
    final submittedAnswerId = widget.questions[_currQueIdx].submittedAnswerId;

    return optionId == correctAnswerId || optionId == submittedAnswerId
        ? Theme.of(context).colorScheme.surface
        : Theme.of(context).colorScheme.surface;
  }

  /// التحقق من وجود شرح للسؤال الحالي
  bool _hasExplanation() {
    if (_isGuessTheWord || widget.questions.isEmpty) return false;

    final currentQuestion = widget.questions[_currQueIdx];
    final hasNote =
        currentQuestion.note != null && currentQuestion.note!.isNotEmpty;
    final hasVideo = currentQuestion.hasVideo;

    return hasNote || hasVideo;
  }

//int radius=8;
  //to build option of given question
  // TODO(J): Latex Options List
  // Sizing issues with Latex Options Lists.
  Widget _buildOptions() => Column(
        children: widget.questions[_currQueIdx].answerOptions!
            .map(_buildOption)
            .toList(),
      );

  Widget _buildGuessTheWordOptionAndAnswer(
    GuessTheWordQuestion guessTheWordQuestion,
  ) {
    final isCorrect = UiUtils.buildGuessTheWordQuestionAnswer(
          guessTheWordQuestion.submittedAnswer,
        ) ==
        guessTheWordQuestion.answer;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 25),
        Padding(
          padding: EdgeInsets.zero,
          child: Text(
            "${context.tr("yourAnsLbl")!} : ${UiUtils.buildGuessTheWordQuestionAnswer(guessTheWordQuestion.submittedAnswer)}",
            style: TextStyle(
              fontSize: 18,
              color: isCorrect
                  ? kCorrectAnswerColor
                  : Theme.of(context).colorScheme.onTertiary,
            ),
          ),
        ),
        if (!isCorrect) ...[
          Padding(
            padding: EdgeInsetsDirectional.zero,
            child: Text(
              "${context.tr("correctAndLbl")!}: ${guessTheWordQuestion.answer}",
              style: TextStyle(
                fontSize: 18,
                color: Theme.of(context).colorScheme.onTertiary,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildQuestionAndOptions(Question question, int index) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          QuestionContainer(
            isMathQuestion: isLatex,
            question: question,
            questionColor: Theme.of(context).colorScheme.onTertiary,
          ),
          if (_isAudioQuestions)
            BlocProvider<MusicPlayerCubit>(
              create: (_) => MusicPlayerCubit(),
              child: MusicPlayerContainer(
                currentIndex: _currQueIdx,
                index: index,
                url: question.audio!,
                key: _musicPlayerKeys[index],
              ),
            )
          else
            const SizedBox(),
          _buildOptions(),
          const SizedBox(height: 15),
        ],
      ),
    );
  }

  Widget _buildGuessTheWordQuestionAndOptions(GuessTheWordQuestion question) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          QuestionContainer(
            isMathQuestion: false,
            questionColor: Theme.of(context).colorScheme.onTertiary,
            question: Question(
              marks: '',
              id: question.id,
              question: question.question,
              imageUrl: question.image,
            ),
          ),
          //build options
          _buildGuessTheWordOptionAndAnswer(question),
        ],
      ),
    );
  }

  Widget _buildExplanationButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Tooltip(
        message: 'اشرح لي',
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: () => _showExplanationDialog(),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.lightbulb_outline,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReportButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Tooltip(
        message: context.tr('reportQuestion') ?? 'Report Question',
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: _onTapReportQuestion,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.flag_rounded,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBookmarkButton() {
    // For Quiz Zone type
    if (widget.quizType == QuizTypes.quizZone) {
      final bookmarkCubit = context.read<BookmarkCubit>();
      final updateBookmarkCubit = context.read<UpdateBookmarkCubit>();

      return BlocListener<UpdateBookmarkCubit, UpdateBookmarkState>(
        bloc: updateBookmarkCubit,
        listener: (context, state) {
          if (state is UpdateBookmarkFailure) {
            if (state.errorMessageCode == errorCodeUnauthorizedAccess) {
              showAlreadyLoggedInDialog(context);
              return;
            }

            if (state.failedStatus == '0') {
              bookmarkCubit.addBookmarkQuestion(widget.questions[_currQueIdx]);
            } else {
              bookmarkCubit.removeBookmarkQuestion(
                widget.questions[_currQueIdx].id!,
              );
            }

            UiUtils.showSnackBar(
              context.tr(
                convertErrorCodeToLanguageKey(
                  errorCodeUpdateBookmarkFailure,
                ),
              )!,
              context,
            );
          }
        },
        child: BlocBuilder<BookmarkCubit, BookmarkState>(
          bloc: bookmarkCubit,
          builder: (context, state) {
            if (state is BookmarkFetchSuccess) {
              final isBookmarked = bookmarkCubit.hasQuestionBookmarked(
                widget.questions[_currQueIdx].id!,
              );

              return _buildBookmarkButtonUI(
                isBookmarked: isBookmarked,
                onTap: () {
                  if (isBookmarked) {
                    bookmarkCubit.removeBookmarkQuestion(
                      widget.questions[_currQueIdx].id!,
                    );
                    updateBookmarkCubit.updateBookmark(
                      widget.questions[_currQueIdx].id!,
                      '0',
                      '1',
                    );
                    _showBookmarkSnackBar(false);
                  } else {
                    bookmarkCubit.addBookmarkQuestion(
                      widget.questions[_currQueIdx],
                    );
                    updateBookmarkCubit.updateBookmark(
                      widget.questions[_currQueIdx].id!,
                      '1',
                      '1',
                    );
                    _showBookmarkSnackBar(true);
                  }
                },
              );
            }

            if (state is BookmarkFetchFailure) {
              log('Bookmark Fetch Failure: ${state.errorMessageCode}');
            }
            return const SizedBox();
          },
        ),
      );
    }

    // For Audio Questions type
    if (widget.quizType == QuizTypes.audioQuestions) {
      final bookmarkCubit = context.read<AudioQuestionBookmarkCubit>();
      final updateBookmarkCubit = context.read<UpdateBookmarkCubit>();

      return BlocListener<UpdateBookmarkCubit, UpdateBookmarkState>(
        bloc: updateBookmarkCubit,
        listener: (context, state) {
          if (state is UpdateBookmarkFailure) {
            if (state.errorMessageCode == errorCodeUnauthorizedAccess) {
              showAlreadyLoggedInDialog(context);
              return;
            }

            if (state.failedStatus == '0') {
              bookmarkCubit.addBookmarkQuestion(widget.questions[_currQueIdx]);
            } else {
              bookmarkCubit.removeBookmarkQuestion(
                widget.questions[_currQueIdx].id!,
              );
            }

            UiUtils.showSnackBar(
              context.tr(
                convertErrorCodeToLanguageKey(
                  errorCodeUpdateBookmarkFailure,
                ),
              )!,
              context,
            );
          }
        },
        child:
            BlocBuilder<AudioQuestionBookmarkCubit, AudioQuestionBookMarkState>(
          bloc: bookmarkCubit,
          builder: (context, state) {
            if (state is AudioQuestionBookmarkFetchSuccess) {
              final isBookmarked = bookmarkCubit.hasQuestionBookmarked(
                widget.questions[_currQueIdx].id!,
              );

              return _buildBookmarkButtonUI(
                isBookmarked: isBookmarked,
                onTap: () {
                  if (isBookmarked) {
                    bookmarkCubit.removeBookmarkQuestion(
                      widget.questions[_currQueIdx].id!,
                    );
                    updateBookmarkCubit.updateBookmark(
                      widget.questions[_currQueIdx].id!,
                      '0',
                      '4', // Type 4 for audio questions
                    );
                    _showBookmarkSnackBar(false);
                  } else {
                    bookmarkCubit.addBookmarkQuestion(
                      widget.questions[_currQueIdx],
                    );
                    updateBookmarkCubit.updateBookmark(
                      widget.questions[_currQueIdx].id!,
                      '1',
                      '4', // Type 4 for audio questions
                    );
                    _showBookmarkSnackBar(true);
                  }
                },
              );
            }
            return const SizedBox();
          },
        ),
      );
    }

    // For Guess The Word type
    if (widget.quizType == QuizTypes.guessTheWord) {
      final bookmarkCubit = context.read<GuessTheWordBookmarkCubit>();
      final updateBookmarkCubit = context.read<UpdateBookmarkCubit>();

      return BlocListener<UpdateBookmarkCubit, UpdateBookmarkState>(
        bloc: updateBookmarkCubit,
        listener: (context, state) {
          if (state is UpdateBookmarkFailure) {
            if (state.errorMessageCode == errorCodeUnauthorizedAccess) {
              showAlreadyLoggedInDialog(context);
              return;
            }

            if (state.failedStatus == '0') {
              bookmarkCubit.addBookmarkQuestion(
                widget.guessTheWordQuestions[_currQueIdx],
              );
            } else {
              bookmarkCubit.removeBookmarkQuestion(
                widget.guessTheWordQuestions[_currQueIdx].id,
              );
            }

            UiUtils.showSnackBar(
              context.tr(
                convertErrorCodeToLanguageKey(
                  errorCodeUpdateBookmarkFailure,
                ),
              )!,
              context,
            );
          }
        },
        child:
            BlocBuilder<GuessTheWordBookmarkCubit, GuessTheWordBookmarkState>(
          bloc: context.read<GuessTheWordBookmarkCubit>(),
          builder: (context, state) {
            if (state is GuessTheWordBookmarkFetchSuccess) {
              final isBookmarked = bookmarkCubit.hasQuestionBookmarked(
                widget.guessTheWordQuestions[_currQueIdx].id,
              );

              return _buildBookmarkButtonUI(
                isBookmarked: isBookmarked,
                onTap: () {
                  if (isBookmarked) {
                    bookmarkCubit.removeBookmarkQuestion(
                      widget.guessTheWordQuestions[_currQueIdx].id,
                    );
                    updateBookmarkCubit.updateBookmark(
                      widget.guessTheWordQuestions[_currQueIdx].id,
                      '0',
                      '3', // Type 3 for guess the word questions
                    );
                    _showBookmarkSnackBar(false);
                  } else {
                    bookmarkCubit.addBookmarkQuestion(
                      widget.guessTheWordQuestions[_currQueIdx],
                    );
                    updateBookmarkCubit.updateBookmark(
                      widget.guessTheWordQuestions[_currQueIdx].id,
                      '1',
                      '3', // Type 3 for guess the word questions
                    );
                    _showBookmarkSnackBar(true);
                  }
                },
              );
            }
            return const SizedBox();
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }

  // Helper method to show a beautiful snackbar for bookmark actions
  void _showBookmarkSnackBar(bool isBookmarked) {
    final message = isBookmarked
        ? 'تم حفظ السؤال في صفحة أخطائي'
        : 'تم إزالة السؤال من صفحة أخطائي';

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isBookmarked ? Icons.bookmark_added : Icons.bookmark_remove,
              color: Colors.white,
            ),
            const SizedBox(width: 10),
            Text(
              message,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        backgroundColor:
            isBookmarked ? Theme.of(context).primaryColor : Colors.redAccent,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'حسناً',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // Helper method to build consistent bookmark button UI
  Widget _buildBookmarkButtonUI({
    required bool isBookmarked,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Tooltip(
        message: context.tr(isBookmarked ? 'removeBookmark' : 'addBookmark') ??
            (isBookmarked ? 'Remove Bookmark' : 'Add Bookmark'),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: onTap,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.all(8),
              child: Icon(
                isBookmarked
                    ? CupertinoIcons.bookmark_fill
                    : CupertinoIcons.bookmark,
                color: isBookmarked
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).primaryColor,
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UpdateScoreAndCoinsCubit, UpdateScoreAndCoinsState>(
      listener: (context, state) {
        if (state is UpdateScoreAndCoinsFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      child: Scaffold(
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
                Colors.white,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: const [0.2, 0.9],
            ),
          ),
          child: SafeArea(
            child: Stack(
              children: [
                // زخارف الخلفية
                Positioned(
                  top: -50,
                  right: -50,
                  child: Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.2),
                    ),
                  ),
                ),

                Positioned(
                  bottom: -80,
                  left: -80,
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Theme.of(context)
                          .primaryColor
                          .withValues(alpha: 0.15),
                    ),
                  ),
                ),

                // شريط العنوان المخصص
                Positioned(
                  top: 10,
                  left: 16,
                  right: 16,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // زر الرجوع بتصميم محسن
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: Icon(
                            Icons.arrow_back_ios_new_rounded,
                            color: Theme.of(context).primaryColor,
                            size: 20,
                          ),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ),

                      // العنوان
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Text(
                          context.tr('reviewAnswerLbl')!,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),

                      // عداد الأسئلة
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 15.0,
                          vertical: 8.0,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Text(
                          '${_currQueIdx + 1}/$questionsLength',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // محتوى الأسئلة
                Padding(
                  padding: const EdgeInsets.only(
                    top: 80,
                    left: 16,
                    right: 16,
                    bottom: 80,
                  ),
                  child: Card(
                    elevation: 8,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Column(
                        children: [
                          // Action buttons - Bookmark and Report
                          Padding(
                            padding: const EdgeInsets.only(
                                top: 8, right: 8, left: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // زر الشرح
                                if (!_isGuessTheWord && _hasExplanation())
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .primaryColor
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: _buildExplanationButton(),
                                  ),
                                if (!_isGuessTheWord && _hasExplanation())
                                  const SizedBox(width: 8),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: _buildBookmarkButton(),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: _buildReportButton(),
                                ),
                              ],
                            ),
                          ),

                          // PageView for questions
                          Expanded(
                            child: PageView.builder(
                              controller: _pageController,
                              onPageChanged: _onPageChanged,
                              itemCount: questionsLength,
                              itemBuilder: (context, index) {
                                return SingleChildScrollView(
                                  padding: const EdgeInsets.all(20),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.stretch,
                                    children: [
                                      // Question Container
                                      Container(
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              Theme.of(context)
                                                  .primaryColor
                                                  .withValues(alpha: 0.1),
                                              Theme.of(context)
                                                  .primaryColor
                                                  .withValues(alpha: 0.05),
                                            ],
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(15),
                                          border: Border.all(
                                            color: Theme.of(context)
                                                .primaryColor
                                                .withValues(alpha: 0.2),
                                          ),
                                        ),
                                        child: _isGuessTheWord
                                            ? _buildGuessTheWordQuestionAndOptions(
                                                widget.guessTheWordQuestions[
                                                    index],
                                              )
                                            : _buildQuestionAndOptions(
                                                widget.questions[index],
                                                index,
                                              ),
                                      ),

                                      const SizedBox(height: 20),

                                      // Notes Section (if available)
                                      if (!_isGuessTheWord &&
                                          widget.questions[index].note
                                                  ?.isNotEmpty ==
                                              true)
                                        Container(
                                          padding: const EdgeInsets.all(16),
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                .primaryColor
                                                .withValues(alpha: 0.05),
                                            borderRadius:
                                                BorderRadius.circular(15),
                                            border: Border.all(
                                              color: Theme.of(context)
                                                  .primaryColor
                                                  .withValues(alpha: 0.2),
                                            ),
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Icon(
                                                    Icons.lightbulb_outline,
                                                    color: Theme.of(context)
                                                        .primaryColor,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(
                                                    context.tr(notesKey)!,
                                                    style: TextStyle(
                                                      fontSize: 18,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Theme.of(context)
                                                          .primaryColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 8),
                                              Text(
                                                widget.questions[index].note!,
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                  height: 1.5,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // أزرار التنقل بين الأسئلة
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // زر السؤال السابق
                      if (_currQueIdx > 0)
                        Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.arrow_back_ios_new_rounded,
                              color: Colors.white,
                            ),
                            onPressed: () {
                              _pageController.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            },
                          ),
                        )
                      else
                        const SizedBox(width: 48),

                      // زر السؤال التالي
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context)
                                  .primaryColor
                                  .withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: Icon(
                            _currQueIdx == questionsLength - 1
                                ? Icons.check_circle_outline_rounded
                                : Icons.arrow_forward_ios_rounded,
                            color: Colors.white,
                          ),
                          onPressed: () {
                            if (_currQueIdx == questionsLength - 1) {
                              Navigator.of(context).pop();
                            } else {
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOption(AnswerOption option) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _optionBackgroundColor(option.id),
            _optionBackgroundColor(option.id).withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: _optionBackgroundColor(option.id).withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {},
          borderRadius: BorderRadius.circular(15),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 15,
            ),
            child: Text(
              option.title!,
              textAlign: TextAlign.center,
              style: GoogleFonts.ibmPlexSansArabic(
                color: _optionTextColor(option.id),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
