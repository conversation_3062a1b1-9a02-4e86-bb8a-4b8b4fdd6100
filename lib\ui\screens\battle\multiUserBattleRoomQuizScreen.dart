import 'dart:async';
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/battleRoom/battleRoomRepository.dart';
import 'package:flutterquiz/features/battleRoom/cubits/messageCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/multiUserBattleRoomCubit.dart';
import 'package:flutterquiz/features/battleRoom/models/battleRoom.dart';
import 'package:flutterquiz/features/battleRoom/models/message.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/models/userBattleRoomDetails.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/battle/battle_voice_chat_provider.dart';
import 'package:flutterquiz/ui/screens/battle/voice_chat_controller.dart';
import 'package:flutterquiz/ui/screens/battle/widgets/messageBoxContainer.dart';
import 'package:flutterquiz/ui/screens/battle/widgets/waitForOthersContainer.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/exitGameDialog.dart';
import 'package:flutterquiz/ui/widgets/questionsContainer.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/internet_connectivity.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

// رسام الخلفية المزخرفة للمعركة
class BattleBackgroundPainter extends CustomPainter {
  final Color color;

  BattleBackgroundPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // رسم الأشكال الزخرفية
    final random = Random(42); // استخدام بذرة ثابتة للحصول على نفس النمط دائمًا

    // رسم دوائر عشوائية
    for (var i = 0; i < 15; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = 5.0 + random.nextDouble() * 20.0;

      canvas.drawCircle(
        Offset(x, y),
        radius,
        paint,
      );
    }

    // رسم خطوط متقاطعة
    for (var i = 0; i < 10; i++) {
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;
      final endX = random.nextDouble() * size.width;
      final endY = random.nextDouble() * size.height;

      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        paint..strokeWidth = 1.0,
      );
    }

    // رسم مستطيلات بزوايا دائرية
    for (var i = 0; i < 5; i++) {
      final left = random.nextDouble() * size.width;
      final top = random.nextDouble() * size.height;
      final width = 20.0 + random.nextDouble() * 50.0;
      final height = 20.0 + random.nextDouble() * 50.0;

      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(left, top, width, height),
        const Radius.circular(8),
      );

      canvas.drawRRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// بطاقة ملف المستخدم المحسنة - مع مؤشرات حالة الميكروفون ومستوى الصوت
Widget _buildUserProfileCard({
  required BuildContext context,
  required String name,
  required String profileUrl,
  required String correctAnswers,
  required String totalQuestions,
  required bool isMuted,
  bool isCurrentUser = false,
  VoidCallback? onMuteToggle,
}) {
  // تحديد الألوان بناءً على حالة المستخدم
  final Color cardColor = isCurrentUser
      ? Theme.of(context).primaryColor.withOpacity(0.3)
      : Colors.black.withOpacity(0.3);

  final Color borderColor = isCurrentUser
      ? Theme.of(context).primaryColor.withOpacity(0.4)
      : Colors.white.withOpacity(0.2);

  final Color micColor = isMuted ? Colors.red : Colors.green;

  // تقصير الاسم إذا كان طويلاً
  final String displayName =
      name.length > 10 ? '${name.substring(0, 8)}...' : name;

  // الحصول على مستوى الصوت للمستخدم
  double volumeLevel = 0.0;
  if (!isMuted) {
    if (isCurrentUser) {
      // مستوى صوت المستخدم الحالي
      volumeLevel = BattleVoiceChatProvider.of(context)
              ?.controller
              .getLocalUserVolumeLevel() ??
          0.0;
    } else {
      // مستوى صوت المستخدم البعيد
      final uid = int.tryParse(name.hashCode.toString()) ??
          0; // استخدام هاش الاسم كمعرف مؤقت
      volumeLevel = BattleVoiceChatProvider.of(context)
              ?.controller
              .getRemoteUserVolumeLevel(uid) ??
          0.0;
    }
  }

  // تحديد لون الحدود بناءً على مستوى الصوت
  Color activeBorderColor = borderColor;
  if (!isMuted && volumeLevel > 0.1) {
    // تغيير لون الحدود عندما يتحدث المستخدم
    if (volumeLevel > 0.7) {
      activeBorderColor = Colors.red; // مستوى صوت مرتفع
    } else if (volumeLevel > 0.4) {
      activeBorderColor = Colors.orange; // مستوى صوت متوسط
    } else {
      activeBorderColor = Colors.green; // مستوى صوت منخفض
    }
  }

  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
    decoration: BoxDecoration(
      color: cardColor,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: !isMuted && volumeLevel > 0.1 ? activeBorderColor : borderColor,
        width: !isMuted && volumeLevel > 0.1 ? 2.0 : 1.5,
      ),
      boxShadow: [
        BoxShadow(
          color: !isMuted && volumeLevel > 0.1
              ? activeBorderColor.withOpacity(0.3)
              : Colors.black.withOpacity(0.1),
          blurRadius: !isMuted && volumeLevel > 0.1 ? 10 : 8,
          offset: const Offset(0, 3),
        ),
      ],
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // صف الصورة الشخصية وأزرار التحكم
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // صورة المستخدم مع مؤشر حالة الميكروفون
            Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isCurrentUser
                          ? Theme.of(context).primaryColor
                          : Colors.white,
                      width: 1.5,
                    ),
                  ),
                  child: CircleAvatar(
                    radius: 16, // حجم أصغر للصورة
                    backgroundColor: Colors.grey[300],
                    backgroundImage: NetworkImage(profileUrl),
                  ),
                ),

                // مؤشر المستخدم الحالي
                if (isCurrentUser)
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 1,
                        ),
                      ),
                    ),
                  ),

                // مؤشر حالة الميكروفون
                Positioned(
                  left: 0,
                  bottom: 0,
                  child: Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: isMuted ? Colors.red : Colors.green,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      isMuted ? Icons.mic_off : Icons.mic,
                      color: Colors.white,
                      size: 6,
                    ),
                  ),
                ),
              ],
            ),

            // مؤشر مستوى الصوت للمستخدمين الذين يتحدثون
            if (!isMuted && volumeLevel > 0.1)
              Container(
                width: 20,
                height: 20,
                margin: const EdgeInsets.only(right: 4),
                child: CustomPaint(
                  painter: VoiceLevelPainter(
                    level: volumeLevel,
                    color: activeBorderColor,
                    barCount: 4,
                    barWidth: 2,
                    spacing: 1,
                  ),
                ),
              ),

            // أزرار التحكم - محسنة
            GestureDetector(
              onTap: onMuteToggle,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: micColor.withOpacity(0.2),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: micColor.withOpacity(0.5),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: micColor.withOpacity(0.2),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  isMuted ? Icons.mic_off : Icons.mic,
                  color: micColor,
                  size: 14,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 4),

        // اسم المستخدم
        Text(
          displayName,
          style: TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.5),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 2),

        // عدد الإجابات الصحيحة
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            "$correctAnswers/$totalQuestions",
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ),
      ],
    ),
  );
}

class MultiUserBattleRoomQuizScreen extends StatefulWidget {
  const MultiUserBattleRoomQuizScreen({super.key});

  @override
  State<MultiUserBattleRoomQuizScreen> createState() =>
      _MultiUserBattleRoomQuizScreenState();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
          BlocProvider<UpdateBookmarkCubit>(
            create: (_) => UpdateBookmarkCubit(BookmarkRepository()),
          ),
          BlocProvider<MessageCubit>(
            create: (_) => MessageCubit(BattleRoomRepository()),
          ),
        ],
        child: const MultiUserBattleRoomQuizScreen(),
      ),
    );
  }
}

class _MultiUserBattleRoomQuizScreenState
    extends State<MultiUserBattleRoomQuizScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  // تم حذف متغير _isSpeakerEnabled غير المستخدم
  late AnimationController timerAnimationController = AnimationController(
    vsync: this,
    duration: Duration(
      seconds: context.read<SystemConfigCubit>().quizTimer(QuizTypes.groupPlay),
    ),
  )
    ..addStatusListener(currentUserTimerAnimationStatusListener)
    ..forward();

  //to animate the question container
  late AnimationController questionAnimationController;
  late AnimationController questionContentAnimationController;

  //to slide the question container from right to left
  late Animation<double> questionSlideAnimation;

  //to scale up the second question
  late Animation<double> questionScaleUpAnimation;

  //to scale down the second question
  late Animation<double> questionScaleDownAnimation;

  //to slude the question content from right to left
  late Animation<double> questionContentAnimation;

  late AnimationController messageAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 300),
    reverseDuration: const Duration(milliseconds: 300),
  );
  late Animation<double> messageAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: messageAnimationController,
      curve: Curves.easeOutBack,
    ),
  );

  late List<AnimationController> opponentMessageAnimationControllers = [];
  late List<Animation<double>> opponentMessageAnimations = [];

  late List<AnimationController> opponentProgressAnimationControllers = [];

  late AnimationController messageBoxAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 350),
  );
  late Animation<double> messageBoxAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: messageBoxAnimationController,
      curve: Curves.easeInOut,
    ),
  );

  int currentQuestionIndex = 0;

  //if user has minimized the app
  bool showUserLeftTheGame = false;

  bool showWaitForOthers = false;

  //to track if setting dialog is open
  bool isSettingDialogOpen = false;

  bool isExitDialogOpen = false;

  // وحدة تحكم المحادثة الصوتية
  final VoiceChatController _voiceChatController = VoiceChatController();

  //current user message timer
  Timer? currentUserMessageDisappearTimer;
  int currentUserMessageDisappearTimeInSeconds = 4;

  List<Timer?> opponentsMessageDisappearTimer = [];
  List<int> opponentsMessageDisappearTimeInSeconds = [];

  late double userDetaislHorizontalPaddingPercentage =
      (1.0 - UiUtils.questionContainerWidthPercentage) * (0.5);

  late List<Message> latestMessagesByUsers = [];
  late int userLength;

  @override
  void initState() {
    super.initState();
    //add empty messages ofr every user
    WakelockPlus.enable();
    for (var i = 0; i < maxUsersInGroupBattle; i++) {
      latestMessagesByUsers.add(Message.empty());
    }

    //deduct coins of entry fee
    Future.delayed(Duration.zero, () {
      context.read<UpdateScoreAndCoinsCubit>().updateCoins(
            coins: context.read<MultiUserBattleRoomCubit>().getEntryFee(),
            addCoin: false,
            title: playedGroupBattleKey,
          );
      context.read<UserDetailsCubit>().updateCoins(
            addCoin: false,
            coins: context.read<MultiUserBattleRoomCubit>().getEntryFee(),
          );
      context.read<MessageCubit>().subscribeToMessages(
            context.read<MultiUserBattleRoomCubit>().getRoomId(),
          );
      //Get join user length

      // تهيئة المحادثة الصوتية
      _initializeVoiceChat();
    });
    initializeAnimation();
    initOpponentConfig();
    questionContentAnimationController.forward();
    //add observer to track app lifecycle activity
    WidgetsBinding.instance.addObserver(this);
    userLength = context.read<MultiUserBattleRoomCubit>().getUsers().length;
  }

  // تهيئة المحادثة الصوتية
  Future<void> _initializeVoiceChat() async {
    final battleRoomCubit = context.read<MultiUserBattleRoomCubit>();
    final state = battleRoomCubit.state;

    if (state is MultiUserBattleRoomSuccess &&
        state.battleRoom.isVoiceChatEnabled == true &&
        state.battleRoom.voiceChannelId != null &&
        state.battleRoom.voiceChannelId!.isNotEmpty) {
      debugPrint("بدء تهيئة المحادثة الصوتية والانضمام إلى القناة...");

      try {
        // الانضمام إلى القناة الصوتية
        final uid =
            int.tryParse(context.read<UserDetailsCubit>().userId()) ?? 0;

        // حفظ معرف القناة ومعرف المستخدم
        final channelId = state.battleRoom.voiceChannelId!;

        // تهيئة المحادثة الصوتية مباشرة باستخدام وحدة التحكم
        final initialized = await _voiceChatController.initialize();

        if (!initialized) {
          debugPrint("فشل في تهيئة المحادثة الصوتية");
          return;
        }

        // الانضمام إلى القناة
        final success = await _voiceChatController.joinChannel(channelId, uid);

        if (success) {
          debugPrint("تم تهيئة المحادثة الصوتية والانضمام إلى القناة بنجاح");

          // محاولة تمكين مكبر الصوت بعد الانضمام
          final speakerSuccess =
              await _voiceChatController.enableSpeakerphoneAfterJoin(
            delayMs: 1500, // تأخير أطول للتأكد من اكتمال الانضمام
          );

          if (speakerSuccess) {
            debugPrint("تم تمكين مكبر الصوت بعد الانضمام بنجاح");
          } else {
            debugPrint(
                "فشل في تمكين مكبر الصوت بعد الانضمام، محاولة مرة أخرى...");

            // محاولة ثانية بعد تأخير إضافي
            await Future.delayed(const Duration(seconds: 2));
            final secondAttempt = await _voiceChatController.toggleSpeakerphone(
              true,
              maxRetries: 5,
              delayMs: 800,
            );

            if (secondAttempt) {
              debugPrint("نجحت المحاولة الثانية لتمكين مكبر الصوت");
            } else {
              debugPrint("فشلت جميع محاولات تمكين مكبر الصوت");
            }
          }
        } else {
          debugPrint("فشل في تهيئة المحادثة الصوتية أو الانضمام إلى القناة");
        }
      } catch (e) {
        debugPrint("خطأ غير متوقع في تهيئة المحادثة الصوتية: $e");
      }
    } else {
      debugPrint("المحادثة الصوتية غير ممكنة أو معرف القناة غير متوفر");
    }
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    timerAnimationController
      ..removeStatusListener(currentUserTimerAnimationStatusListener)
      ..dispose();
    questionAnimationController.dispose();
    questionContentAnimationController.dispose();
    messageAnimationController.dispose();
    for (final element in opponentMessageAnimationControllers) {
      element.dispose();
    }
    for (final element in opponentProgressAnimationControllers) {
      element.dispose();
    }
    for (final element in opponentsMessageDisappearTimer) {
      element?.cancel();
    }
    messageBoxAnimationController.dispose();
    currentUserMessageDisappearTimer?.cancel();
    _voiceChatController.dispose();
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  bool appWasPaused = false;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    //remove user from room
    if (state == AppLifecycleState.paused) {
      appWasPaused = true;
      final multiUserBattleRoomCubit = context.read<MultiUserBattleRoomCubit>();
      //if user has already won the game then do nothing
      if (multiUserBattleRoomCubit.getUsers().length != 1) {
        deleteMessages(multiUserBattleRoomCubit);
        multiUserBattleRoomCubit
            .deleteUserFromRoom(context.read<UserDetailsCubit>().userId());
      }
      //
    } else if (state == AppLifecycleState.resumed && appWasPaused) {
      final multiUserBattleRoomCubit = context.read<MultiUserBattleRoomCubit>();
      //if user has won the game already
      if (multiUserBattleRoomCubit.getUsers().length == 1 &&
          multiUserBattleRoomCubit.getUsers().first!.uid ==
              context.read<UserDetailsCubit>().userId()) {
        setState(() {
          showUserLeftTheGame = false;
        });
      }
      //
      else {
        setState(() {
          showUserLeftTheGame = true;
        });
      }

      timerAnimationController.stop();
    }
  }

  void deleteMessages(MultiUserBattleRoomCubit battleRoomCubit) {
    //to delete messages by given user
    context.read<MessageCubit>().deleteMessages(
          battleRoomCubit.getRoomId(),
          context.read<UserDetailsCubit>().userId(),
        );
  }

  void initOpponentConfig() {
    //
    for (var i = 0; i < (maxUsersInGroupBattle - 1); i++) {
      opponentMessageAnimationControllers.add(
        AnimationController(
          vsync: this,
          duration: const Duration(milliseconds: 300),
        ),
      );
      opponentProgressAnimationControllers
          .add(AnimationController(vsync: this));
      opponentMessageAnimations.add(
        Tween<double>(begin: 0, end: 1).animate(
          CurvedAnimation(
            parent: opponentMessageAnimationControllers[i],
            curve: Curves.easeOutBack,
          ),
        ),
      );
      opponentsMessageDisappearTimer.add(null);
      opponentsMessageDisappearTimeInSeconds.add(4);
    }
  }

  //
  void initializeAnimation() {
    questionAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    questionContentAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );

    questionSlideAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    questionScaleUpAnimation = Tween<double>(begin: 0, end: 0.1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0, 0.5, curve: Curves.easeInQuad),
      ),
    );
    questionScaleDownAnimation = Tween<double>(begin: 0, end: 0.05).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0.5, 1, curve: Curves.easeOutQuad),
      ),
    );
    questionContentAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionContentAnimationController,
        curve: Curves.easeInQuad,
      ),
    );
  }

  void toggleSettingDialog() {
    isSettingDialogOpen = !isSettingDialogOpen;
  }

  //listener for current user timer
  void currentUserTimerAnimationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      submitAnswer('-1');
    }
  }

  //update answer locally and on cloud
  Future<void> submitAnswer(String submittedAnswer) async {
    //
    timerAnimationController.stop();
    final battleRoomCubit = context.read<MultiUserBattleRoomCubit>();
    final questions = battleRoomCubit.getQuestions();

    if (!questions[currentQuestionIndex].attempted) {
      //updated answer locally
      battleRoomCubit
        ..updateQuestionAnswer(
          questions[currentQuestionIndex].id!,
          submittedAnswer,
        )
        ..submitAnswer(
          context.read<UserDetailsCubit>().userId(),
          submittedAnswer,
          isCorrectAnswer: submittedAnswer ==
              AnswerEncryption.decryptCorrectAnswer(
                rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
                correctAnswer: questions[currentQuestionIndex].correctAnswer!,
              ),
        );

      //change question
      await Future<void>.delayed(
        const Duration(seconds: inBetweenQuestionTimeInSeconds),
      );
      if (currentQuestionIndex == (questions.length - 1)) {
        setState(() {
          showWaitForOthers = true;
        });
      } else {
        changeQuestion();
        await timerAnimationController.forward(from: 0);
      }
    }
  }

  //next question
  void changeQuestion() {
    questionAnimationController.forward(from: 0).then((value) {
      //need to dispose the animation controllers
      questionAnimationController.dispose();
      questionContentAnimationController.dispose();
      //initializeAnimation again
      setState(() {
        initializeAnimation();
        currentQuestionIndex++;
      });
      //load content(options, image etc) of question
      questionContentAnimationController.forward();
    });
  }

  //if user has submitted the answer for current question
  bool hasSubmittedAnswerForCurrentQuestion() {
    return context
        .read<MultiUserBattleRoomCubit>()
        .getQuestions()[currentQuestionIndex]
        .attempted;
  }

  void battleRoomListener(
    BuildContext context,
    MultiUserBattleRoomState state,
    MultiUserBattleRoomCubit battleRoomCubit,
  ) {
    Future.delayed(Duration.zero, () async {
      if (await InternetConnectivity.isUserOffline()) {
        await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            shadowColor: Colors.transparent,
            actions: [
              TextButton(
                onPressed: () async {
                  if (!await InternetConnectivity.isUserOffline()) {
                    Navigator.of(context).pop(true);
                  }
                },
                child: Text(
                  context.tr('retryLbl')!,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
            content: Text(
              context.tr('noInternet')!,
            ),
          ),
        );
      }
    });

    if (state is MultiUserBattleRoomSuccess) {
      //show result only for more than two user
      if (battleRoomCubit.getUsers().length != 1) {
        //if there is more than one user in room
        //navigate to result
        navigateToResultScreen(
          battleRoomCubit.getUsers(),
          state.battleRoom,
          state.questions,
        );
      }
    }
  }

  void setCurrentUserMessageDisappearTimer() {
    if (currentUserMessageDisappearTimeInSeconds != 4) {
      currentUserMessageDisappearTimeInSeconds = 4;
    }

    currentUserMessageDisappearTimer =
        Timer.periodic(const Duration(seconds: 1), (timer) {
      if (currentUserMessageDisappearTimeInSeconds == 0) {
        //
        timer.cancel();
        messageAnimationController.reverse();
      } else {
        currentUserMessageDisappearTimeInSeconds--;
      }
    });
  }

  void setOpponentUserMessageDisappearTimer(int opponentUserIndex) {
    //
    if (opponentsMessageDisappearTimeInSeconds[opponentUserIndex] != 4) {
      opponentsMessageDisappearTimeInSeconds[opponentUserIndex] = 4;
    }

    opponentsMessageDisappearTimer[opponentUserIndex] =
        Timer.periodic(const Duration(seconds: 1), (timer) {
      if (opponentsMessageDisappearTimeInSeconds[opponentUserIndex] == 0) {
        //
        timer.cancel();
        opponentMessageAnimationControllers[opponentUserIndex].reverse();
      } else {
        //
        opponentsMessageDisappearTimeInSeconds[opponentUserIndex] =
            opponentsMessageDisappearTimeInSeconds[opponentUserIndex] - 1;
      }
    });
  }

  Future<void> messagesListener(MessageState state) async {
    if (!mounted) return; // تحقق من أن الـ widget لا يزال موجودًا

    if (state is MessageFetchedSuccess) {
      if (state.messages.isNotEmpty) {
        //current user message
        try {
          // التحقق من وجود الرسائل للمستخدم الحالي
          if (latestMessagesByUsers
                  .isNotEmpty && // تحقق من أن القائمة ليست فارغة
              context
                  .read<MessageCubit>()
                  .getUserLatestMessage(
                    //fetch user id
                    context.read<UserDetailsCubit>().userId(),
                    messageId: latestMessagesByUsers[0].messageId,
                    //latest user message id
                  )
                  .messageId
                  .isNotEmpty) {
            //Assign latest message
            latestMessagesByUsers[0] =
                context.read<MessageCubit>().getUserLatestMessage(
                      context.read<UserDetailsCubit>().userId(),
                      messageId: latestMessagesByUsers[0].messageId,
                    );

            //Display latest message by current user
            //means timer is running
            if (currentUserMessageDisappearTimeInSeconds > 0 &&
                currentUserMessageDisappearTimeInSeconds < 4) {
              currentUserMessageDisappearTimer?.cancel();
              setCurrentUserMessageDisappearTimer();
            } else {
              await messageAnimationController.forward();
              setCurrentUserMessageDisappearTimer();
            }
          }

          //display opponent user messages
          final opponentUsers = context
              .read<MultiUserBattleRoomCubit>()
              .getOpponentUsers(context.read<UserDetailsCubit>().userId());

          // التحقق من أن لدينا رسائل كافية للمستخدمين المنافسين
          while (latestMessagesByUsers.length <= opponentUsers.length) {
            latestMessagesByUsers.add(Message.empty());
          }

          // التحقق من أن لدينا مؤقتات كافية للمستخدمين المنافسين
          while (opponentsMessageDisappearTimer.length < opponentUsers.length) {
            opponentsMessageDisappearTimer.add(null);
          }

          // التحقق من أن لدينا عدادات كافية للمستخدمين المنافسين
          while (opponentsMessageDisappearTimeInSeconds.length <
              opponentUsers.length) {
            opponentsMessageDisappearTimeInSeconds.add(4);
          }

          for (var i = 0; i < opponentUsers.length; i++) {
            if (i <
                    opponentMessageAnimationControllers
                        .length && // تحقق من أن المؤشر ضمن النطاق
                context
                    .read<MessageCubit>()
                    .getUserLatestMessage(
                      //opponent user id
                      opponentUsers[i]!.uid,
                      messageId: latestMessagesByUsers[i + 1].messageId,
                      //latest user message id
                    )
                    .messageId
                    .isNotEmpty) {
              //Assign latest message
              latestMessagesByUsers[i + 1] =
                  context.read<MessageCubit>().getUserLatestMessage(
                        context.read<UserDetailsCubit>().userId(),
                        messageId: latestMessagesByUsers[i + 1].messageId,
                      );

              //if new message by opponent
              if (opponentsMessageDisappearTimeInSeconds[i] > 0 &&
                  opponentsMessageDisappearTimeInSeconds[i] < 4) {
                //
                opponentsMessageDisappearTimer[i]?.cancel();
                setOpponentUserMessageDisappearTimer(i);
              } else {
                await opponentMessageAnimationControllers[i].forward();
                setOpponentUserMessageDisappearTimer(i);
              }
            }
          }
        } catch (e) {
          debugPrint("خطأ في معالجة الرسائل: $e");
        }
      }
    }
  }

  void navigateToResultScreen(
    List<UserBattleRoomDetails?> users,
    BattleRoom? battleRoom,
    List<Question>? questions,
  ) {
    var navigateToResult = true;

    if (users.isEmpty) {
      return;
    }

    //checking if every user has given all question's answer
    for (final user in users) {
      //if user uid is not empty means user has not left the game so
      //we will check for it's answer completion
      if (user!.uid.isNotEmpty) {
        //if every user has submitted the answer then move user to result screen
        if (user.answers.length != questions!.length) {
          navigateToResult = false;
        }
      }
    }

    //if all users has submitted the answer
    if (navigateToResult) {
      //giving delay
      Future.delayed(const Duration(milliseconds: 1000), () {
        try {
          //delete battle room by creator of this room
          if (battleRoom!.user1!.uid ==
              context.read<UserDetailsCubit>().userId()) {
            context
                .read<MultiUserBattleRoomCubit>()
                .deleteMultiUserBattleRoom();
          }
          deleteMessages(context.read<MultiUserBattleRoomCubit>());

          //
          //navigating result screen twice...
          //Find optimize solution of navigating to result screen
          //https://stackoverflow.com/questions/56519093/bloc-listen-callback-called-multiple-times try this solution
          //https: //stackoverflow.com/questions/52249578/how-to-deal-with-unwanted-widget-build
          //tried with mounted is true but not working as expected
          //so executing this code in try catch
          //

          if (isSettingDialogOpen) {
            Navigator.of(context).pop();
          }
          if (isExitDialogOpen) {
            Navigator.of(context).pop();
          }

          Navigator.pushReplacementNamed(
            context,
            Routes.multiUserBattleRoomQuizResult,
            arguments: {
              'user': context.read<MultiUserBattleRoomCubit>().getUsers(),
              'entryFee': battleRoom.entryFee,
              'totalQuestions': context
                  .read<MultiUserBattleRoomCubit>()
                  .getQuestions()
                  .length,
            },
          );
        } catch (e) {
          rethrow;
        }
      });
    }
  }

  // بناء شبكة المستخدمين المحسنة - توزيع في زوايا الشاشة
  Widget _buildUsersGrid(
      BuildContext context, MultiUserBattleRoomCubit battleRoomCubit) {
    return BlocBuilder<MultiUserBattleRoomCubit, MultiUserBattleRoomState>(
      bloc: battleRoomCubit,
      builder: (context, state) {
        if (state is MultiUserBattleRoomSuccess) {
          final currentUserId = context.read<UserDetailsCubit>().userId();
          final currentUser = battleRoomCubit.getUser(currentUserId);
          final opponentUsers = battleRoomCubit.getOpponentUsers(currentUserId);

          if (currentUser == null) {
            return const SizedBox();
          }

          // تحضير قائمة المستخدمين
          final List<UserBattleRoomDetails?> topUsers = [];
          final List<UserBattleRoomDetails?> bottomUsers = [];

          // إضافة المستخدم الحالي دائمًا في الأعلى
          topUsers.add(currentUser);

          // إضافة المنافس الأول في الأعلى إذا وجد
          if (opponentUsers.isNotEmpty) {
            topUsers.add(opponentUsers[0]);
          } else {
            // إضافة مستخدم فارغ للحفاظ على التوازن
            topUsers.add(null);
          }

          // إضافة المنافسين الآخرين في الأسفل
          if (opponentUsers.length > 1) {
            bottomUsers.add(opponentUsers[1]);
          } else {
            // إضافة مستخدم فارغ للحفاظ على التوازن
            bottomUsers.add(null);
          }

          if (opponentUsers.length > 2) {
            bottomUsers.add(opponentUsers[2]);
          } else {
            // إضافة مستخدم فارغ للحفاظ على التوازن
            bottomUsers.add(null);
          }

          // استخدام LayoutBuilder للحصول على أبعاد الشاشة المتاحة
          return LayoutBuilder(
            builder: (context, constraints) {
              // الحصول على عرض وارتفاع الشاشة المتاحة
              final availableWidth = constraints.maxWidth;
              final availableHeight = constraints.maxHeight;

              // تحديد حجم البطاقة بنسبة من عرض الشاشة (أصغر من السابق)
              final cardWidth = availableWidth * 0.35; // 35% من عرض الشاشة

              return SizedBox(
                width: availableWidth,
                height: availableHeight,
                child: Stack(
                  children: [
                    // صف المستخدمين العلوي - موزعين على الزوايا العلوية
                    Positioned(
                      top: 10,
                      left: 10,
                      child: SizedBox(
                        width: cardWidth,
                        child: topUsers[0] == null
                            ? _buildEmptyUserCard() // بطاقة فارغة للمستخدم غير الموجود
                            : _buildUserProfileCard(
                                context: context,
                                name: topUsers[0]!.name,
                                profileUrl: topUsers[0]!.profileUrl,
                                correctAnswers:
                                    topUsers[0]!.correctAnswers.toString(),
                                totalQuestions:
                                    state.questions.length.toString(),
                                isMuted: topUsers[0]!.uid == currentUserId
                                    ? BattleVoiceChatProvider.of(context)!
                                        .controller
                                        .isMicMuted
                                    : BattleVoiceChatProvider.of(context)!
                                        .controller
                                        .isRemoteMicMuted(
                                            int.tryParse(topUsers[0]!.uid) ??
                                                0),
                                isCurrentUser:
                                    topUsers[0]!.uid == currentUserId,
                                onMuteToggle:
                                    (state.battleRoom.isVoiceChatEnabled ==
                                                true &&
                                            state.battleRoom.voiceChannelId !=
                                                null &&
                                            state.battleRoom.voiceChannelId!
                                                .isNotEmpty)
                                        ? (topUsers[0]!.uid == currentUserId
                                            ? () {
                                                setState(() {
                                                  BattleVoiceChatProvider.of(
                                                          context)!
                                                      .controller
                                                      .toggleMute();
                                                });
                                              }
                                            : null)
                                        : null,
                              ),
                      ),
                    ),

                    // المستخدم الثاني في الزاوية العلوية اليمنى
                    Positioned(
                      top: 10,
                      right: 10,
                      child: SizedBox(
                        width: cardWidth,
                        child: topUsers[1] == null
                            ? _buildEmptyUserCard() // بطاقة فارغة للمستخدم غير الموجود
                            : _buildUserProfileCard(
                                context: context,
                                name: topUsers[1]!.name,
                                profileUrl: topUsers[1]!.profileUrl,
                                correctAnswers:
                                    topUsers[1]!.correctAnswers.toString(),
                                totalQuestions:
                                    state.questions.length.toString(),
                                isMuted: topUsers[1]!.uid == currentUserId
                                    ? BattleVoiceChatProvider.of(context)!
                                        .controller
                                        .isMicMuted
                                    : BattleVoiceChatProvider.of(context)!
                                        .controller
                                        .isRemoteMicMuted(
                                            int.tryParse(topUsers[1]!.uid) ??
                                                0),
                                isCurrentUser:
                                    topUsers[1]!.uid == currentUserId,
                                onMuteToggle:
                                    (state.battleRoom.isVoiceChatEnabled ==
                                                true &&
                                            state.battleRoom.voiceChannelId !=
                                                null &&
                                            state.battleRoom.voiceChannelId!
                                                .isNotEmpty)
                                        ? (topUsers[1]!.uid == currentUserId
                                            ? () {
                                                setState(() {
                                                  BattleVoiceChatProvider.of(
                                                          context)!
                                                      .controller
                                                      .toggleMute();
                                                });
                                              }
                                            : null)
                                        : null,
                              ),
                      ),
                    ),

                    // المستخدم الثالث في الزاوية السفلية اليسرى
                    Positioned(
                      bottom:
                          10, // تقليل المسافة من الأسفل ليكون أقرب إلى أسفل الشاشة
                      left: 10,
                      child: SizedBox(
                        width: cardWidth,
                        child: bottomUsers[0] == null
                            ? _buildEmptyUserCard() // بطاقة فارغة للمستخدم غير الموجود
                            : _buildUserProfileCard(
                                context: context,
                                name: bottomUsers[0]!.name,
                                profileUrl: bottomUsers[0]!.profileUrl,
                                correctAnswers:
                                    bottomUsers[0]!.correctAnswers.toString(),
                                totalQuestions:
                                    state.questions.length.toString(),
                                isMuted: BattleVoiceChatProvider.of(context)!
                                    .controller
                                    .isRemoteMicMuted(
                                        int.tryParse(bottomUsers[0]!.uid) ?? 0),
                                isCurrentUser: false,
                                onMuteToggle:
                                    (state.battleRoom.isVoiceChatEnabled ==
                                                true &&
                                            state.battleRoom.voiceChannelId !=
                                                null &&
                                            state.battleRoom.voiceChannelId!
                                                .isNotEmpty)
                                        ? null
                                        : null,
                              ),
                      ),
                    ),

                    // المستخدم الرابع في الزاوية السفلية اليمنى
                    Positioned(
                      bottom:
                          10, // تقليل المسافة من الأسفل ليكون أقرب إلى أسفل الشاشة
                      right: 10,
                      child: SizedBox(
                        width: cardWidth,
                        child: bottomUsers[1] == null
                            ? _buildEmptyUserCard() // بطاقة فارغة للمستخدم غير الموجود
                            : _buildUserProfileCard(
                                context: context,
                                name: bottomUsers[1]!.name,
                                profileUrl: bottomUsers[1]!.profileUrl,
                                correctAnswers:
                                    bottomUsers[1]!.correctAnswers.toString(),
                                totalQuestions:
                                    state.questions.length.toString(),
                                isMuted: BattleVoiceChatProvider.of(context)!
                                    .controller
                                    .isRemoteMicMuted(
                                        int.tryParse(bottomUsers[1]!.uid) ?? 0),
                                isCurrentUser: false,
                                onMuteToggle:
                                    (state.battleRoom.isVoiceChatEnabled ==
                                                true &&
                                            state.battleRoom.voiceChannelId !=
                                                null &&
                                            state.battleRoom.voiceChannelId!
                                                .isNotEmpty)
                                        ? null
                                        : null,
                              ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        }
        return const SizedBox();
      },
    );
  }

  // بطاقة فارغة للمستخدم غير الموجود - أصغر وأكثر ملاءمة
  Widget _buildEmptyUserCard() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // صف الصورة الشخصية
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // صورة المستخدم الفارغة
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.grey.withOpacity(0.2),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.1),
                    width: 1,
                  ),
                ),
                width: 32,
                height: 32,
                child: Icon(
                  Icons.person,
                  color: Colors.grey.withOpacity(0.5),
                  size: 16,
                ),
              ),

              // مساحة فارغة بدلاً من زر كتم الصوت
              const SizedBox(width: 24),
            ],
          ),

          const SizedBox(height: 4),

          // نص "في انتظار المنافس"
          Text(
            "في انتظار المنافس",
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.withOpacity(0.7),
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 2),

          // مؤشر الإجابات الفارغ
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              "0/0",
              style: TextStyle(
                fontSize: 9,
                color: Colors.grey.withOpacity(0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYouWonContainer(MultiUserBattleRoomCubit battleRoomCubit) {
    return BlocBuilder<MultiUserBattleRoomCubit, MultiUserBattleRoomState>(
      bloc: battleRoomCubit,
      builder: (context, state) {
        if (state is MultiUserBattleRoomSuccess) {
          if (battleRoomCubit.getUsers().length == 1 &&
              state.battleRoom.user1!.uid ==
                  context.read<UserDetailsCubit>().userId()) {
            timerAnimationController.stop();
            return Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              color: Theme.of(context).colorScheme.surface.withOpacity(0.1),
              alignment: Alignment.center,
              child: AlertDialog(
                shadowColor: Colors.transparent,
                title: Text(
                  context.tr('youWonLbl')!,
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
                content: Text(
                  context.tr('everyOneLeftLbl')!,
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      //delete messages
                      deleteMessages(context.read<MultiUserBattleRoomCubit>());

                      //add coins locally

                      context.read<UserDetailsCubit>().updateCoins(
                            addCoin: true,
                            coins: battleRoomCubit.getEntryFee() * userLength,
                          );
                      //add coins in database

                      context.read<UpdateScoreAndCoinsCubit>().updateCoins(
                            coins: battleRoomCubit.getEntryFee() * userLength,
                            addCoin: true,
                            title: wonGroupBattleKey,
                          );

                      //delete room
                      battleRoomCubit.deleteMultiUserBattleRoom();
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      context.tr('okayLbl')!,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
        }
        return const SizedBox();
      },
    );
  }

  Widget _buildUserLeftTheGame() {
    //cancel timer when user left the game
    if (showUserLeftTheGame) {
      return Container(
        color: Theme.of(context).colorScheme.surface.withOpacity(0.1),
        alignment: Alignment.center,
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: AlertDialog(
          shadowColor: Colors.transparent,
          content: Text(
            context.tr('youLeftLbl')!,
            style: TextStyle(color: Theme.of(context).primaryColor),
          ),
          actions: [
            TextButton(
              onPressed: Navigator.of(context).pop,
              child: Text(
                context.tr('okayLbl')!,
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox();
  }

  // تم حذف دالة _buildVoiceChatControls لأنها غير مستخدمة

  // تم حذف الدوال غير المستخدمة

  // تم إزالة دالة _buildCurrentUserDetails غير المستخدمة

  // تم إزالة دالة _buildOpponentUserDetails غير المستخدمة

  Widget _buildMessageButton() {
    return AnimatedBuilder(
      animation: messageBoxAnimationController,
      builder: (context, child) {
        return InkWell(
          onTap: () {
            if (messageBoxAnimationController.isCompleted) {
              messageBoxAnimationController.reverse();
            } else {
              messageBoxAnimationController.forward();
            }
          },
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(5),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 4.5, vertical: 4),
            child: Icon(
              CupertinoIcons.ellipses_bubble_fill,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
          ),
        );
      },
    );
  }

  Widget _buildMessageBoxContainer() {
    return Align(
      alignment: Alignment.topCenter,
      child: SlideTransition(
        position: messageBoxAnimation.drive(
          Tween<Offset>(begin: const Offset(1.5, 0), end: Offset.zero),
        ),
        child: MessageBoxContainer(
          quizType: QuizTypes.groupPlay,
          battleRoomId: context.read<MultiUserBattleRoomCubit>().getRoomId(),
          topPadding: MediaQuery.of(context).padding.top,
          closeMessageBox: () {
            messageBoxAnimationController.reverse();
          },
        ),
      ),
    );
  }

  // تم إزالة دالة _buildCurrentUserMessageContainer غير المستخدمة

  // تم إزالة دالة _buildOpponentUserMessageContainer غير المستخدمة

  @override
  Widget build(BuildContext context) {
    final battleRoomCubit = context.read<MultiUserBattleRoomCubit>();

    return PopScope(
        canPop: showUserLeftTheGame,
        onPopInvokedWithResult: (didPop, _) {
          if (didPop) return;

          // Close Message Box Before
          if (messageBoxAnimationController.isCompleted) {
            messageBoxAnimationController.reverse();
            return;
          }

          isExitDialogOpen = true;
          showDialog<void>(
            context: context,
            builder: (_) => ExitGameDialog(
              onTapYes: () {
                if (battleRoomCubit.getUsers().length == 1) {
                  battleRoomCubit.deleteMultiUserBattleRoom();
                } else {
                  //delete user from game room
                  battleRoomCubit.deleteUserFromRoom(
                    context.read<UserDetailsCubit>().userId(),
                  );
                }
                deleteMessages(battleRoomCubit);
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
            ),
          ).then((value) => isExitDialogOpen = true);
        },
        child: BattleVoiceChatProvider(
            controller: _voiceChatController,
            child: Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.arrow_back_ios_new,
                        color: Colors.white, size: 18),
                  ),
                  onPressed: () {
                    final battleRoomCubit =
                        context.read<MultiUserBattleRoomCubit>();

                    //if user hasleft the game
                    if (showUserLeftTheGame) {
                      Navigator.of(context).pop();
                    }
                    //
                    if (battleRoomCubit.getUsers().length == 1 &&
                        battleRoomCubit.getUsers().first!.uid ==
                            context.read<UserDetailsCubit>().userId()) {
                      return;
                    }

                    //if user is playing game then show
                    //exit game dialog

                    isExitDialogOpen = true;
                    showDialog<void>(
                      context: context,
                      builder: (_) => ExitGameDialog(
                        onTapYes: () {
                          if (battleRoomCubit.getUsers().length == 1) {
                            battleRoomCubit.deleteMultiUserBattleRoom();
                          } else {
                            //delete user from game room
                            battleRoomCubit.deleteUserFromRoom(
                              context.read<UserDetailsCubit>().userId(),
                            );
                          }
                          deleteMessages(battleRoomCubit);
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                        },
                      ),
                    ).then((value) => isExitDialogOpen = true);
                  },
                ),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 16.0),
                    child: _buildMessageButton(),
                  ),
                ],
              ),
              body: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withOpacity(0.8),
                      Theme.of(context).primaryColor.withOpacity(0.6),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: MultiBlocListener(
                  listeners: [
                    //update ui and do other callback based on changes in MultiUserBattleRoomCubit
                    BlocListener<MultiUserBattleRoomCubit,
                        MultiUserBattleRoomState>(
                      bloc: battleRoomCubit,
                      listener: (context, state) {
                        battleRoomListener(context, state, battleRoomCubit);
                      },
                    ),
                    BlocListener<MessageCubit, MessageState>(
                      bloc: context.read<MessageCubit>(),
                      listener: (context, state) {
                        //this listener will be call everytime when new message will add
                        messagesListener(state);
                      },
                    ),
                    BlocListener<UpdateScoreAndCoinsCubit,
                        UpdateScoreAndCoinsState>(
                      listener: (context, state) {
                        if (state is UpdateScoreAndCoinsFailure) {
                          if (state.errorMessage ==
                              errorCodeUnauthorizedAccess) {
                            timerAnimationController.stop();
                            showAlreadyLoggedInDialog(context);
                          }
                        }
                      },
                    ),
                  ],
                  child: SafeArea(
                    child: Stack(
                      children: [
                        // الخلفية المزخرفة
                        Positioned.fill(
                          child: CustomPaint(
                            painter: BattleBackgroundPainter(
                              color: Colors.white.withOpacity(0.05),
                            ),
                          ),
                        ),

                        // شبكة المستخدمين في الزوايا
                        Positioned.fill(
                          child: _buildUsersGrid(context, battleRoomCubit),
                        ),
                        // حاوية الأسئلة - في المنتصف تمامًا
                        Positioned(
                          top: 100, // تقليل المسافة من الأعلى
                          bottom:
                              150, // زيادة المسافة من الأسفل لترك مساحة للمستخدمين وأزرار التحكم
                          left: 0,
                          right: 0,
                          child: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 500),
                            child: showWaitForOthers
                                ? const WaitForOthersContainer(
                                    key: Key('waitForOthers'),
                                  )
                                : BlocBuilder<MultiUserBattleRoomCubit,
                                    MultiUserBattleRoomState>(
                                    bloc: battleRoomCubit,
                                    builder: (context, state) {
                                      return Container(
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 16),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(24),
                                          boxShadow: [
                                            BoxShadow(
                                              color:
                                                  Colors.black.withOpacity(0.1),
                                              blurRadius: 20,
                                              offset: const Offset(0, 5),
                                            ),
                                          ],
                                        ),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(24),
                                          child: QuestionsContainer(
                                            topPadding:
                                                0, // إزالة المساحة العلوية داخل الحاوية
                                            timerAnimationController:
                                                timerAnimationController,
                                            quizType: QuizTypes.groupPlay,
                                            answerMode: context
                                                .read<SystemConfigCubit>()
                                                .answerMode,
                                            lifeLines: const {},
                                            key: const Key('questions'),
                                            hasSubmittedAnswerForCurrentQuestion:
                                                hasSubmittedAnswerForCurrentQuestion,
                                            questions:
                                                battleRoomCubit.getQuestions(),
                                            submitAnswer: submitAnswer,
                                            questionContentAnimation:
                                                questionContentAnimation,
                                            questionScaleDownAnimation:
                                                questionScaleDownAnimation,
                                            questionScaleUpAnimation:
                                                questionScaleUpAnimation,
                                            questionSlideAnimation:
                                                questionSlideAnimation,
                                            currentQuestionIndex:
                                                currentQuestionIndex,
                                            questionAnimationController:
                                                questionAnimationController,
                                            questionContentAnimationController:
                                                questionContentAnimationController,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                          ),
                        ),
                        _buildMessageBoxContainer(),
                        if (showUserLeftTheGame) ...[
                          // لا نعرض أي شيء إذا غادر المستخدم اللعبة
                        ],
                        // تم إزالة شريط التحكم في المحادثة الصوتية لأنه غير مهم
                        _buildYouWonContainer(battleRoomCubit),
                        _buildUserLeftTheGame(),
                      ],
                    ),
                  ),
                ),
              ),
            )));
  }
}

/// رسام مستوى الصوت المخصص
/// يرسم أشرطة مستوى الصوت
class VoiceLevelPainter extends CustomPainter {
  final double level;
  final Color color;
  final int barCount;
  final double barWidth;
  final double spacing;

  VoiceLevelPainter({
    required this.level,
    required this.color,
    this.barCount = 4,
    this.barWidth = 2,
    this.spacing = 1,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final totalWidth = (barWidth + spacing) * barCount - spacing;
    final startX = (size.width - totalWidth) / 2;
    final maxHeight = size.height * 0.8;

    for (int i = 0; i < barCount; i++) {
      final barHeight = maxHeight * ((i + 1) / barCount) * level;
      final barX = startX + i * (barWidth + spacing);
      final barY = (size.height - barHeight) / 2;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(barX, barY, barWidth, barHeight),
          const Radius.circular(1),
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(VoiceLevelPainter oldDelegate) {
    return oldDelegate.level != level || oldDelegate.color != color;
  }
}
