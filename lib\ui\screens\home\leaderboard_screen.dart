import 'dart:async';
import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardAllTimeCubit.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardDailyCubit.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardMonthlyCubit.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/customAppbar.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:iconsax_plus/iconsax_plus.dart';

class LeaderBoardScreen extends StatefulWidget {
  const LeaderBoardScreen({this.fromNav = false, super.key});

  final bool fromNav;

  @override
  State<LeaderBoardScreen> createState() => _LeaderBoardScreen();

  static Route<LeaderBoardScreen> route() {
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<LeaderBoardMonthlyCubit>(
            create: (_) => LeaderBoardMonthlyCubit(),
          ),
          BlocProvider<LeaderBoardDailyCubit>(
            create: (_) => LeaderBoardDailyCubit(),
          ),
          BlocProvider<LeaderBoardAllTimeCubit>(
            create: (_) => LeaderBoardAllTimeCubit(),
          ),
        ],
        child: const LeaderBoardScreen(),
      ),
    );
  }
}

class _LeaderBoardScreen extends State<LeaderBoardScreen> with SingleTickerProviderStateMixin {
  final controllerM = ScrollController();
  final controllerA = ScrollController();
  final controllerD = ScrollController();
  late AnimationController _crownController;

  @override
  void initState() {
    controllerM.addListener(scrollListenerM);
    controllerA.addListener(scrollListenerA);
    controllerD.addListener(scrollListenerD);

    _crownController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    Future.delayed(
      Duration.zero,
      () {
        context.read<LeaderBoardDailyCubit>().fetchLeaderBoard('20');
        context.read<LeaderBoardMonthlyCubit>().fetchLeaderBoard('20');
        context.read<LeaderBoardAllTimeCubit>().fetchLeaderBoard('20');
      },
    );
    super.initState();
  }

  @override
  void dispose() {
    controllerM.removeListener(scrollListenerM);
    controllerA.removeListener(scrollListenerA);
    controllerD.removeListener(scrollListenerD);
    _crownController.dispose();
    super.dispose();
  }

  void scrollListenerM() {
    if (controllerM.position.maxScrollExtent == controllerM.offset) {
      if (context.read<LeaderBoardMonthlyCubit>().hasMoreData()) {
        context.read<LeaderBoardMonthlyCubit>().fetchMoreLeaderBoardData('20');
      }
    }
  }

  void scrollListenerA() {
    if (controllerA.position.maxScrollExtent == controllerA.offset) {
      if (context.read<LeaderBoardAllTimeCubit>().hasMoreData()) {
        context.read<LeaderBoardAllTimeCubit>().fetchMoreLeaderBoardData('20');
      }
    }
  }

  void scrollListenerD() {
    if (controllerD.position.maxScrollExtent == controllerD.offset) {
      if (context.read<LeaderBoardDailyCubit>().hasMoreData()) {
        context.read<LeaderBoardDailyCubit>().fetchMoreLeaderBoardData('20');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: QAppBar(
          automaticallyImplyLeading: !widget.fromNav,
          elevation: 0,
          title: Text(
            context.tr('leaderboardLbl')!,
          ),
          bottom: TabBar(
            tabAlignment: TabAlignment.fill,
            tabs: [
              Tab(
                text: context.tr('allTimeLbl'),
              ),
              Tab(
                text: context.tr('monthLbl'),
              ),
              Tab(
                text: context.tr('dailyLbl'),
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            allTimeLeaderBoard(),
            monthlyLeaderBoard(),
            dailyLeaderBoard(),
          ],
        ),
      ),
    );
  }

  void fetchMonthlyLeaderBoard() =>
      context.read<LeaderBoardMonthlyCubit>().fetchLeaderBoard('20');

  void fetchDailyLeaderBoard() =>
      context.read<LeaderBoardDailyCubit>().fetchLeaderBoard('20');

  void fetchAllTimeLeaderBoard() =>
      context.read<LeaderBoardAllTimeCubit>().fetchLeaderBoard('20');

  Widget noLeaderboard(VoidCallback onTapRetry) => Center(
        child: ErrorContainer(
          topMargin: 0,
          errorMessage: 'noLeaderboardLbl',
          onTapRetry: onTapRetry,
          showErrorImage: false,
        ),
      );

  Widget dailyLeaderBoard() {
    return BlocConsumer<LeaderBoardDailyCubit, LeaderBoardDailyState>(
      bloc: context.read<LeaderBoardDailyCubit>(),
      listener: (context, state) {
        if (state is LeaderBoardDailyFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);

            return;
          }
        }
      },
      builder: (context, state) {
        if (state is LeaderBoardDailyFailure) {
          return ErrorContainer(
            showBackButton: false,
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
            onTapRetry: fetchDailyLeaderBoard,
            showErrorImage: true,
            errorMessageColor: Theme.of(context).primaryColor,
          );
        }

        ///
        if (state is LeaderBoardDailySuccess) {
          final dailyList = state.leaderBoardDetails;
          final hasMore = state.hasMore;

          /// API returns empty list if there is no leaderboard data.
          if (dailyList.isEmpty) {
            return noLeaderboard(fetchDailyLeaderBoard);
          }


          return SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            child: Column(
              children: [
                topThreeRanks(dailyList),
                leaderBoardList(dailyList, controllerD, hasMore: hasMore),
                if (LeaderBoardDailyCubit.scoreD.isNotEmpty &&
                    LeaderBoardDailyCubit.scoreD != '0' &&
                    int.parse(LeaderBoardDailyCubit.rankD) > 3)
                  myRank(
                    LeaderBoardDailyCubit.rankD,
                    LeaderBoardDailyCubit.profileD,
                    LeaderBoardDailyCubit.scoreD,
                  ),
              ],
            ),
          );
        }

        return const Center(child: CircularProgressContainer());
      },
    );
  }

  Widget monthlyLeaderBoard() {
    return BlocConsumer<LeaderBoardMonthlyCubit, LeaderBoardMonthlyState>(
      bloc: context.read<LeaderBoardMonthlyCubit>(),
      listener: (context, state) {
        if (state is LeaderBoardMonthlyFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);

            return;
          }
        }
      },
      builder: (context, state) {
        if (state is LeaderBoardMonthlyFailure) {
          return ErrorContainer(
            showBackButton: false,
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
            onTapRetry: fetchMonthlyLeaderBoard,
            showErrorImage: true,
            errorMessageColor: Theme.of(context).primaryColor,
          );
        }

        ///
        if (state is LeaderBoardMonthlySuccess) {
          final monthlyList = state.leaderBoardDetails;
          final hasMore = state.hasMore;

          /// API returns empty list if there is no leaderboard data.
          if (monthlyList.isEmpty) {
            return noLeaderboard(fetchMonthlyLeaderBoard);
          }


          return SizedBox(
            height: MediaQuery.of(context).size.height * .8,
            child: Column(
              children: [
                topThreeRanks(monthlyList),
                leaderBoardList(monthlyList, controllerM, hasMore: hasMore),
                if (LeaderBoardMonthlyCubit.scoreM.isNotEmpty &&
                    LeaderBoardMonthlyCubit.scoreM != '0' &&
                    int.parse(LeaderBoardMonthlyCubit.rankM) > 3)
                  myRank(
                    LeaderBoardMonthlyCubit.rankM,
                    LeaderBoardMonthlyCubit.profileM,
                    LeaderBoardMonthlyCubit.scoreM,
                  ),
              ],
            ),
          );
        }

        return const Center(child: CircularProgressContainer());
      },
    );
  }

  Widget allTimeLeaderBoard() {
    return BlocConsumer<LeaderBoardAllTimeCubit, LeaderBoardAllTimeState>(
      bloc: context.read<LeaderBoardAllTimeCubit>(),
      listener: (context, state) {
        if (state is LeaderBoardAllTimeFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      builder: (context, state) {
        if (state is LeaderBoardAllTimeFailure) {
          return ErrorContainer(
            showBackButton: false,
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
            onTapRetry: fetchAllTimeLeaderBoard,
            showErrorImage: true,
            errorMessageColor: Theme.of(context).primaryColor,
          );
        }

        ///
        if (state is LeaderBoardAllTimeSuccess) {
          final allTimeList = state.leaderBoardDetails;
          final hasMore = state.hasMore;

          /// API returns empty list if there is no leaderboard data.
          if (allTimeList.isEmpty) {
            return noLeaderboard(fetchDailyLeaderBoard);
          }
          return SizedBox(
            height: MediaQuery.of(context).size.height * .6,
            child: Column(
              children: [
                topThreeRanks(allTimeList),
                leaderBoardList(allTimeList, controllerA, hasMore: hasMore),
                if (LeaderBoardAllTimeCubit.scoreA.isNotEmpty &&
                    LeaderBoardAllTimeCubit.scoreA != '0' &&
                    int.parse(LeaderBoardAllTimeCubit.rankA) > 3)
                  myRank(
                    LeaderBoardAllTimeCubit.rankA,
                    LeaderBoardAllTimeCubit.profileA,
                    LeaderBoardAllTimeCubit.scoreA,
                  ),
              ],
            ),
          );
        }

        return const Center(child: CircularProgressContainer());
      },
    );
  }

  Widget topThreeRanks(List<Map<String, dynamic>> circleList) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final colorScheme = Theme.of(context).colorScheme;
    
    // Calculate responsive dimensions
    final containerHeight = size.height * 0.32;
    final topPadding = size.height * 0.02;
    final horizontalPadding = width * 0.03;

    return Container(
      padding: EdgeInsets.only(
        top: topPadding,
        bottom: topPadding,
        left: horizontalPadding,
        right: horizontalPadding,
      ),
      width: width,
      height: containerHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            colorScheme.surface,
            colorScheme.surface.withOpacity(0.9),
          ],
        ),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background decorative elements
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: containerHeight * 0.2,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary.withOpacity(0.1),
                    colorScheme.primary.withOpacity(0.05),
                    Colors.transparent,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (circleList.length > 1)
                      _buildTopPlayer(
                        rank: "2",
                        player: circleList[1],
                        height: containerHeight * 0.75,
                        width: width * 0.24,
                        color: const Color(0xFFC0C0C0),
                        showCrown: false,
                        scale: 0.85,
                      ),
                    if (circleList.isNotEmpty)
                      _buildTopPlayer(
                        rank: "1",
                        player: circleList[0],
                        height: containerHeight * 0.85,
                        width: width * 0.26,
                        color: Colors.amber,
                        showCrown: true,
                        scale: 1.0,
                      ),
                    if (circleList.length > 2)
                      _buildTopPlayer(
                        rank: "3",
                        player: circleList[2],
                        height: containerHeight * 0.7,
                        width: width * 0.24,
                        color: const Color(0xFFCD7F32),
                        showCrown: false,
                        scale: 0.8,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTopPlayer({
    required String rank,
    required Map<String, dynamic> player,
    required double height,
    required double width,
    required Color color,
    required bool showCrown,
    required double scale,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final adjustedHeight = height * 0.95; // Reduce overall height slightly
    
    return Container(
      height: adjustedHeight,
      width: width,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            color.withOpacity(0.2),
            color.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final availableHeight = constraints.maxHeight;
          final crownHeight = showCrown ? availableHeight * 0.15 : 0.0;
          final imageSize = width * 0.7;
          final spacingHeight = availableHeight * 0.02;
          
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (showCrown) 
                SizedBox(
                  height: crownHeight,
                  child: _buildCrownAnimation(),
                ),
              SizedBox(height: spacingHeight),
              SizedBox(
                height: imageSize,
                child: Stack(
                  alignment: Alignment.bottomRight,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(3),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: color,
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: color.withOpacity(0.3),
                            blurRadius: 8,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: QImage.circular(
                        imageUrl: player['profile'] as String,
                        width: imageSize,
                        height: imageSize,
                      ),
                    ),
                    Transform.scale(
                      scale: scale,
                      child: rankCircle(rank, size: 25),
                    ),
                  ],
                ),
              ),
              SizedBox(height: spacingHeight),
              Flexible(
                child: Text(
                  player['name'] as String? ?? '...',
                  style: TextStyle(
                    fontSize: 12 * scale,
                    fontWeight: FontWeights.semiBold,
                    color: colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(height: spacingHeight),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: width * 0.1,
                  vertical: availableHeight * 0.01,
                ),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    UiUtils.formatNumber(
                      int.parse(player['score'] as String? ?? '0'),
                    ),
                    style: TextStyle(
                      fontSize: 14 * scale,
                      fontWeight: FontWeights.bold,
                      color: colorScheme.surface,
                    ),
                  ),
                ),
              ),
              SizedBox(height: spacingHeight),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCrownAnimation() {
    return AnimatedBuilder(
      animation: _crownController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, sin(_crownController.value * pi) * 5),
          child: ShaderMask(
            shaderCallback: (Rect bounds) {
              return const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFFFFD700),
                  Color(0xFFFFA500),
                  Color(0xFFFFD700),
                ],
              ).createShader(bounds);
            },
            child: const Icon(
              IconsaxPlusBold.crown,
              size: 36,
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }

  Widget rankCircle(String text, {double size = 25}) {
    final colorScheme = Theme.of(context).colorScheme;
    final rankColor = text == '1'
        ? Colors.amber
        : text == '2'
            ? const Color(0xFFC0C0C0)
            : const Color(0xFFCD7F32);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        shape: BoxShape.circle,
        border: Border.all(
          color: rankColor,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: rankColor.withOpacity(0.3),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            color: rankColor,
            fontSize: size * 0.5,
            fontWeight: FontWeights.bold,
          ),
        ),
      ),
    );
  }

  Widget leaderBoardList(
    List<Map<String, dynamic>> leaderBoardList,
    ScrollController controller, {
    required bool hasMore,
  }) {
    if (leaderBoardList.length <= 3) return const SizedBox();

    final textStyle = TextStyle(
      color: Theme.of(context).colorScheme.onTertiary,
      fontSize: 16,
    );

    final width = MediaQuery.of(context).size.width;
    final imageSize = width * 0.12; // ثابت لحجم الصورة

    return Expanded(
      child: Container(
        width: width,
        padding: EdgeInsets.only(top: 5, left: width * .02, right: width * .02),
        child: ListView.builder(
          controller: controller,
          shrinkWrap: true,
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: leaderBoardList.length + (hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index < 3) return const SizedBox();
            
            if (hasMore && index == leaderBoardList.length) {
              return const Center(child: CircularProgressContainer());
            }

            final leaderBoard = leaderBoardList[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 5,
              ),
              child: Row(
                children: [
                  Text(
                    leaderBoard['user_rank'] as String,
                    style: textStyle.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: ListTile(
                      dense: true,
                      contentPadding: const EdgeInsets.only(right: 20),
                      title: Text(
                        leaderBoard['name'] as String? ?? '...',
                        overflow: TextOverflow.ellipsis,
                        style: textStyle,
                      ),
                      leading: Container(
                        width: imageSize,
                        height: imageSize, // نفس العرض للحفاظ على الشكل الدائري
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        child: ClipOval(
                          child: QImage.circular(
                            imageUrl: leaderBoard['profile'] as String? ?? '',
                            width: imageSize,
                            height: imageSize,
                          ),
                        ),
                      ),
                      trailing: Container(
                        width: imageSize,
                        height: 35, // ارتفاع ثابت لمربع النقاط
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            UiUtils.formatNumber(
                              int.parse(leaderBoard['score'] as String? ?? '0'),
                            ),
                            maxLines: 1,
                            softWrap: false,
                            style: textStyle.copyWith(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget myRank(String rank, String profile, String score) {
    final colorScheme = Theme.of(context).colorScheme;
    final width = MediaQuery.of(context).size.width;
    final imageSize = width * 0.12; // استخدام نفس حجم الصورة المستخدم في القائمة

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.primary.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.all(10).copyWith(bottom: 0),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: width * 0.03),
        title: Row(
          children: [
            Center(
              child: Text(
                rank,
                style: TextStyle(
                  color: colorScheme.surface,
                  fontSize: 16.5,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(width: width * .05),
            Container(
              width: imageSize,
              height: imageSize,
              padding: const EdgeInsets.all(3),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                shape: BoxShape.circle,
                border: Border.all(
                  color: colorScheme.surface,
                  width: 2,
                ),
              ),
              child: ClipOval(
                child: QImage.circular(
                  imageUrl: profile,
                  width: imageSize,
                  height: imageSize,
                ),
              ),
            ),
            const SizedBox(width: 10),
            Text(
             context.tr(myRankKey)!,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: colorScheme.surface,
                fontSize: 16.5,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        trailing: Container(
          width: width * .15,
          height: 35, // ارتفاع ثابت لمربع النقاط
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              UiUtils.formatNumber(int.tryParse(score) ?? 0),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Shimmer effect widget for profile images
class ShimmerEffect extends StatefulWidget {
  final Gradient gradient;

  const ShimmerEffect({Key? key, required this.gradient}) : super(key: key);

  @override
  _ShimmerEffectState createState() => _ShimmerEffectState();
}

class _ShimmerEffectState extends State<ShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: widget.gradient,
          ),
          child: Transform.rotate(
            angle: _controller.value * 2 * 3.14,
            child: Container(
              decoration: BoxDecoration(
                gradient: widget.gradient,
              ),
            ),
          ),
        );
      },
    );
  }
}