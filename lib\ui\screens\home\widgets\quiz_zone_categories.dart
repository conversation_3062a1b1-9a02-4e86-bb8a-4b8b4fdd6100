import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/quiz/cubits/quizzone_category_cubit.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/login_dialog.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/ui_utils.dart';

class QuizZoneCategories extends StatelessWidget {
  final bool isGuest;
  
Future<void> _showLoginDialog(BuildContext context) {
  return showLoginDialog(
    context,
    onTapYes: () {
      Navigator.of(context).pop(); // إغلاق مربع الحوار
      Navigator.of(context).pushNamed(Routes.login); // الانتقال إلى صفحة تسجيل الدخول
    },
  );
}
  const QuizZoneCategories({super.key, required this.isGuest});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<QuizoneCategoryCubit, QuizoneCategoryState>(
      bloc: context.read<QuizoneCategoryCubit>(),
      listener: (context, state) {
        if (state is QuizoneCategoryFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      builder: (context, state) {
        if (state is QuizoneCategoryFailure) {
          return ErrorContainer(
            showRTryButton: false,
            showBackButton: false,
            showErrorImage: false,
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
            onTapRetry: () {
              context.read<QuizoneCategoryCubit>().getQuizCategoryWithUserId(
                    languageId: UiUtils.getCurrentQuizLanguageId(context),
                  );
            },
          );
        }

        if (state is QuizoneCategorySuccess) {
          final categories = state.categories;
          
          return SizedBox(
            height: 160, // ارتفاع ثابت للقائمة
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                return CategoryCard(
                  category: category,
                  onTap: () async {
                    if (isGuest) {
                      _showLoginDialog(context);
                      return;
                    }

                    if (category.noOf == '0') {
                      if (category.maxLevel == '0') {
                        Navigator.of(context).pushNamed(
                          Routes.quiz,
                          arguments: {
                            'numberOfPlayer': 1,
                            'quizType': QuizTypes.quizZone,
                            'categoryId': category.id,
                            'subcategoryId': '',
                            'level': '0',
                            'subcategoryMaxLevel': '0',
                            'unlockedLevel': 0,
                            'contestId': '',
                            'comprehensionId': '',
                            'quizName': 'Quiz Zone',
                            'showRetryButton': category.noOfQues! != '0',
                          },
                        );
                      } else {
                        Navigator.of(context).pushNamed(
                          Routes.levels,
                          arguments: {
                            'Category': category,
                          },
                        );
                      }
                    } else {
                      Navigator.of(context).pushNamed(
                        Routes.subcategoryAndLevel,
                        arguments: {
                          'category_id': category.id,
                          'category_name': category.categoryName,
                          'isPremiumCategory': category.isPremium,
                        },
                      );
                    }
                  },
                );
              },
            ),
          );
        }

        return const Center(child: CircularProgressContainer());
      },
    );
  }
}

class CategoryCard extends StatelessWidget {
  final dynamic category;
  final VoidCallback onTap;

  const CategoryCard({
    super.key,
    required this.category,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(right: 15),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.1),
              Theme.of(context).primaryColor.withOpacity(0.2),
            ],
          ),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Stack(
            children: [
              // صورة الخلفية
              Positioned.fill(
                child: QImage(
                  imageUrl: category.image!,
                  fit: BoxFit.cover, padding: EdgeInsets.zero,
                ),
              ),
              
              // تدرج لوني فوق الصورة
              Positioned.fill(
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.8),
                      ],
                    ),
                  ),
                ),
              ),
              
              // المحتوى
              Positioned(
                bottom: 12,
                left: 12,
                right: 12,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.categoryName!,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withOpacity(0.8),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '${category.noOfQues} سؤال',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),

                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
