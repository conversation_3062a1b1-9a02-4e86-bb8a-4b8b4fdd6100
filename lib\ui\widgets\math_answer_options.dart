// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_math_fork/flutter_math.dart';
// import 'package:flutterquiz/features/quiz/models/answerOption.dart';
// import 'package:flutterquiz/features/settings/settingsCubit.dart';
// import 'package:flutterquiz/ui/styles/colors.dart';
// import 'package:just_audio/just_audio.dart';

// class MathAnswerOptions extends StatefulWidget {
//   final List<AnswerOption> answerOptions;
//   final Function(String) onTapOption;
//   final String? submittedAnswerId;
//   final String? correctAnswerId;
//   final bool Function() hasSubmittedAnswerForCurrentQuestion;
//   final bool showAudiencePoll;
//   final BoxConstraints constraints;

//   const MathAnswerOptions({
//     Key? key,
//     required this.answerOptions,
//     required this.onTapOption,
//     required this.submittedAnswerId,
//     required this.correctAnswerId,
//     required this.hasSubmittedAnswerForCurrentQuestion,
//     required this.showAudiencePoll,
//     required this.constraints,
//   }) : super(key: key);

//   @override
//   _MathAnswerOptionsState createState() => _MathAnswerOptionsState();
// }

// class _MathAnswerOptionsState extends State<MathAnswerOptions> {
//   late final AudioPlayer _audioPlayer = AudioPlayer();

//   Color _getOptionBackgroundColor(String optionId) {
//     if (widget.hasSubmittedAnswerForCurrentQuestion()) {
//       if (optionId == widget.correctAnswerId) {
//         return kCorrectAnswerColor;
//       }
//       if (optionId == widget.submittedAnswerId) {
//         return kWrongAnswerColor;
//       }
//     }
//     return Theme.of(context).colorScheme.background;
//   }

//   Color _getOptionBorderColor(String optionId) {
//     if (widget.hasSubmittedAnswerForCurrentQuestion()) {
//       if (optionId == widget.correctAnswerId) {
//         return kCorrectAnswerColor;
//       }
//       if (optionId == widget.submittedAnswerId) {
//         return kWrongAnswerColor;
//       }
//     }
//     return Theme.of(context).colorScheme.onBackground;
//   }

//   Widget _buildMathOption(AnswerOption option, int index) {
//     return GestureDetector(
//       onTap: () {
//         if (!widget.hasSubmittedAnswerForCurrentQuestion()) {
//           widget.onTapOption(option.id!);
//           _audioPlayer.setAsset('assets/sounds/button_click.mp3');
//           _audioPlayer.play();
//         }
//       },
//       child: Container(
//         margin: const EdgeInsets.only(bottom: 12),
//         decoration: BoxDecoration(
//           color: _getOptionBackgroundColor(option.id!),
//           borderRadius: BorderRadius.circular(8),
//           border: Border.all(
//             color: _getOptionBorderColor(option.id!),
//           ),
//         ),
//         padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//         child: Container(
//           width: double.infinity,
//           alignment: Alignment.center,
//           child: Math.tex(
//             option.title!,
//             textStyle: TextStyle(
//               fontSize: context.read<SettingsCubit>().getSettings().playAreaFontSize,
//               color: widget.hasSubmittedAnswerForCurrentQuestion() &&
//                       (option.id! == widget.correctAnswerId ||
//                           option.id! == widget.submittedAnswerId)
//                   ? Colors.white
//                   : Theme.of(context).colorScheme.onBackground,
//             ),
//             mathStyle: MathStyle.text,
//           ),
//         ),
//       ),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: List.generate(
//         widget.answerOptions.length,
//         (index) => _buildMathOption(widget.answerOptions[index], index),
//       ),
//     );
//   }
// }
