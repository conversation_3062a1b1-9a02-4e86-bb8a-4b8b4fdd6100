import 'package:flutter/services.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'subscription_model.dart';

class SubscriptionManager {
  static final SubscriptionManager instance = SubscriptionManager._internal();
  factory SubscriptionManager() => instance;
  SubscriptionManager._internal();

  // معرف الاشتراك الخاص بك من RevenueCat
  static const String entitlementID = 'premium_access';
  static const Map<String, Map<String, String>> productIds = {
    'ios': {
      'weekly': 'weekly_id', // صحيح
      'monthly': 'monthly_id', // صحيح
      'yearly': 'yearly_id', // صحيح
    },
    'android': {
      'weekly': 'premium_access:weekly-auto', // صحيح
      'monthly': 'premium_access:monthly-auto', // صحيح
      'yearly': 'premium_access:yearly-auto', // صحيح
      // 'monthly': 'test:monthlysubscrilption',  // معرف قديم
    }
  };

  bool _isSubscribed = false;

  bool get isSubscribed => _isSubscribed;

  // تهيئة حالة الاشتراك
  Future<void> initialize() async {
    try {
      print('🔄 بدء تهيئة مدير الاشتراكات...');

      // التحقق من تهيئة RevenueCat
      final customerInfo = await Purchases.getCustomerInfo();
      _updateSubscriptionStatus(customerInfo);

      print('✅ تم تهيئة مدير الاشتراكات بنجاح');
    } catch (e) {
      print('❌ خطأ في تهيئة مدير الاشتراكات: $e');

      // إذا كان الخطأ متعلق بعدم تهيئة RevenueCat
      if (e.toString().contains('not been configured')) {
        print('⚠️ RevenueCat لم يتم تهيئته بعد');
      }
    }
  }

  // تحديث حالة الاشتراك
  void _updateSubscriptionStatus(CustomerInfo customerInfo) {
    _isSubscribed =
        customerInfo.entitlements.all[entitlementID]?.isActive ?? false;
    print('تم تحديث حالة الاشتراك: ${_isSubscribed ? 'مفعل' : 'غير مفعل'}');
  }

  // جلب العروض المتاحة
  Future<List<SubscriptionModel>> getAvailablePackages() async {
    try {
      final offerings = await Purchases.getOfferings();

      print('''
📦 فحص العروض المتاحة:
- عدد العروض: ${offerings.all.length}
- العرض الحالي: ${offerings.current?.identifier ?? 'لا يوجد'}
''');

      if (offerings.current == null) {
        print('⚠️ لا يوجد عرض حالي متاح');
        return [];
      }

      return offerings.current!.availablePackages
          .map((package) => SubscriptionModel.fromPackage(package))
          .toList();
    } catch (e) {
      print('❌ خطأ في جلب العروض: $e');
      return [];
    }
  }

  // شراء باقة
  Future<bool> purchasePackage(Package package) async {
    try {
      print('🛒 بدء عملية الشراء للباقة: ${package.identifier}');

      // محاولة الشراء
      final purchaseResult = await Purchases.purchasePackage(package);

      // تحديث حالة الاشتراك
      _updateSubscriptionStatus(purchaseResult);

      final isPurchased =
          purchaseResult.entitlements.all[entitlementID]?.isActive ?? false;

      if (isPurchased) {
        print('✅ تم الشراء بنجاح!');
        print('معرف المنتج: ${package.storeProduct.identifier}');
        print('السعر: ${package.storeProduct.priceString}');
      } else {
        print('⚠️ لم يتم تفعيل الاشتراك بعد الشراء');
      }

      return isPurchased;
    } catch (e) {
      if (e is PlatformException) {
        print('''
❌ خطأ في عملية الشراء:
- نوع الخطأ: ${e.code}
- الرسالة: ${e.message}

''');

        switch (e.code) {
          case 'PURCHASE_CANCELLED':
            print('تم إلغاء عملية الشراء من قبل المستخدم');
            break;
          case 'INVALID_PURCHASE':
            print('عملية شراء غير صالحة');
            break;
          case 'PRODUCT_NOT_AVAILABLE':
            print('المنتج غير متاح للشراء');
            break;
          case 'NETWORK_ERROR':
            print('خطأ في الاتصال بالإنترنت');
            break;
          default:
            print('خطأ غير معروف: ${e.code}');
        }
      } else {
        print('خطأ غير متوقع: $e');
      }
      return false;
    }
  }

  // التحقق من حالة الاشتراك
  Future<bool> checkSubscriptionStatus() async {
    try {
      print('جاري التحقق من حالة الاشتراك...');

      final customerInfo = await Purchases.getCustomerInfo();
      final entitlement = customerInfo.entitlements.all[entitlementID];

      _isSubscribed = entitlement?.isActive ?? false;

      if (_isSubscribed) {
        print('الاشتراك نشط');
        // يمكنك إضافة معلومات إضافية هنا
        print(
            'تاريخ انتهاء الاشتراك: ${entitlement?.expirationDate ?? "غير محدد"}');
      } else {
        print('الاشتراك غير نشط');
      }

      return _isSubscribed;
    } catch (e) {
      print('خطأ في التحقق من حالة الاشتراك: $e');
      return false;
    }
  }
//   Future<bool> checkPremiumAccess(BuildContext context, bool isPremiumCategory, int currentLevel, int maxMiumLevel) async {
//   if (isPremiumCategory && currentLevel >= maxMiumLevel) {
//     final isSubscribed = await SubscriptionManager.instance.checkSubscriptionStatus();

//     if (!isSubscribed) {
//       if (!context.mounted) return false;
//       showDialog(
//         context: context,
//         builder: (context) => const PremiumContentDialog(
//           title: "محتوى تعليمي مميز",
//           message: "هذا المحتوى متاح فقط للمشتركين في الباقة المميزة",
//         ),
//       );
//       return false;
//     }
//   }
//   return true;
// }
//   bool hasAccess = await checkPremiumAccess(context, widget.isPremiumCategory, currentLevel, maxMiumLevel);

//   if (!hasAccess) return;
}
