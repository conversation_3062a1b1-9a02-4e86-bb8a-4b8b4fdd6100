import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/audioQuestionBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/guessTheWordBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/guessTheWordQuizCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/questionsCubit.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/features/systemConfig/model/answer_mode.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/audioQuestionContainer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/guessTheWordQuestionContainer.dart';
import 'package:flutterquiz/ui/widgets/customAppbar.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/exitGameDialog.dart';
import 'package:flutterquiz/ui/widgets/questionsContainer.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';

class BookmarkQuizScreen extends StatefulWidget {
  const BookmarkQuizScreen({required this.quizType, super.key});

  final QuizTypes quizType;

  @override
  State<BookmarkQuizScreen> createState() => _BookmarkQuizScreenState();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<QuestionsCubit>(
            create: (_) => QuestionsCubit(QuizRepository()),
          ),
          BlocProvider<GuessTheWordQuizCubit>(
            create: (_) => GuessTheWordQuizCubit(QuizRepository()),
          ),
          BlocProvider<UpdateBookmarkCubit>(
            create: (_) => UpdateBookmarkCubit(BookmarkRepository()),
          ),
        ],
        child: BookmarkQuizScreen(
          quizType: routeSettings.arguments! as QuizTypes,
        ),
      ),
    );
  }
}

class _BookmarkQuizScreenState extends State<BookmarkQuizScreen>
    with TickerProviderStateMixin {
  late AnimationController questionAnimationController;
  late AnimationController questionContentAnimationController;
  late AnimationController timerAnimationController = AnimationController(
    vsync: this,
    duration: Duration(
      seconds:
          context.read<SystemConfigCubit>().quizTimer(QuizTypes.bookmarkQuiz),
    ),
  )..addStatusListener(currentUserTimerAnimationStatusListener);
  late Animation<double> questionSlideAnimation;
  late Animation<double> questionScaleUpAnimation;
  late Animation<double> questionScaleDownAnimation;
  late Animation<double> questionContentAnimation;
  late AnimationController animationController;
  late AnimationController topContainerAnimationController;
  int currentQuestionIndex = 0;

  bool completedQuiz = false;

  //to track if setting dialog is open
  bool isSettingDialogOpen = false;

  bool isExitDialogOpen = false;

  late List<GlobalKey<GuessTheWordQuestionContainerState>>
      guessTheWordQuestionContainerKeys = [];

  late List<GlobalKey<AudioQuestionContainerState>> audioQuestionContainerKeys =
      [];

  late AnimationController showOptionAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 500),
  );

  void _getQuestions() {
    /// Fetch Questions from Local Bookmark Cubits to Questions Cubit
    Future.delayed(Duration.zero, () {
      if (widget.quizType == QuizTypes.audioQuestions) {
        final bookmarkedQuestions = context
            .read<AudioQuestionBookmarkCubit>()
            .questions()
            .map((e) => e.copyWith(submittedAnswer: '', attempted: false))
            .toList(growable: false);

        context.read<QuestionsCubit>().updateState(
              QuestionsFetchSuccess(
                questions: bookmarkedQuestions,
                currentPoints: 0,
                quizType: QuizTypes.bookmarkQuiz,
              ),
            );

        for (final _ in bookmarkedQuestions) {
          audioQuestionContainerKeys
              .add(GlobalKey<AudioQuestionContainerState>());
        }
      } else if (widget.quizType == QuizTypes.quizZone) {
        final bookmarkedQuestions = context
            .read<BookmarkCubit>()
            .questions()
            .map((e) => e.copyWith(submittedAnswer: '', attempted: false))
            .toList(growable: false);

        context.read<QuestionsCubit>().updateState(
              QuestionsFetchSuccess(
                questions: bookmarkedQuestions,
                currentPoints: 0,
                quizType: QuizTypes.bookmarkQuiz,
              ),
            );
        timerAnimationController.forward();
      } else {
        final bookmarkedQuestions = context
            .read<GuessTheWordBookmarkCubit>()
            .questions()
            .map((e) => e.copyWith(hasAnswerGiven: false))
            .toList(growable: false);

        context.read<GuessTheWordQuizCubit>().updateState(
              GuessTheWordQuizFetchSuccess(
                questions: bookmarkedQuestions,
                currentPoints: 0,
              ),
            );

        for (final _ in bookmarkedQuestions) {
          guessTheWordQuestionContainerKeys
              .add(GlobalKey<GuessTheWordQuestionContainerState>());
        }
        timerAnimationController.forward();
      }
    });
  }

  @override
  void initState() {
    initializeAnimation();
    _getQuestions();
    super.initState();
  }

  void initializeAnimation() {
    questionContentAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    )..forward();
    questionAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 525),
    );
    questionSlideAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    questionScaleUpAnimation = Tween<double>(begin: 0, end: 0.1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0, 0.5, curve: Curves.easeInQuad),
      ),
    );
    questionContentAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionContentAnimationController,
        curve: Curves.easeInQuad,
      ),
    );
    questionScaleDownAnimation = Tween<double>(begin: 0, end: 0.05).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0.5, 1, curve: Curves.easeOutQuad),
      ),
    );
  }

  void toggleSettingDialog() {
    isSettingDialogOpen = !isSettingDialogOpen;
  }

  //change to next Question
  void changeQuestion() {
    questionAnimationController.forward(from: 0).then((value) {
      //need to dispose the animation controllers
      questionAnimationController.dispose();
      questionContentAnimationController.dispose();
      //initializeAnimation again
      setState(() {
        initializeAnimation();
        currentQuestionIndex++;
      });
      //load content(options, image etc) of question
      questionContentAnimationController.forward();
    });
  }

  //if user has submitted the answer for current question
  bool hasSubmittedAnswerForCurrentQuestion() {
    // ignore: avoid_bool_literals_in_conditional_expressions
    return widget.quizType == QuizTypes.guessTheWord
        ? false
        : context
            .read<QuestionsCubit>()
            .questions()[currentQuestionIndex]
            .attempted;
  }

  Future<void> submitAnswer(String submittedAnswer) async {
    timerAnimationController.stop();
    if (!context
        .read<QuestionsCubit>()
        .questions()[currentQuestionIndex]
        .attempted) {
      context.read<QuestionsCubit>().updateQuestionWithAnswerAndLifeline(
            context.read<QuestionsCubit>().questions()[currentQuestionIndex].id,
            submittedAnswer,
            context.read<UserDetailsCubit>().getUserFirebaseId(),
            context
                .read<SystemConfigCubit>()
                .quizCorrectAnswerCreditScore(QuizTypes.bookmarkQuiz),
            context
                .read<SystemConfigCubit>()
                .quizWrongAnswerDeductScore(QuizTypes.bookmarkQuiz),
          ); //change question
      await Future<void>.delayed(
        const Duration(seconds: inBetweenQuestionTimeInSeconds),
      );
      if (currentQuestionIndex !=
          (context.read<QuestionsCubit>().questions().length - 1)) {
        changeQuestion();
        if (widget.quizType == QuizTypes.quizZone) {
          await timerAnimationController.forward(from: 0);
        } else {
          timerAnimationController.value = 0.0;
        }
      } else {
        setState(() {
          completedQuiz = true;
        });
      }
    }
  }

  Future<void> submitGuessTheWordAnswer(List<String> submittedAnswer) async {
    timerAnimationController.stop();
    final guessTheWordQuizCubit = context.read<GuessTheWordQuizCubit>();
    //if answer not submitted then submit answer
    if (!guessTheWordQuizCubit
        .getQuestions()[currentQuestionIndex]
        .hasAnswered) {
      //submitted answer
      guessTheWordQuizCubit.submitAnswer(
        guessTheWordQuizCubit.getQuestions()[currentQuestionIndex].id,
        submittedAnswer,
        context
            .read<SystemConfigCubit>()
            .quizCorrectAnswerCreditScore(QuizTypes.guessTheWord),
        context
            .read<SystemConfigCubit>()
            .quizWrongAnswerDeductScore(QuizTypes.guessTheWord),
      );
      //wait for some seconds
      await Future<void>.delayed(
        const Duration(seconds: inBetweenQuestionTimeInSeconds),
      );
      //if currentQuestion is last then complete quiz to result screen
      if (currentQuestionIndex ==
          (guessTheWordQuizCubit.getQuestions().length - 1)) {
        //
        setState(() {
          completedQuiz = true;
        });
      } else {
        //change question
        changeQuestion();
        await timerAnimationController.forward(from: 0);
      }
    }
  }

  //listener for current user timer
  void currentUserTimerAnimationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      submitAnswer('-1');
    }
  }

  @override
  void dispose() {
    timerAnimationController
      ..removeStatusListener(currentUserTimerAnimationStatusListener)
      ..dispose();
    questionAnimationController.dispose();
    questionContentAnimationController.dispose();
    showOptionAnimationController.dispose();
    super.dispose();
  }

  void onTapBackButton() {
    isExitDialogOpen = true;
    showDialog<void>(
      context: context,
      builder: (_) => const ExitGameDialog(),
    ).then((_) => isExitDialogOpen = false);
  }

  Widget _buildQuestions() {
    if (widget.quizType == QuizTypes.guessTheWord) {
      return BlocConsumer<GuessTheWordQuizCubit, GuessTheWordQuizState>(
        bloc: context.read<GuessTheWordQuizCubit>(),
        listener: (context, state) {},
        builder: (context, state) {
          if (state is GuessTheWordQuizFetchInProgress ||
              state is GuessTheWordQuizIntial) {
            return Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
              ),
            );
          }
          if (state is GuessTheWordQuizFetchFailure) {
            return Center(
              child: ErrorContainer(
                showBackButton: true,
                errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
                onTapRetry: () {
                  _getQuestions();
                },
                showErrorImage: true,
              ),
            );
          }

          return QuestionsContainer(
            timerAnimationController: timerAnimationController,
            quizType: widget.quizType,
            topPadding: 20,
            answerMode: AnswerMode.showAnswerCorrectnessAndCorrectAnswer,
            lifeLines: const {},
            hasSubmittedAnswerForCurrentQuestion: () {
              return false;
            },
            questions: const [],
            submitAnswer: (_) {},
            questionContentAnimation: questionContentAnimation,
            questionScaleDownAnimation: questionScaleDownAnimation,
            questionScaleUpAnimation: questionScaleUpAnimation,
            questionSlideAnimation: questionSlideAnimation,
            currentQuestionIndex: currentQuestionIndex,
            questionAnimationController: questionAnimationController,
            questionContentAnimationController:
                questionContentAnimationController,
          );
        },
      );
    }
    return BlocBuilder<QuestionsCubit, QuestionsState>(
      bloc: context.read<QuestionsCubit>(),
      builder: (context, state) {
        if (state is QuestionsFetchInProgress || state is QuestionsIntial) {
          return Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          );
        }
        if (state is QuestionsFetchFailure) {
          return Center(
            child: ErrorContainer(
              showBackButton: true,
              errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
              onTapRetry: () {
                _getQuestions();
              },
              showErrorImage: true,
            ),
          );
        }
        final questions = (state as QuestionsFetchSuccess).questions;

        return QuestionsContainer(
          timerAnimationController: timerAnimationController,
          quizType: widget.quizType,
          topPadding: 20,
          answerMode: AnswerMode.showAnswerCorrectnessAndCorrectAnswer,
          lifeLines: const {},
          hasSubmittedAnswerForCurrentQuestion:
              hasSubmittedAnswerForCurrentQuestion,
          questions: questions,
          submitAnswer: submitAnswer,
          questionContentAnimation: questionContentAnimation,
          questionScaleDownAnimation: questionScaleDownAnimation,
          questionScaleUpAnimation: questionScaleUpAnimation,
          questionSlideAnimation: questionSlideAnimation,
          currentQuestionIndex: currentQuestionIndex,
          questionAnimationController: questionAnimationController,
          questionContentAnimationController:
              questionContentAnimationController,
        );
      },
    );
  }

  Widget _buildBottomButton() {
    if (widget.quizType == QuizTypes.guessTheWord) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * 0.05,
          vertical: 10.0,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withOpacity(0.8),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                    blurRadius: 10,
                    spreadRadius: 1,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: CustomRoundedButton(
                widthPercentage: 0.5,
                backgroundColor: Colors.transparent,
                buttonTitle: context.tr('submitBtn') ?? "إرسال",
                elevation: 0,
                titleColor: Colors.white,
                fontWeight: FontWeight.bold,
                onTap: () {
                  submitGuessTheWordAnswer(
                    guessTheWordQuestionContainerKeys[currentQuestionIndex]
                        .currentState!
                        .getSubmittedAnswer(),
                  );
                },
                radius: 10,
                showBorder: false,
                height: 45,
              ),
            ),
          ],
        ),
      );
    }
    if (widget.quizType == QuizTypes.audioQuestions) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * 0.05,
          vertical: 10.0,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SlideTransition(
          position: Tween<Offset>(begin: Offset.zero, end: const Offset(0, 1.5))
              .animate(
            CurvedAnimation(
              parent: showOptionAnimationController,
              curve: Curves.easeInOut,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withOpacity(0.8),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                      blurRadius: 10,
                      spreadRadius: 1,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: CustomRoundedButton(
                  widthPercentage: 0.5,
                  backgroundColor: Colors.transparent,
                  buttonTitle: context.tr(showOptionsKey) ?? "عرض الخيارات",
                  elevation: 0,
                  titleColor: Colors.white,
                  fontWeight: FontWeight.bold,
                  onTap: () {
                    if (!showOptionAnimationController.isAnimating) {
                      showOptionAnimationController.reverse();
                      audioQuestionContainerKeys[currentQuestionIndex]
                          .currentState!
                          .changeShowOption();
                      timerAnimationController.forward(from: 0);
                    }
                  },
                  radius: 10,
                  showBorder: false,
                  height: 45,
                ),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: completedQuiz,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;

        onTapBackButton();
      },
      child: Scaffold(
        appBar: QAppBar(
          roundedAppBar: false,
          title: Text(
            context.tr('bookmarkLbl') ?? "المفضلة",
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
                Theme.of(context).primaryColor.withOpacity(0.6),
                Colors.white,
              ],
              stops: const [0.0, 0.3, 0.5, 0.8],
            ),
          ),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 500),
            child: completedQuiz
                ? Container(
                    margin: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.1,
                    ),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.2),
                          blurRadius: 20,
                          spreadRadius: 2,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: Theme.of(context).primaryColor,
                          size: 60,
                        ),
                        const SizedBox(height: 20),
                        Text(
                          context.tr("completeAllQueLbl") ??
                              "تم الانتهاء من جميع الأسئلة",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 30),
                        CustomRoundedButton(
                          widthPercentage: 0.5,
                          backgroundColor: Theme.of(context).primaryColor,
                          buttonTitle: context.tr('goBAckLbl') ?? "العودة",
                          titleColor: Colors.white,
                          radius: 10,
                          showBorder: false,
                          elevation: 5,
                          onTap: () {
                            if (isSettingDialogOpen) {
                              Navigator.of(context).pop();
                            }
                            if (isExitDialogOpen) {
                              Navigator.of(context).pop();
                            }

                            Navigator.of(context).pop();
                          },
                          height: 45,
                          fontWeight: FontWeight.bold,
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: [
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.only(
                            top: MediaQuery.of(context).size.height * 0.05,
                            left: MediaQuery.of(context).size.width * 0.05,
                            right: MediaQuery.of(context).size.width * 0.05,
                            bottom: MediaQuery.of(context).size.height * 0.01,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.2),
                                blurRadius: 20,
                                spreadRadius: 2,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: _buildQuestions(),
                          ),
                        ),
                      ),
                      if (!completedQuiz)
                        _buildBottomButton()
                      else
                        const SizedBox.shrink(),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
