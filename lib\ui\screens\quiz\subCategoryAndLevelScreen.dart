import 'dart:async';
import 'dart:math' as math;
import 'dart:math';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/unlockedLevelCubit.dart';
import 'package:flutterquiz/features/quiz/models/book_model.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';

import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/ui/widgets/premium_content_dialog.dart';
import 'package:flutterquiz/features/quiz/models/subcategory.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/ui/screens/subscription/subscription_manager.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter/cupertino.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutterquiz/features/quiz/models/video_model.dart';
import 'package:flutterquiz/features/quiz/models/lesson_model.dart';
import 'package:flutterquiz/features/quiz/models/section_model.dart';
import 'package:flutterquiz/ui/screens/quiz/lesson_video_screen.dart';

class SectionCard extends StatefulWidget {
  final SectionModel section;
  final List<LessonModel> lessons;

  const SectionCard({
    super.key,
    required this.section,
    required this.lessons,
  });

  @override
  State<SectionCard> createState() => _SectionCardState();
}

class _SectionCardState extends State<SectionCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final horizontalPadding = size.width * 0.04;
    final imageSize = size.width * 0.15; // زيادة حجم الصورة
    final titleSize = size.width * 0.045; // زيادة حجم العنوان
    final subtitleSize = titleSize * 0.8;
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: size.height * 0.01,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () => setState(() => isExpanded = !isExpanded),
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: EdgeInsets.all(size.width * 0.04),
              child: Row(
                children: [
                  Container(
                    width: imageSize,
                    height: imageSize,
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: CachedNetworkImage(
                        imageUrl: widget.section.imageUrl,
                        fit: BoxFit.cover,
                        placeholder: (_, __) => Center(
                          child: CircularProgressIndicator(
                            color: colorScheme.primary,
                            strokeWidth: 2,
                          ),
                        ),
                        errorWidget: (_, __, ___) => Icon(
                          Icons.play_lesson,
                          color: colorScheme.primary,
                          size: imageSize * 0.5,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: size.width * 0.04),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.section.title,
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: titleSize,
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                        SizedBox(height: size.height * 0.01),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: size.width * 0.02,
                            vertical: size.height * 0.005,
                          ),
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.play_circle_outline,
                                size: subtitleSize,
                                color: colorScheme.primary,
                              ),
                              SizedBox(width: size.width * 0.01),
                              Text(
                                '${widget.lessons.length} دروس',
                                style: GoogleFonts.ibmPlexSansArabic(
                                  fontSize: subtitleSize,
                                  fontWeight: FontWeight.w500,
                                  color: colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: colorScheme.primary,
                    size: titleSize * 1.5,
                  ),
                ],
              ),
            ),
          ),
          AnimatedCrossFade(
            firstChild: const SizedBox(),
            secondChild: Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: colorScheme.primary.withOpacity(0.1),
                    width: 1,
                  ),
                ),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.only(
                  top: size.height * 0.01,
                  bottom: size.height * 0.01,
                ),
                itemCount: widget.lessons.length,
                itemBuilder: (context, index) => _buildLessonCard(
                  context,
                  widget.lessons[index],
                  index + 1,
                  size,
                ),
              ),
            ),
            crossFadeState: isExpanded
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }

  Widget _buildLessonCard(
    BuildContext context,
    LessonModel lesson,
    int index,
    Size size,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final fontSize = size.width * 0.04;

    return InkWell(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => LessonVideoScreen(lesson: lesson),
        ),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: size.width * 0.04,
          vertical: size.height * 0.015,
        ),
        child: Row(
          children: [
            Container(
              width: fontSize * 2,
              height: fontSize * 2,
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(fontSize),
              ),
              child: Center(
                child: Text(
                  '$index',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: fontSize,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ),
            ),
            SizedBox(width: size.width * 0.03),
            Expanded(
              child: Text(
                lesson.title,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: fontSize,
                  color: colorScheme.onSurface,
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.all(fontSize * 0.5),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.play_arrow_rounded,
                color: colorScheme.primary,
                size: fontSize * 1.2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SubCategoryAndLevelScreen extends StatefulWidget {
  const SubCategoryAndLevelScreen({
    required this.isPremiumCategory,
    super.key,
    this.category,
    this.categoryName,
  });

  final String? category;
  final String? categoryName;
  final bool isPremiumCategory;

  static Route<SubCategoryAndLevelScreen> route(RouteSettings routeSettings) {
    final args = routeSettings.arguments! as Map;

    return CupertinoPageRoute(
      builder: (_) => SubCategoryAndLevelScreen(
        category: args['category_id'] as String?,
        categoryName: args['category_name'] as String?,
        isPremiumCategory: args['isPremiumCategory'] as bool? ?? false,
      ),
    );
  }

  @override
  State<SubCategoryAndLevelScreen> createState() =>
      _SubCategoryAndLevelScreen();
}

class _SubCategoryAndLevelScreen extends State<SubCategoryAndLevelScreen>
    with SingleTickerProviderStateMixin {
  List<FileModel> files = [];
  List<VideoModel> videos = [];
  bool isLoading = false;
  bool isLoadingSections = false;

  late AnimationController _animationController;
  int maxLevels = 0;
  final cacheManager = DefaultCacheManager();
  String loadingUrl = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    getFilesData();
    getVideosData();
    fetchSubCategory();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> getFilesData() async {
    try {
      setState(() => isLoading = true);

      final querySnapshot = await FirebaseFirestore.instance
          .collection('files')
          .where('category_id', isEqualTo: widget.category)
          .get();

      final newFiles = querySnapshot.docs
          .map((doc) => FileModel.fromMap(doc.data()))
          .toList();

      setState(() {
        files = newFiles;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading files: $e');
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في تحميل الملفات'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> getVideosData() async {
    await FirebaseFirestore.instance
        .collection('videos')
        .where('category_id', isEqualTo: widget.category)
        .get()
        .then((querySnapshot) {
      for (final doc in querySnapshot.docs) {
        setState(() {
          videos.add(VideoModel.fromMap(doc.data()));
        });
      }
    }).catchError((e) {
      print("Error loading videos: $e");
    });
  }

  void fetchSubCategory() {
    context.read<SubCategoryCubit>().fetchSubCategory(widget.category!);
  }

  Future<String> downloadAndSavePdf(String url) async {
    if (url.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('رابط الملف غير صالح'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      return '';
    }

    try {
      setState(() => loadingUrl = url);
      print('Downloading PDF from: $url');

      final file = await cacheManager.getSingleFile(url);
      print('File downloaded to: ${file.path}');

      if (!mounted) return '';
      setState(() => loadingUrl = '');

      if (!await file.exists()) {
        throw Exception('الملف غير موجود');
      }

      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(
              title: Text(
                'عرض الملف',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            body: SfPdfViewer.file(
              file,
              onDocumentLoadFailed: (details) {
                print('PDF load failed: ${details.error}');
                Navigator.pop(context);
                if (!mounted) return;
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('فشل في تحميل الملف'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
          ),
        ),
      );

      return file.path;
    } catch (e) {
      print('Error in downloadAndSavePdf: $e');
      setState(() => loadingUrl = '');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Expanded(child: Text('حدث خطأ في فتح الملف')),
                TextButton(
                  onPressed: () => downloadAndSavePdf(url),
                  child: const Text(
                    'إعادة المحاولة',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            duration: const Duration(seconds: 4),
          ),
        );
      }
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(25),
            ),
          ),
          centerTitle: true,
          title: Text(
            widget.categoryName!,
            style: GoogleFonts.ibmPlexSansArabic(
              textStyle: const TextStyle(
                color: Colors.white,
                fontSize: 22,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withOpacity(0.8),
                  Theme.of(context).primaryColor.withOpacity(0.6),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
          ),
          bottom: TabBar(
            indicatorSize: TabBarIndicatorSize.label,
            indicatorWeight: 3,
            indicatorColor: Colors.white,
            labelStyle: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            tabs: const [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.grid_view_rounded, size: 20),
                    SizedBox(width: 8),
                    Text('المستويات'),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.menu_book_rounded, size: 20),
                    SizedBox(width: 8),
                    Text('المكتبة'),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.play_lesson, size: 20),
                    SizedBox(width: 8),
                    Text('الدورات'),
                  ],
                ),
              ),
            ],
          ),
          elevation: 0,
        ),
        body: TabBarView(
          children: [
            // المستويات
            Container(
              decoration: const BoxDecoration(),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    _buildLevelSection(),
                  ],
                ),
              ),
            ),
            // المكتبة
            SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  _buildFileSection(),
                ],
              ),
            ),
            SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  _buildCoursesSection(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoursesSection() {
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('sections')
          .where('category_id', isEqualTo: widget.category)
          .orderBy('order')
          .snapshots(),
      builder: (context, sectionsSnapshot) {
        if (sectionsSnapshot.hasError) {
          return Center(child: Text('حدث خطأ: ${sectionsSnapshot.error}'));
        }

        if (sectionsSnapshot.connectionState == ConnectionState.waiting) {
          // تغيير loading indicator إلى shimmer
          return _buildShimmerSections();
        }

        final sections = sectionsSnapshot.data?.docs
                .map((doc) => SectionModel.fromMap(
                    doc.data() as Map<String, dynamic>, doc.id))
                .toList() ??
            [];

        if (sections.isEmpty) {
          return Padding(
            padding: const EdgeInsets.only(top: 50), // تحريك النص للأسفل
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(Icons.school,
                    size: 60, color: Colors.grey.shade400), // أيقونة تعليمية
                const SizedBox(height: 10),
                const Text(
                  'لا توجد دورات متاحة حالياً',
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: sections.length,
          itemBuilder: (context, index) {
            final section = sections[index];

            return StreamBuilder<QuerySnapshot>(
              stream: FirebaseFirestore.instance
                  .collection('lessons')
                  .where('section_id', isEqualTo: section.id)
                  .orderBy('order')
                  .snapshots(),
              builder: (context, lessonsSnapshot) {
                if (!lessonsSnapshot.hasData) {
                  // تغيير loading indicator إلى shimmer للدروس
                  return _buildShimmerLessons();
                }

                final lessons = lessonsSnapshot.data?.docs
                        .map((doc) => LessonModel.fromMap(
                            doc.data() as Map<String, dynamic>, doc.id))
                        .toList() ??
                    [];

                return SectionCard(
                  section: section,
                  lessons: lessons,
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildShimmerSections() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3,
          itemBuilder: (_, __) => Card(
            margin: const EdgeInsets.only(bottom: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                Container(
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      Container(
                        width: 45,
                        height: 45,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: double.infinity,
                              height: 18,
                              color: Colors.white,
                            ),
                            const SizedBox(height: 8),
                            Container(
                              width: 100,
                              height: 12,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerLessons() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          children: List.generate(
            3,
            (index) => Container(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 16,
                          color: Colors.white,
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: 100,
                          height: 12,
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 32,
                    height: 32,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFileSection() {
    if (isLoading) {
      return Center(
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          period: const Duration(milliseconds: 1500),
          child: Container(
            height: 200,
            width: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
          ),
        ),
      );
    }

    if (files.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_off_rounded,
              size: 80,
              color: Theme.of(context).primaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد ملفات متاحة',
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final isTablet = width > 600;
        final isDesktop = width > 900;

        // تحديد عدد الأعمدة بناءً على حجم الشاشة
        final crossAxisCount = isDesktop ? 4 : (isTablet ? 3 : 2);

        // حساب الهوامش والمسافات
        final horizontalPadding = width * 0.04;
        final spacing = width * 0.03;

        // حساب عرض العنصر
        final itemWidth = (width -
                (horizontalPadding * 2) -
                (spacing * (crossAxisCount - 1))) /
            crossAxisCount;

        // حساب نسبة العرض للارتفاع
        final aspectRatio = isDesktop ? 0.85 : (isTablet ? 0.8 : 0.75);

        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding,
            vertical: width * 0.02,
          ),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: spacing,
              mainAxisSpacing: spacing,
              childAspectRatio: aspectRatio,
            ),
            itemCount: files.length,
            itemBuilder: (context, index) {
              final file = files[index];
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                child: GestureDetector(
                  onTap: () => downloadAndSavePdf(file.fileUrl),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Expanded(
                          flex: 3,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(20),
                              ),
                              gradient: LinearGradient(
                                begin: Alignment.topRight,
                                end: Alignment.bottomLeft,
                                colors: [
                                  Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.1),
                                  Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.05),
                                ],
                              ),
                            ),
                            child: Stack(
                              fit: StackFit.expand,
                              children: [
                                if (file.imageUrl.isNotEmpty)
                                  ClipRRect(
                                    borderRadius: const BorderRadius.vertical(
                                      top: Radius.circular(20),
                                    ),
                                    child: Hero(
                                      tag: 'file_${file.fileUrl}',
                                      child: CachedNetworkImage(
                                        imageUrl: file.imageUrl,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) =>
                                            _buildShimmer(constraints),
                                        errorWidget: (context, url, error) =>
                                            _buildFileIcon(itemWidth * 0.2),
                                      ),
                                    ),
                                  )
                                else
                                  _buildFileIcon(itemWidth * 0.2),
                                if (loadingUrl == file.fileUrl)
                                  _buildLoadingOverlay(),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    file.fileName,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: GoogleFonts.ibmPlexSansArabic(
                                      fontSize: itemWidth * 0.065,
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ),
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .primaryColor
                                            .withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.touch_app_rounded,
                                            size: itemWidth * 0.05,
                                            color:
                                                Theme.of(context).primaryColor,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            'اضغط للعرض',
                                            style:
                                                GoogleFonts.ibmPlexSansArabic(
                                              fontSize: itemWidth * 0.04,
                                              color: Theme.of(context)
                                                  .primaryColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildFileIcon(double size) {
    return Container(
      padding: EdgeInsets.all(size * 0.2),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.picture_as_pdf_rounded,
        size: size,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildShimmer(BoxConstraints constraints) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
              ),
            ],
          ),
          child: const CircularProgressIndicator(),
        ),
      ),
    );
  }

  Widget _buildLevelSection() {
    return BlocConsumer<SubCategoryCubit, SubCategoryState>(
      listener: (context, state) {
        if (state is SubCategoryFetchFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      builder: (context, state) {
        if (state is SubCategoryFetchInProgress ||
            state is SubCategoryInitial) {
          return Padding(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                period: const Duration(milliseconds: 1500),
                child: Container(
                  height: 20,
                  width: 20,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          );
        }
        if (state is SubCategoryFetchFailure) {
          return Padding(
            padding: const EdgeInsets.all(20),
            child: ErrorContainer(
              errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
              topMargin: 0,
              onTapRetry: fetchSubCategory,
              showErrorImage: false,
            ),
          );
        }
        if (state is SubCategoryFetchSuccess) {
          final subCategoryList = state.subcategoryList;
          final quizRepository = QuizRepository();
          return ListView.separated(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (_, i) => const SizedBox(height: 12),
            itemCount: subCategoryList.length,
            itemBuilder: (_, i) {
              return BlocProvider<UnlockedLevelCubit>(
                lazy: false,
                create: (_) => UnlockedLevelCubit(quizRepository),
                child: AnimatedSubcategoryContainer(
                  subcategory: subCategoryList[i],
                  category: widget.category,
                  isPremiumCategory: widget.isPremiumCategory,
                ),
              );
            },
          );
        }
        return const SizedBox();
      },
    );
  }
}

class AnimatedSubcategoryContainer extends StatefulWidget {
  const AnimatedSubcategoryContainer({
    required this.subcategory,
    required this.category,
    required this.isPremiumCategory,
    super.key,
  });

  final String? category;
  final Subcategory subcategory;
  final bool isPremiumCategory;

  @override
  State<AnimatedSubcategoryContainer> createState() =>
      _AnimatedSubcategoryContainerState();
}

class _AnimatedSubcategoryContainerState
    extends State<AnimatedSubcategoryContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController expandController;
  late Animation<double> animation;
  late Animation<double> arrowAnimation;
  bool _isExpanded = false;
  late final int maxLevels;
  bool _showAllLevels = false;

  @override
  void initState() {
    super.initState();
    maxLevels = int.parse(widget.subcategory.maxLevel ?? '6');
    _showAllLevels = maxLevels <= 6;

    expandController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    animation = CurvedAnimation(
      parent: expandController,
      curve: Curves.easeInOut,
    );

    arrowAnimation = Tween<double>(
      begin: 0,
      end: 0.5,
    ).animate(animation);

    fetchUnlockedLevel();
  }

  @override
  void dispose() {
    expandController.dispose();
    super.dispose();
  }

  void _onTapSubcategory() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        expandController.forward();
      } else {
        expandController.reverse();
      }
    });
  }

  void fetchUnlockedLevel() {
    context.read<UnlockedLevelCubit>().fetchUnlockLevel(
          widget.category!,
          widget.subcategory.id!,
        );
  }

  Future<void> _handleLevelTap(
      int levelNumber, bool isUnlocked, bool isPremium) async {
    if (isPremium) {
      // التحقق من حالة الاشتراك
      final isSubscribed =
          await SubscriptionManager.instance.checkSubscriptionStatus();

      if (!isSubscribed) {
        // عرض نافذة المحتوى المميز
        if (!context.mounted) return;
        showDialog(
          context: context,
          builder: (context) => const PremiumContentDialog(
            title: "محتوى تعليمي مميز",
            message: "هذا المحتوى متاح فقط للمشتركين في الباقة المميزة",
          ),
        );
        return;
      }
    }

    if (isUnlocked) {
      Navigator.of(context).pushNamed(
        Routes.quiz,
        arguments: {
          'numberOfPlayer': 1,
          'quizType': QuizTypes.quizZone,
          'categoryId': widget.category,
          'subcategoryId': widget.subcategory.id,
          'level': levelNumber.toString(),
          'subcategoryMaxLevel': widget.subcategory.maxLevel,
          'unlockedLevel': context.read<UnlockedLevelCubit>().state
                  is UnlockedLevelFetchSuccess
              ? (context.read<UnlockedLevelCubit>().state
                      as UnlockedLevelFetchSuccess)
                  .unlockedLevel
              : 1,
          'contestId': '',
          'comprehensionId': '',
          'quizName': 'Quiz Zone',
          'isPremiumCategory': widget.isPremiumCategory,
        },
      ).then((_) => fetchUnlockedLevel());
    } else {
      UiUtils.showSnackBar(
        context.tr(convertErrorCodeToLanguageKey(errorCodeLevelLocked))!,
        context,
      );
    }
  }

  Widget _buildEnhancedLevelCard(
    BuildContext context, {
    required int levelNumber,
    required bool isUnlocked,
    required bool isCompleted,
    required bool isPremium,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    // تحديد الألوان بناءً على الحالة
    Color cardColor;
    Color textColor;
    Color iconColor;
    IconData statusIcon;

    if (isPremium && !isUnlocked) {
      cardColor = Colors.amber.shade100;
      textColor = Colors.amber.shade800;
      iconColor = Colors.amber.shade600;
      statusIcon = Icons.diamond;
    } else if (isCompleted) {
      cardColor = Colors.green.shade50;
      textColor = Colors.green.shade800;
      iconColor = Colors.green.shade600;
      statusIcon = Icons.check_circle;
    } else if (isUnlocked) {
      cardColor = colorScheme.primary.withValues(alpha: 0.1);
      textColor = colorScheme.primary;
      iconColor = colorScheme.primary;
      statusIcon = Icons.play_circle_filled;
    } else {
      cardColor = Colors.grey.shade100;
      textColor = Colors.grey.shade600;
      iconColor = Colors.grey.shade500;
      statusIcon = Icons.lock;
    }

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isUnlocked
                ? colorScheme.primary.withValues(alpha: 0.3)
                : Colors.grey.shade300,
            width: isUnlocked ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isUnlocked
                  ? colorScheme.primary.withValues(alpha: 0.15)
                  : Colors.black.withValues(alpha: 0.05),
              blurRadius: isUnlocked ? 12 : 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة الحالة
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(
                  statusIcon,
                  color: iconColor,
                  size: 28,
                ),
              ),
              const SizedBox(height: 12),

              // رقم المستوى
              Text(
                'المستوى $levelNumber',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              // معلومات إضافية
              if (isPremium && !isUnlocked) ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'مميز',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.amber.shade800,
                    ),
                  ),
                ),
              ] else if (isCompleted) ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'مكتمل',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.green.shade800,
                    ),
                  ),
                ),
              ] else if (isUnlocked) ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'متاح',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: colorScheme.primary,
                    ),
                  ),
                ),
              ] else ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'مغلق',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final subcategory = widget.subcategory;
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Container(
      padding: EdgeInsets.all(screenWidth * 0.01),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.02),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: screenWidth * 0.03,
            offset: Offset(0, screenHeight * 0.01),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: _onTapSubcategory,
            borderRadius: BorderRadius.circular(screenWidth * 0.02),
            child: Padding(
              padding: EdgeInsets.all(screenWidth * 0.02),
              child: Row(
                children: [
                  Container(
                    height: screenWidth * 0.2,
                    width: screenWidth * 0.2,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(screenWidth * 0.025),
                      border: Border.all(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    padding: EdgeInsets.all(screenWidth * 0.005),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(screenWidth * 0.025),
                      child: CachedNetworkImage(
                        imageUrl: subcategory.image!,
                        fit: BoxFit.cover,
                        errorWidget: (_, s, d) => Icon(
                          Icons.subject,
                          color: Theme.of(context).primaryColor,
                          size: screenWidth * 0.12,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: screenWidth * 0.05),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          subcategory.subcategoryName!,
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: screenWidth * 0.045,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.01),
                        Text(
                          'عدد المستويات: $maxLevels',
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: screenWidth * 0.035,
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  AnimatedBuilder(
                    animation: arrowAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _isExpanded ? pi : 0,
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: Theme.of(context).primaryColor,
                          size: screenWidth * 0.08,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          SizeTransition(
            sizeFactor: animation,
            child: _buildLevelSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildLevelSection() {
    return BlocConsumer<UnlockedLevelCubit, UnlockedLevelState>(
      listener: (context, state) {
        if (state is UnlockedLevelFetchFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      builder: (_, state) {
        if (state is UnlockedLevelFetchInProgress ||
            state is UnlockedLevelInitial) {
          return Center(
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              period: const Duration(milliseconds: 1500),
              child: Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          );
        }

        if (state is UnlockedLevelFetchFailure) {
          return Center(
            child: ErrorContainer(
              errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
              topMargin: 0,
              onTapRetry: fetchUnlockedLevel,
              showErrorImage: false,
            ),
          );
        }

        if (state is UnlockedLevelFetchSuccess) {
          final unlockedLevel = state.unlockedLevel;
          return Container(
            margin: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.04,
              vertical: MediaQuery.of(context).size.height * 0.02,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Container(
                  margin: const EdgeInsets.only(bottom: 15),
                  child: Text(
                    'اختر المستوى',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount:
                        MediaQuery.of(context).size.width > 600 ? 3 : 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 0.85,
                  ),
                  itemCount:
                      _showAllLevels ? maxLevels : math.min(6, maxLevels),
                  itemBuilder: (context, i) {
                    final isUnlocked = (i + 1) <= unlockedLevel;
                    final isCompleted = (i + 2) <= unlockedLevel;
                    final isPremium =
                        widget.isPremiumCategory && i >= maxMiumlever;

                    return _buildEnhancedLevelCard(
                      context,
                      levelNumber: i + 1,
                      isUnlocked: isUnlocked,
                      isCompleted: isCompleted,
                      isPremium: isPremium,
                      onTap: () =>
                          _handleLevelTap(i + 1, isUnlocked, isPremium),
                    );
                  },
                ),
                if (maxLevels > 6) ...[
                  const SizedBox(height: 20),
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _showAllLevels = !_showAllLevels;
                        });
                      },
                      borderRadius: BorderRadius.circular(30),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 16,
                        ),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _showAllLevels ? 'إخفاء' : 'عرض الكل',
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 14,
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              _showAllLevels
                                  ? Icons.keyboard_arrow_up
                                  : Icons.keyboard_arrow_down,
                              color: Theme.of(context).primaryColor,
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        }

        return Center(
          child: Text(
            context.tr('noLevelsLbl')!,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              color: Theme.of(context).primaryColor,
            ),
          ),
        );
      },
    );
  }
}
