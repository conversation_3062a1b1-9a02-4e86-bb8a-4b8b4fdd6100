import UIKit
import Flutter
import Firebase
import Photos
import CallKit
import awesome_notifications

@main
@objc class AppDelegate: FlutterAppDelegate {
    var callObserver: CXCallObserver!
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // Remove screen recording prevention
        // application.isIdleTimerDisabled = true
        
        // Initialize other services
        FirebaseApp.configure()
        GeneratedPluginRegistrant.register(with: self)
        
        // Setup call observer
        callObserver = CXCallObserver()
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    override func applicationDidBecomeActive(_ application: UIApplication) {
        self.window.isHidden = false
    }
    override func applicationWillResignActive(_ application: UIApplication) {
        self.window.rootViewController?.view.endEditing(true)
        self.window.isHidden = true
    }
}
extension AppDelegate: CXCallObserverDelegate {
    
    func callObserver(_ callObserver: CXCallObserver, callChanged call: CXCall) {
        if call.hasEnded == true {
            print("Call Disconnected")
        }
        
        if call.isOutgoing == true && call.hasConnected == false {
            print("call Dialing")
        }
        
        if call.isOutgoing == false && call.hasConnected == false && call.hasEnded == false {
            print("call Incoming")
        }
        
        if call.hasConnected == true && call.hasEnded == false {
            print("Call Connected")
        }
    }
}
