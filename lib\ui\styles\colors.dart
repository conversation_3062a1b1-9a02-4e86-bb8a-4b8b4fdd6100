import 'package:flutter/material.dart';
//
// /// Light Theme
// const klBackgroundColor = Color(0xffffffff);
// const klCanvasColor = Color(0xcc000000);
// const klPageBackgroundColor = Color(0xfff3f7fa);
// const klPrimaryColor = Color(0xff3F72AF);
//
// const klPrimaryTextColor = Color(0xff45536d);
//
// /// Dark Theme
const kdBackgroundColor = Color(0xff294261);
const kdCanvasColor = Color(0xccffffff);
const kdPageBackgroundColor = Color(0xff233354);
// const kdPrimaryColor = Color(0xff3F72AF);
const kdPrimaryColor = Color(0xff0c73e9);
const kdPrimaryTextColor = Color(0xfffefefe);


/// Light Theme
const klBackgroundColor = Color(0xffF5F7FA); // Light cool gray
const klCanvasColor = Color(0xffECEFF4); // Soft off-white
const klPageBackgroundColor = Color(0xffE2E8F0); // Very light blue-gray
//const klPrimaryColor = Color(0xff4A90E2); // Soft, modern blue
const klPrimaryColor = Color.fromARGB(255, 12, 115, 233) ;// Soft, modern blue

const klPrimaryTextColor = Color(0xff2D3A45); // Muted dark blue-gray

/// Dark Theme
// const kdBackgroundColor = Color(0xff1C2A3A); // Deep blue-gray
// const kdCanvasColor = Color(0xff2E3A48); // Slightly lighter than background
// const kdPageBackgroundColor = Color(0xff1A222D); // Darker background for contrast
// const kdPrimaryColor = Color(0xff50B7F1); // Light, vibrant blue
// const kdPrimaryTextColor = Color(0xffD1D9E6); // Soft white with a blue tint

/// Common
// const kAddCoinColor = Colors.green;
const kBadgeLockedColor = Color(0xffA0AEC0); // Neutral gray for locked items
const kHurryUpTimerColor = Color(0xffFF6B6B); // Soft red-orange
const kCorrectAnswerColor = Color(0xff48BB78); // Fresh green
const kWrongAnswerColor = Color(0xffE53E3E); // Slightly muted red
const kPendingColor = Color(0xffF6AD55); // Warm orange-yellow
