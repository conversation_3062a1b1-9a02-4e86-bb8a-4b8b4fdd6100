import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/app_localization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/gdpr_helper.dart';
import 'package:unity_ads_plugin/unity_ads_plugin.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoAnimationController;
  late AnimationController _backgroundAnimationController;
  late AnimationController _glowAnimationController;
  late Animation<double> _logoScaleUpAnimation;
  late Animation<double> _fadeInAnimation;

  bool _systemConfigLoaded = false;

  final _appLogoPath = Assets.splashLogo;
  // final _orgLogoPath = Assets.orgLogo;
  final showCompanyLogo = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _fetchSystemConfig();
    _loadLanguage();
  }

  @override
  void dispose() {
    // إيقاف جميع الـ animations قبل التخلص منها
    _logoAnimationController.stop();
    _backgroundAnimationController.stop();
    _glowAnimationController.stop();

    _logoAnimationController.dispose();
    _backgroundAnimationController.dispose();
    _glowAnimationController.dispose();
    super.dispose();
  }

  void _initAnimations() {
    _logoAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    )..addListener(() {
        if (_logoAnimationController.isCompleted) {
          _navigateToNextScreen();
        }
      });

    _backgroundAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    )..repeat();

    _glowAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _logoScaleUpAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoAnimationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutBack),
      ),
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoAnimationController,
        curve: const Interval(0.3, 0.8, curve: Curves.easeIn),
      ),
    );

    _logoAnimationController.forward();
    _backgroundAnimationController.repeat();
    _glowAnimationController.repeat(reverse: true);
  }

  Future<void> _initUnityAds() async {
    final gameId = context.read<SystemConfigCubit>().unityGameId;

    // التحقق من صحة Game ID قبل التهيئة
    if (gameId.isEmpty ||
        gameId == 'Android Game Id' ||
        gameId == 'IOS Game Id') {
      print('Unity Ads: Invalid Game ID, skipping initialization');
      return;
    }

    try {
      await UnityAds.init(
        gameId: gameId,
        testMode: true,
        onComplete: () => print('Unity Ads: Initialized successfully'),
        onFailed: (err, msg) =>
            print('Unity Ads: Initialization Failed: $err $msg'),
      );
    } catch (e) {
      print('Unity Ads: Exception during initialization: $e');
    }
  }

  Future<void> _fetchSystemConfig() async {
    await context.read<SystemConfigCubit>().getSystemConfig();
    await GdprHelper.initialize();
  }

  Future<void> _loadLanguage() async {
    await AppLocalization().loadLanguage();
  }

  Future<void> _navigateToNextScreen() async {
    if (!_systemConfigLoaded) return;

    await _initUnityAds();

    final showIntroSlider =
        context.read<SettingsCubit>().state.settingsModel!.showIntroSlider;
    final currAuthState = context.read<AuthCubit>().state;

    if (showIntroSlider) {
      await Navigator.of(context).pushReplacementNamed(Routes.languageSelect);
      return;
    }

    if (currAuthState is Authenticated) {
      await Navigator.of(context).pushReplacementNamed(
        Routes.home,
        arguments: false,
      );
    } else {
      await Navigator.of(context).pushReplacementNamed(
        Routes.home,
        arguments: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SystemConfigCubit, SystemConfigState>(
      bloc: context.read<SystemConfigCubit>(),
      listener: (context, state) {
        if (state is SystemConfigFetchSuccess) {
          if (!_systemConfigLoaded) {
            _systemConfigLoaded = true;
          }

          if (_logoAnimationController.isCompleted) {
            _navigateToNextScreen();
          }
        }
      },
      builder: (context, state) {
        if (state is SystemConfigFetchFailure) {
          return Scaffold(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            body: Center(
              key: const Key('errorContainer'),
              child: ErrorContainer(
                showBackButton: true,
                errorMessageColor: Theme.of(context).colorScheme.onTertiary,
                errorMessage: convertErrorCodeToLanguageKey(state.errorCode),
                onTapRetry: () {
                  setState(_initAnimations);
                  _fetchSystemConfig();
                  _loadLanguage();
                },
                showErrorImage: true,
              ),
            ),
          );
        }

        final size = MediaQuery.of(context).size;

        return Scaffold(
          body: Stack(
            fit: StackFit.expand,
            children: [
              // Enhanced gradient background
              AnimatedBuilder(
                animation: _backgroundAnimationController,
                builder: (context, child) {
                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.blue[50]!,
                          Colors.white,
                          Colors.blue[100]!,
                          Colors.blue[200]!.withOpacity(0.3),
                          Colors.white,
                        ],
                        stops: [
                          0.0,
                          0.3 + (_backgroundAnimationController.value * 0.1),
                          0.5 + (_backgroundAnimationController.value * 0.1),
                          0.7 + (_backgroundAnimationController.value * 0.1),
                          1.0,
                        ],
                      ),
                    ),
                  );
                },
              ),

              // Animated circles with faster animation
              ...List.generate(5, (index) {
                final delay = index * 0.15;
                final size = 100.0 + (index * 30.0);
                return Positioned(
                  top: 100 + (index * 40),
                  right: -size / 2,
                  child: AnimatedBuilder(
                    animation: _backgroundAnimationController,
                    builder: (context, child) {
                      final animation =
                          (_backgroundAnimationController.value + delay) % 1.0;
                      return Transform.scale(
                        scale: 0.8 + (animation * 0.3),
                        child: Container(
                          width: size,
                          height: size,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.blue[100]!
                                .withOpacity(0.1 - (index * 0.01)),
                          ),
                        ),
                      );
                    },
                  ),
                );
              }),

              // Animated dots with faster animation
              ...List.generate(20, (index) {
                final random = index * 50.0;
                return Positioned(
                  left: random % size.width,
                  top: random % size.height,
                  child: AnimatedBuilder(
                    animation: _backgroundAnimationController,
                    builder: (context, child) {
                      final animation = (_backgroundAnimationController.value +
                              (index * 0.08)) %
                          1.0;
                      return Transform.scale(
                        scale: 0.5 + (animation * 0.4),
                        child: Container(
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.blue[200]!.withOpacity(0.2),
                          ),
                        ),
                      );
                    },
                  ),
                );
              }),

              // Main content - Logo with enhanced colors
              Center(
                child: AnimatedBuilder(
                  animation: Listenable.merge([
                    _logoAnimationController,
                    _glowAnimationController,
                  ]),
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _logoScaleUpAnimation.value,
                      child: FadeTransition(
                        opacity: _fadeInAnimation,
                        child: Container(
                          width: size.width * 0.45,
                          height: size.width * 0.45,
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                color: Colors.blue[200]!.withOpacity(0.6),
                                blurRadius: 30,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: ShaderMask(
                            shaderCallback: (Rect bounds) {
                              return LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.blue[400]!,
                                  Colors.blue[600]!,
                                  Colors.blue[700]!,
                                ],
                                stops: [
                                  0.0,
                                  0.5,
                                  1.0,
                                ],
                              ).createShader(bounds);
                            },
                            blendMode: BlendMode.srcATop,
                            child: ShaderMask(
                              shaderCallback: (Rect bounds) {
                                return LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.white.withOpacity(1.0),
                                    Colors.white.withOpacity(0.8),
                                    Colors.white.withOpacity(0.9),
                                  ],
                                ).createShader(bounds);
                              },
                              child: QImage(
                                imageUrl: _appLogoPath,
                                fit: BoxFit.contain,
                                padding: EdgeInsets.zero,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Modern loading indicator
              Positioned(
                bottom: size.height * 0.15,
                left: 0,
                right: 0,
                child: AnimatedBuilder(
                  animation: _logoAnimationController,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeInAnimation,
                      child: Center(
                        child: Container(
                          width: 50,
                          height: 50,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.blue[100]!.withOpacity(0.3),
                                blurRadius: 15,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.blue[300]!,
                            ),
                            strokeWidth: 3,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
