import 'package:country_code_picker/country_code_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/auth/cubits/signInCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/ui/screens/auth/widgets/all.dart';
import 'package:flutterquiz/ui/widgets/all.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

const int otpTimeOutSeconds = 60;

class OtpScreen extends StatefulWidget {
  const OtpScreen({super.key});

  @override
  State<OtpScreen> createState() => _OtpScreen();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => BlocProvider<SignInCubit>(
        child: const OtpScreen(),
        create: (_) => SignInCubit(AuthRepository()),
      ),
    );
  }
}

class _OtpScreen extends State<OtpScreen> with SingleTickerProviderStateMixin {
  TextEditingController phoneNumberController = TextEditingController();

  CountryCode? selectedCountryCode;
  final smsCodeController = TextEditingController();

  final resendOtpTimerContainerKey = GlobalKey<ResendOtpTimerContainerState>();

  bool codeSent = false;
  bool hasError = false;
  String errorMessage = '';
  bool isLoading = false;
  String userVerificationId = '';

  bool enableResendOtpButton = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    // تعيين رمز الدولة للسعودية مباشرة
    selectedCountryCode =  CountryCode(
      code: 'SA',
      dialCode: '+966',
      name: 'السعودية',
      flagUri: 'flags/sa.png',
    );
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    phoneNumberController.dispose();
    smsCodeController.dispose();
    super.dispose();
  }

  Future<void> signInWithPhoneNumber({required String phoneNumber}) async {
    await FirebaseAuth.instance.verifyPhoneNumber(
      timeout: const Duration(seconds: otpTimeOutSeconds),
      phoneNumber: '${selectedCountryCode!.dialCode} $phoneNumber',
      verificationCompleted: (PhoneAuthCredential credential) {},
      verificationFailed: (FirebaseAuthException e) {
        //if otp code does not verify

        UiUtils.showSnackBar(
          context.tr(
            convertErrorCodeToLanguageKey(
              e.code == 'invalid-phone-number'
                  ? errorCodeInvalidPhoneNumber
                  : errorCodeDefaultMessage,
            ),
          )!,
          context,
        );

        setState(() {
          isLoading = false;
        });
      },
      codeSent: (String verificationId, int? resendToken) {
        setState(() {
          codeSent = true;
          userVerificationId = verificationId;
          isLoading = false;
        });

        Future<void>.delayed(const Duration(milliseconds: 75)).then((value) {
          resendOtpTimerContainerKey.currentState?.setResendOtpTimer();
        });
      },
      codeAutoRetrievalTimeout: (String verificationId) {},
    );
  }

  Widget _buildOTPSentToPhoneNumber() {
    if (codeSent) {
      return Column(
        children: [
          Text(
            context.tr(otpSendLbl)!,
            textAlign: TextAlign.center,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onTertiary.withOpacity(0.7),
              height: 1.5,
            ),
          ),
          Text(
            '${selectedCountryCode!.dialCode} ${phoneNumberController.text.trim()}',
            textAlign: TextAlign.center,
            style: GoogleFonts.ibmPlexSansArabic(
              color: Theme.of(context).colorScheme.onTertiary,
              fontSize: 16,
              fontWeight: FontWeights.medium,
            ),
          ),
        ],
      );
    }

    return const SizedBox();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final primaryColor = Theme.of(context).primaryColor;

    return PopScope(
      canPop:
          context.read<SignInCubit>().state is! SignInProgress && !isLoading,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                primaryColor.withOpacity(0.25),
                primaryColor.withOpacity(0.15),
                primaryColor.withOpacity(0.05),
                Colors.transparent,
              ],
              stops: const [0.0, 0.3, 0.6, 1.0],
            ),
          ),
          child: Stack(
            children: [
              SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(
                  vertical: size.height * UiUtils.vtMarginPct,
                  horizontal: 20,
                ),
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: size.height * .05),
                      _backButton(),
                      SizedBox(height: size.height * 0.02),
                      _registerText(),
                      SizedBox(height: size.height * 0.02),
                      // Info card for Saudi phone numbers
                      if (!codeSent) _buildSaudiPhoneInfoCard(),
                      SizedBox(height: size.height * 0.02),
                      _buildOTPSentToPhoneNumber(),
                      SizedBox(height: size.height * 0.03),
                      if (codeSent)
                        _buildSmsCodeContainer()
                      else
                        _buildSaudiMobileNumberInput(),
                      SizedBox(height: size.height * 0.03),
                      if (codeSent)
                        _buildSubmitOtpContainer()
                      else
                        _buildRequestOtpContainer(),
                      if (codeSent) 
                        Padding(
                          padding: const EdgeInsets.only(top: 12.0),
                          child: _buildResendText(),
                        ) 
                      else 
                        const SizedBox(),
                      SizedBox(height: size.height * 0.03),
                      const TermsAndCondition(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _backButton() {
    return Row(
      children: [
        Material(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(30),
          child: InkWell(
            borderRadius: BorderRadius.circular(30),
            onTap: Navigator.of(context).pop,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.arrow_back_ios_new_rounded,
                size: 22,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _registerText() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ShaderMask(
          blendMode: BlendMode.srcIn,
          shaderCallback: (bounds) => LinearGradient(
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ).createShader(bounds),
          child: Text(
            context.tr('registration')!,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 32,
              fontWeight: FontWeights.bold,
              height: 1.2,
            ),
          ),
        ),
        if (!codeSent) ...[
          const SizedBox(height: 10),
          SizedBox(
            width: MediaQuery.of(context).size.width * .85,
            child: Text(
              codeSent 
                ? 'أدخل رمز التحقق المرسل إلى هاتفك'
                : 'سجل باستخدام رقم الهاتف السعودي للمتابعة',
              textAlign: TextAlign.center,
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 16,
                fontWeight: FontWeights.regular,
                color: Theme.of(context).colorScheme.onTertiary.withOpacity(0.7),
                height: 1.5,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSaudiPhoneInfoCard() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.15),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.info_outline,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'أدخل رقم هاتفك السعودي بدون الصفر البادئ (مثال: 5xxxxxxxx)',
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 13,
                color: Theme.of(context).colorScheme.onBackground,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaudiMobileNumberInput() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        textDirection: TextDirection.ltr, // لضمان عرض الرقم بشكل صحيح من اليسار لليمين
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.08),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                // استخدام أيقونة بدلاً من الصورة لتجنب مشاكل تحميل العلم
                Icon(
                  Icons.flag_rounded,
                  color: Theme.of(context).primaryColor,
                  size: 22,
                ),
                const SizedBox(width: 8),
                Text(
                  '+966',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onTertiary,
                    fontSize: 16,
                    fontWeight: FontWeights.medium,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: TextField(
              enabled: !isLoading,
              controller: phoneNumberController,
              keyboardType: TextInputType.phone,
              cursorColor: Theme.of(context).primaryColor,
              textAlign: TextAlign.left,
              textDirection: TextDirection.ltr,
              inputFormatters: [
                LengthLimitingTextInputFormatter(9),
                FilteringTextInputFormatter.digitsOnly,
              ],
              style: GoogleFonts.ibmPlexSansArabic(
                color: Theme.of(context).colorScheme.onTertiary,
                fontSize: 16,
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                hintText: 'أدخل رقم الهاتف بدون صفر',
                hintStyle: GoogleFonts.ibmPlexSansArabic(
                  color: Theme.of(context).colorScheme.onTertiary.withOpacity(0.4),
                  fontSize: 14,
                ),
                hintTextDirection: TextDirection.rtl,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmsCodeContainer() {
    final colorScheme = Theme.of(context).colorScheme;
    final primaryColor = Theme.of(context).primaryColor;

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.05),
                blurRadius: 10,
                spreadRadius: 2,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Directionality(
            // تطبيق اتجاه من اليسار لليمين لضمان عرض الأرقام بشكل صحيح
            textDirection: TextDirection.ltr,
            child: PinCodeTextField(
              onChanged: (value) {},
              keyboardType: TextInputType.number,
              appContext: context,
              length: 6,
              hintCharacter: '0',
              textStyle: GoogleFonts.ibmPlexSansArabic(
                color: colorScheme.onTertiary,
                fontSize: 20,
                fontWeight: FontWeights.semiBold,
              ),
              hintStyle: GoogleFonts.ibmPlexSansArabic(
                color: colorScheme.onTertiary.withOpacity(.3),
              ),
              pinTheme: PinTheme(
                selectedFillColor: primaryColor.withOpacity(0.1),
                inactiveColor: colorScheme.surface,
                activeColor: primaryColor,
                inactiveFillColor: colorScheme.surface,
                selectedColor: primaryColor,
                shape: PinCodeFieldShape.box,
                borderRadius: BorderRadius.circular(10),
                fieldHeight: 50,
                fieldWidth: 45,
                activeFillColor: colorScheme.surface,
                borderWidth: 1.5,
              ),
              animationType: AnimationType.scale,
              cursorColor: primaryColor,
              animationDuration: const Duration(milliseconds: 300),
              enableActiveFill: true,
              controller: smsCodeController,
            ),
          ),
        ),
        const SizedBox(height: 10),
        Text(
          'تم إرسال رمز التحقق المكون من 6 أرقام',
          textAlign: TextAlign.center,
          style: GoogleFonts.ibmPlexSansArabic(
            fontSize: 14,
            color: colorScheme.onTertiary.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitOtpContainer() {
    return BlocConsumer<SignInCubit, SignInState>(
      bloc: context.read<SignInCubit>(),
      builder: (context, state) {
        if (state is SignInProgress) {
          return const CircularProgressContainer(size: 50);
        }

        return Container(
          width: MediaQuery.of(context).size.width,
          height: 55,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).primaryColor.withOpacity(0.25),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: () async {
                if (smsCodeController.text.trim().length == 6) {
                  context.read<SignInCubit>().signInUser(
                        AuthProviders.mobile,
                        smsCode: smsCodeController.text.trim(),
                        verificationId: userVerificationId,
                      );
                } else {
                  UiUtils.showSnackBar(
                    'يرجى إدخال رمز التحقق المكون من 6 أرقام',
                    context,
                  );
                }
              },
              child: Center(
                child: Text(
                  context.tr(submitBtn)!,
                  style: GoogleFonts.ibmPlexSansArabic(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeights.bold,
                  ),
                ),
              ),
            ),
          ),
        );
      },
      listener: (context, state) {
        if (state is SignInSuccess) {
          //update auth details
          context.read<AuthCubit>().updateAuthDetails(
                authProvider: AuthProviders.mobile,
                authStatus: true,
                firebaseId: state.user.uid,
                isNewUser: state.isNewUser,
              );

          if (state.isNewUser) {
            context.read<UserDetailsCubit>().fetchUserDetails();
            Navigator.of(context).pop();
            Navigator.of(context)
                .pushReplacementNamed(Routes.selectProfile, arguments: true);
          } else {
            context.read<UserDetailsCubit>().fetchUserDetails();
            Navigator.of(context).pop();
            Navigator.of(context).pushNamedAndRemoveUntil(
              Routes.home,
              (_) => false,
              arguments: false,
            );
          }
        } else if (state is SignInFailure) {
          UiUtils.showSnackBar(
            context.tr(
              convertErrorCodeToLanguageKey(state.errorMessage),
            )!,
            context,
          );
        }
      },
    );
  }

  Widget _buildRequestOtpContainer() {
    if (isLoading) {
      return const CircularProgressContainer(size: 50);
    }

    return Container(
      width: MediaQuery.of(context).size.width,
      height: 55,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.25),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () async {
            if (phoneNumberController.text.trim().length >= 9) {
              setState(() => isLoading = true);
              await signInWithPhoneNumber(
                phoneNumber: phoneNumberController.text.trim(),
              );
            } else {
              UiUtils.showSnackBar(
                'يرجى إدخال رقم هاتف سعودي صحيح',
                context,
              );
            }
          },
          child: Center(
            child: Text(
              context.tr('requestOtpLbl')!,
              style: GoogleFonts.ibmPlexSansArabic(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeights.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResendText() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ResendOtpTimerContainer(
          key: resendOtpTimerContainerKey,
          enableResendOtpButton: () {
            setState(() {
              enableResendOtpButton = true;
            });
          },
        ),
        TextButton(
          onPressed: enableResendOtpButton
              ? () async {
                  setState(() {
                    isLoading = false;
                    enableResendOtpButton = false;
                    smsCodeController.text = '';
                  });
                  resendOtpTimerContainerKey.currentState?.cancelOtpTimer();
                  await signInWithPhoneNumber(
                    phoneNumber: phoneNumberController.text.trim(),
                  );
                }
              : null,
          child: Text(
            context.tr('resendBtn')!,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Theme.of(context).primaryColor,
              decoration: TextDecoration.underline,
              fontWeight: FontWeights.medium,
            ),
          ),
        ),
      ],
    );
  }
}
